package com.hengjian.extend.event;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 简单用户信息事件
 */
@Data
@NoArgsConstructor
public class SimpleSysUserEvent {

    private Long inUserId;

    private SimpleUser user;

    /**
     * 构造函数 - 用于入参事件请求
     * @param inUserId
     */
    public SimpleSysUserEvent(Long inUserId) {
        this.inUserId = inUserId;
    }

    @Data
    @AllArgsConstructor
    public static class SimpleUser {
        /**
         * 用户账号
         */
        private String userName;

        /**
         * 用户昵称
         */
        private String nickName;

        /**
         * 用户邮箱
         */
        private String email;

        /**
         * 手机号码
         */
        private String phoneNumber;
    }
}
