//package com.hengjian.stream.mq.consumer.blog;
//
//
//import lombok.extern.slf4j.Slf4j;
//import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
//import org.apache.rocketmq.spring.core.RocketMQListener;
//import org.springframework.stereotype.Service;
//
//@Slf4j
//@Service
//// topic 主题要与消息生产者一致
//@RocketMQMessageListener(consumerGroup = "blog-group", topic = "blog_issue")
//public class BlogDelayIssueConsumer implements RocketMQListener<Object> {
//
//    @Override
//    public void onMessage(Object testMessaging) {
//        log.info("blog_issue 消费到消息 ===========>> " + testMessaging.toString());
//        // TODO 博客发布逻辑
//
//    }
//
//}
