package com.hengjian.stream.mq.utils;

import cn.hutool.core.date.DateTime;
import com.hengjian.common.core.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Time;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Stream;

import static java.util.stream.Collectors.toList;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2023/12/18 14:54
 */
public class MathTimeUtils {
    private static final Logger log = LoggerFactory.getLogger(MathTimeUtils.class);
    public final static String DATE_TIME_MINUTE = "yyyy-MM-dd HH:mm:ss";
    public final static String DATE_TIME_MINUTE3 = "yyyyMMddHHmmss";
    public static String LONG_DATE_FORMAT_MINUTE1 = "yyyy-MM-dd HH:mm";
    public static String LONG_DATE_FORMAT_MINUTE2 = "yyyy/MM/dd HH:mm";
    public static String DATE_FORMAT_MINUTE1 = "yyyy-MM-dd";
    public static String DATE_FORMAT_MINUTE2 = "yyyy/MM/dd";
    public static String DATE_FORMAT_MINUTE3 = "yyyy.MM.dd";
    public static String DATE_FORMAT_MINUTE4 = "yyMMdd";
    public static String DATE_FORMAT_MINUTE5 = "yyyyMM";
    public static String DATE_FORMAT_MINUTE6 = "MM";
    public static String DATE_FORMAT_MINUTE7 = "dd";
    public static String DATE_FORMAT_MINUTE8 = "yyyyMMdd";
    public static String HOUR_FORMAT = "HH:mm";
    public static String MONTH_DAY = "MM-dd";

    public static String LONG_DATE_FORMAT_MINUTE = "yyyy-MM-dd HH:mm";
    public static String DATE_FORMAT_MINUTE = "yyyy/MM/dd";
    public static String DATE_FORMAT_TIME = "yyyy年MM月dd日 HH:mm:ss";
    public static String DATE_FORMAT = "yyyy年MM月";

    public static DateTimeFormatter CHINESE_FORMATTER = DateTimeFormatter.ofPattern("yyyy年MM月dd日");

    /**
     * 计算剩余处理时间限制
     *
     * @param createTime 创建时间
     * @return {@link String}
     */
    public static String computeReceiptTimeLimit(LocalDateTime createTime) {
        LocalDateTime now = LocalDateTime.now(TimeZone.getTimeZone("America/Los_Angeles").toZoneId());
        String format = "YYYY-MM-dd hh:mm:ss";
        log.info("---time工具类入参检查:创建时间{},当前时间{}", createTime, now);
        long intervalHoursTime = ChronoUnit.HOURS.between(createTime, now);
        //时差大于48小时,回显时限为 精确到分 需要减掉48时变为超时状态
        if (intervalHoursTime > 48) {
            long intervalSecondsTime = ChronoUnit.SECONDS.between(createTime, now) - 48 * 60 * 60;
            return getTimeOutString(intervalSecondsTime);
        } else {
            long intervalSecondsTime = 48 * 60 * 60 - ChronoUnit.SECONDS.between(createTime, now);
            return getTimeString(intervalSecondsTime);
        }

    }

    /**
     * 获取现在时间时间戳
     *
     * @return {@link Long}
     */
    public static Long getTimestampOfNowTime(){
        LocalDateTime now = LocalDateTime.now(TimeZone.getTimeZone("America/Los_Angeles").toZoneId());
        // 把now 转换为时间戳 并返回
        return now.toEpochSecond(ZoneOffset.of("+8"));
    }

    /**
     * 超时反馈
     * 毫秒需要放开 millisecond注释,入参改为millisecond
     *
     * @param second 秒
     * @return {@link String}
     */
    public static String getTimeOutString(long second) {
//        if (millisecond < 1000) {
//            return "0" + "秒";
//        }
//        long second = millisecond / 1000;
//        long seconds = second % 60;
        long minutes = second / 60;
        long hours = 0;
        if (minutes >= 60) {
            hours = minutes / 60;
            minutes = minutes % 60;
        }
        String timeString = "";
        String minuteString = "";
        String hourString = "";

        if (minutes < 10 && hours < 1) {
            minuteString = minutes + "min";
        } else if (minutes < 10) {
            minuteString = "0" + minutes + "min";
        } else {
            minuteString = minutes + "min";
        }
        if (hours < 10) {
            hourString = hours + "h";
        } else {
            hourString = hours + "" + "h";
        }
        if (hours != 0) {
            timeString = "超时" + hourString + minuteString;
        } else {
            timeString = "超时" + minuteString;
        }
        return timeString;
    }

    public static Date longToDate(Long time) throws ParseException {
        SimpleDateFormat sd = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        //long转Date
        Date date = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(sd.format(new Date(time*1000)));
        return date;
    }

    public static Date getNow() throws ParseException {
        Date date = new Date(); // 返回当前时间戳格式
        SimpleDateFormat dateFormat= new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"); // 改变格式
        System.out.println(dateFormat.format(date)); // 获取当前时间  2023-02-22 21:24:53
        return dateFormat.parse(dateFormat.format(date));
    }

    /**
     * 得到回执时限
     *
     * @return {@link String}
     */
    public static String getTimeString(long second) {

        long minutes = second / 60;
        long hours = 0;
        if (minutes >= 60) {
            hours = minutes / 60;
            minutes = minutes % 60;
        }
        String timeString = "剩余处理时间: ";
        String minuteString = "";
        String hourString = "";

        if (minutes < 10 && hours < 1) {
            minuteString = minutes + "min";
        } else if (minutes < 10) {
            minuteString = "0" + minutes + "min";
        } else {
            minuteString = minutes + "min";
        }
        if (hours < 10) {
            hourString = hours + "h";
        } else {
            hourString = hours + "" + "h";
        }
        if (hours != 0) {
            timeString = timeString+ hourString + minuteString;
        } else {
            timeString = timeString+minuteString;
        }
        return timeString;
    }

    public static String getCreateDateTime() {

        return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
    }

    public static LocalDateTime getZeroTime(LocalDate date) {
        if (date == null) {
            return null;
        }

        return date.atTime(0, 0, 0, 0);
    }

    public static String timeToString(LocalDateTime time, String format) {
        if (time == null) {
            return null;
        }
        DateTimeFormatter df = DateTimeFormatter.ofPattern(format);
        return df.format(time);
    }

    public static LocalDateTime stringToTime(String timeStr, String format) {
        if (StringUtils.isBlank(timeStr)) {
            return null;
        }
        DateTimeFormatter df = DateTimeFormatter.ofPattern(format);
        return LocalDateTime.parse(timeStr, df);
    }

    public static String dateToString(LocalDate date, String format) {
        if (date == null) {
            return null;
        }
        DateTimeFormatter df = DateTimeFormatter.ofPattern(format);
        return df.format(date);
    }

    public static Date localDateTimeToDate(LocalDateTime localDateTime) {
        if (localDateTime == null) {
            return null;
        }
        LocalDate localDate = localDateTime.toLocalDate();
        Date date = Date.from(localDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
        return date;
    }

    public static LocalDate parse(String date) {
        return LocalDate.parse(date, DateTimeFormatter.ISO_LOCAL_DATE);
    }

    public static LocalDate firstDateOfMonth(LocalDate anyDay) {
        return anyDay.withDayOfMonth(1);
    }

    public static LocalDate lastDateOfMonth(LocalDate anyDay) {
        LocalDate date = firstDateOfMonth(anyDay);
        return date.plusMonths(1).minusDays(1);
    }

    public static String shortTimeToString(Time time, String format) {
        if (time == null) {
            return null;
        }
        DateFormat df = new SimpleDateFormat(format);
        return df.format(time);
    }

    public static String timeToChinese(String time) {
        if (time == null) {
            return null;
        }
        LocalDate date = null;
        try {
            date = LocalDate.parse(time, DateTimeFormatter.ISO_LOCAL_DATE);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return timeToChinese(date);
    }

    public static String timeToChinese(LocalDate date) {
        if (date == null) {
            return null;
        }
        return date.format(CHINESE_FORMATTER);
    }

    /**
     * yyyy-MM-dd
     */
    public static String today() {
        return dateToString(LocalDate.now(), DATE_FORMAT_MINUTE1);
    }

    /**
     * 2018-12-13 -> [2018, 12, 13]
     */
    public static String[] split(LocalDate date) {
        return dateToString(date, DATE_FORMAT_MINUTE1).split("-");
    }

    public static Boolean beforeOrEquals(LocalDate date1, LocalDate date2) {
        return date1.isBefore(date2) || date1.equals(date2);
    }

    public static Boolean afterOrEquals(LocalDate date1, LocalDate date2) {
        return date1.isAfter(date2) || date1.equals(date2);
    }


    /**
     * 日期2-日期1=天数
     */
    public static int getDays(Date date1, Date date2) {
        Calendar aCalendar = Calendar.getInstance();
        aCalendar.setTime(date1);
        long begin = aCalendar.getTimeInMillis();
        aCalendar.setTime(date2);
        long end = aCalendar.getTimeInMillis();
        long days = (end - begin) / (1000 * 3600 * 24);
        return (int) days;
    }

    /**
     * LocalDate->Date
     */
    public static Date convertDate(LocalDate localDate) {
        ZoneId zoneId = ZoneId.systemDefault();
        ZonedDateTime zdt = localDate.atStartOfDay(zoneId);
        return Date.from(zdt.toInstant());
    }

    /**
     * LocalDateTime->Date
     */
    public static Date convdateTime(LocalDateTime localDateTime) {
        ZoneId zoneId = ZoneId.systemDefault();
        ZonedDateTime zdt = localDateTime.atZone(zoneId);
        return Date.from(zdt.toInstant());
    }

    /**
     * Date->LocalDateTime
     */
    public static LocalDateTime dateToLocalDateTime(Date date) {
        if (date == null) {
            return null;
        }
        Instant instant = date.toInstant();
        ZoneId zone = ZoneId.systemDefault();
        return LocalDateTime.ofInstant(instant, zone);
    }

    /**
     * Date->LocalDate
     */
    public static LocalDate dateToLocalDate(Date date) {
        Instant instant = date.toInstant();
        ZoneId zone = ZoneId.systemDefault();
        LocalDateTime localDateTime = LocalDateTime.ofInstant(instant, zone);
        return localDateTime.toLocalDate();
    }

    /**
     * 周期
     *
     * @return 2018-12-12/2018-12-30
     */
    public static String period(LocalDate startDate, LocalDate endDate, String format) {
        if (startDate.isEqual(endDate)) {
            return dateToString(startDate, format);
        }
        return MathTimeUtils.dateToString(startDate, format) + "-" + MathTimeUtils.dateToString(endDate, format);
    }

    public static String period(LocalDate startDate, LocalDate endDate, String format, String splitter) {
        if (startDate.isEqual(endDate)) {
            return dateToString(startDate, format);
        }
        return MathTimeUtils.dateToString(startDate, format) + splitter + MathTimeUtils.dateToString(endDate, format);
    }

    public static String period(LocalDateTime startTime, LocalDateTime endTime, String format) {
        if (startTime.isEqual(endTime)) {
            return timeToString(startTime, format);
        }
        return MathTimeUtils.timeToString(startTime, format) + "-" + MathTimeUtils.timeToString(endTime, format);
    }

    public static String period(Time startTime, Time endTime, String format) {
        if (startTime.equals(endTime)) {
            return shortTimeToString(startTime, format);
        }
        return MathTimeUtils.shortTimeToString(startTime, format) + "-" + MathTimeUtils.shortTimeToString(endTime, format);
    }

    /**
     * 两个日期之间的所有日期集合
     */
    public static List<LocalDate> getBetweenDay(LocalDate startDate, LocalDate endDate) {
        long distance = ChronoUnit.DAYS.between(startDate, endDate);
        if (distance < 0L) {
            return null;
        }

        return Stream.iterate(startDate, d -> d.plusDays(1)).limit(distance + 1).collect(toList());
    }

    public static Comparator<LocalDateTime> orderByTime() {
        return (LocalDateTime::compareTo);
    }

    /**
     * 获取某天的最后时间
     */
    public static LocalDateTime getEndTimeOfDay(LocalDate date) {
        return LocalDateTime.of(date.getYear(), date.getMonth(), date.getDayOfMonth(), 23, 59, 59);
    }

    /**
     * 判断两个日期是否同一年
     *
     * @param date1
     * @param date2
     * @return
     */
    public static Boolean isSameYear(Date date1, Date date2) {
        Calendar cal1 = Calendar.getInstance();
        cal1.setTime(date1);
        Calendar cal2 = Calendar.getInstance();
        cal2.setTime(date2);
        return cal1.get(Calendar.YEAR) == cal2.get(Calendar.YEAR);
    }

    /**
     * 获取月份
     */
    public static Integer getMonth(LocalDate date) {
        DateTimeFormatter df = DateTimeFormatter.ofPattern(DATE_FORMAT_MINUTE6);
        return Integer.parseInt(df.format(date));
    }

    /**
     * 获取日
     */
    public static Integer getDay(LocalDate date) {
        DateTimeFormatter df = DateTimeFormatter.ofPattern(DATE_FORMAT_MINUTE7);
        return Integer.parseInt(df.format(date));
    }

    public static Boolean isDateExpire(Date startDate, Date endDate) {
        LocalDate start = MathTimeUtils.dateToLocalDate(startDate);
        LocalDate end = MathTimeUtils.dateToLocalDate(endDate);
        LocalDate now = LocalDate.now();
        return beforeOrEquals(start, now) && afterOrEquals(end, now);
    }


    /**
     * 得到今天00:00时间字符串
     *
     * @return
     */
    public static String getTodayStart() {
        LocalDateTime today_start = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
        DateTimeFormatter time = DateTimeFormatter.ofPattern(DATE_TIME_MINUTE);
        String todayStart = time.format(today_start);
        return todayStart;
    }

    /**
     * 得到今天结束23.59时间字符串
     *
     * @return
     */
    public static String getTodayEnd() {
        LocalDateTime today_end = LocalDateTime.of(LocalDate.now(), LocalTime.MAX);
        DateTimeFormatter time = DateTimeFormatter.ofPattern(DATE_TIME_MINUTE);
        String todayEnd = time.format(today_end);
        return todayEnd;
    }

    public static DateTime dateToDateTime(Date date) {
        DateTime dateTime = new DateTime(date);

        return dateTime;
    }
}
