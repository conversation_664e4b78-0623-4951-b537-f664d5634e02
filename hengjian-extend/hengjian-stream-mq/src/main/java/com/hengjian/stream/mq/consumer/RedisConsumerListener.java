package com.hengjian.stream.mq.consumer;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.hengjian.common.core.utils.SpringUtils;
import com.hengjian.common.redis.utils.QueueUtils;
import com.hengjian.common.websocket.dto.WebSocketMessageDto;
import com.hengjian.common.websocket.utils.WebSocketUtils;
import com.hengjian.extend.utils.SystemEventUtils;
import com.hengjian.stream.mqProducer.constants.MessageTypeEnum;
import com.hengjian.stream.mqProducer.constants.QueueConstants;
import com.hengjian.stream.mqProducer.domain.MessageDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.Ordered;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.List;
import java.util.Map;


/**
 * Redis 消费者
 */
@Slf4j
public class RedisConsumerListener implements ApplicationRunner, Ordered {

    private static final ThreadPoolTaskExecutor threadPoolTaskExecutor = SpringUtils.getBean("threadPoolTaskExecutor", ThreadPoolTaskExecutor.class);

//    @Autowired
//    private EsProductSupport esProductSupport;

    /**
     * 消费者 - 公告发布订单
     */
    public void noticePublishConsumer() {
        log.info("初始化订阅noticePublishConsumer");
        // 项目初始化设置一次即可
        QueueUtils.subscribeBlockingQueue(QueueConstants.QueueName.NOTICE, (MessageDto messageDto) -> {
            // 观察接收时间
            log.info("通道: {}, 收到数据: {}", QueueConstants.QueueName.NOTICE, JSONUtil.toJsonStr(messageDto));
            // 开始发送消息通知
            try {
                String msgId = messageDto.getMsgId();

                Map<String, Object> map = (Map<String, Object>) messageDto.getData();
                JSONObject data = JSONUtil.parseObj(map);
                List<String> tenantIds = data.getBeanList("tenantIds", String.class);
                data.remove("tenantIds");

                List<Long> sysUserIds = SystemEventUtils.getSysUserIdsWithTenantId(tenantIds);

                // String message = JSONUtil.createObj().putOnce("title", title).putOnce("noticeId", msgId).toString();
                String message = JSONUtil.toJsonStr(data);
                threadPoolTaskExecutor.execute(() -> {
                    log.info("noticePublishConsumer => thread id = {}, name = {}", Thread.currentThread().getId(), Thread.currentThread().getName());
                    WebSocketMessageDto webSocketMessage = new WebSocketMessageDto();
                    webSocketMessage.setMessage(message);
                    webSocketMessage.setMessageType(MessageTypeEnum.NOTICE.name());
                    webSocketMessage.setSessionKeys(sysUserIds);
                    webSocketMessage.setMessageId(msgId);
                    WebSocketUtils.publishMessage(webSocketMessage);
                });
            } catch (Exception e) {
                log.error("noticePublishConsumer 公告订阅发布异常：{}", e.getMessage(), e);
            }
        });
    }

    /**
     * 消费者 - ElasticSearch商品数据推送
     */
//    public void esProductPushConsumer() {
//        log.info("初始化订阅esProductPushConsumer");
//        // 项目初始化设置一次即可
//        QueueUtils.subscribeBlockingQueue(QueueConstants.QueueName.ES_PRODUCT_PUSH, (MessageDto messageDto) -> {
//            // 观察接收时间
//            log.info("【消费者 - ElasticSearch商品数据推送】通道: {}, 收到数据: {}", QueueConstants.QueueName.ES_PRODUCT_PUSH, JSONUtil.toJsonStr(messageDto));
//            // 开始推送商品数据
//            try {
//                String msgId = messageDto.getMsgId();
//                Map<String, Object> data = (Map<String, Object>) messageDto.getData();
//                List<String> productCodes = (List<String>) data.get("productCodes");
//                List<String> productSkuCodes = (List<String>) data.get("productSkuCodes");
//
//                threadPoolTaskExecutor.execute(() -> {
//                    if (CollUtil.isNotEmpty(productCodes)) {
//                        esProductSupport.productUploadForConsumer(productCodes);
//                    }
//
//                    if (CollUtil.isNotEmpty(productSkuCodes)) {
//                        esProductSupport.productSkuUploadForConsumer(productSkuCodes);
//                    }
//                });
//            } catch (Exception e) {
//                log.error("【消费者 - ElasticSearch商品数据推送】发生异常：{}", e.getMessage(), e);
//            }
//        });
//    }

    @Override
    public void run(ApplicationArguments args) throws Exception {
        noticePublishConsumer();
        //esProductPushConsumer();
        log.info("RedisConsumer主题订阅监听器成功");
    }

    @Override
    public int getOrder() {
        return -1;
    }
}
