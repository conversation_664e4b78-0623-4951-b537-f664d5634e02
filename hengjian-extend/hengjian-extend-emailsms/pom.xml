<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>hengjian-extend</artifactId>
        <groupId>com.hengjian</groupId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>hengjian-extend-emailsms</artifactId>
    <description>短信/邮箱验证码扩展</description>

    <properties>

    </properties>

    <dependencies>
        <dependency>
            <groupId>com.hengjian</groupId>
            <artifactId>hengjian-common-redis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.hengjian</groupId>
            <artifactId>hengjian-common-sms</artifactId>
        </dependency>

        <dependency>
            <groupId>com.hengjian</groupId>
            <artifactId>hengjian-common-mail</artifactId>
        </dependency>

        <dependency>
            <groupId>com.hengjian</groupId>
            <artifactId>hengjian-event-system</artifactId>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>

        <!-- 开启腾讯sdk短信推送，需单独引入包，否则配置不生效。 -->
<!--        <dependency>
            <groupId>com.tencentcloudapi</groupId>
            <artifactId>tencentcloud-sdk-java-sms</artifactId>
        </dependency>-->

        <!-- 短信第三方引入 -->
        <dependency>
            <groupId>com.vonage</groupId>
            <artifactId>client</artifactId>
        </dependency>

        <!-- 邮箱第三方 引入 -->
<!--        <dependency>-->
<!--            <groupId>com.sendgrid</groupId>-->
<!--            <artifactId>sendgrid-java</artifactId>-->
<!--        </dependency>-->

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-json</artifactId>
        </dependency>
    </dependencies>

</project>
