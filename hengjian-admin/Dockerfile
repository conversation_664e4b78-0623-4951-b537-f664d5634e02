FROM findepi/graalvm:java17-native

MAINTAINER Lion Li

RUN mkdir -p /hengjian/server/logs \
    /hengjian/server/temp \
    /hengjian/skywalking/agent

WORKDIR /hengjian/server

ENV SERVER_PORT=8080

EXPOSE ${SERVER_PORT}

ADD ./target/hengjian-admin.jar ./app.jar

ENTRYPOINT ["java", \
            "-Djava.security.egd=file:/dev/./urandom", \
            "-Dserver.port=${SERVER_PORT}", \
            # 应用名称 如果想区分集群节点监控 改成不同的名称即可
#            "-Dskywalking.agent.service_name=hengjian-server", \
#            "-javaagent:/hengjian/skywalking/agent/skywalking-agent.jar", \
            "-jar", "app.jar"]
