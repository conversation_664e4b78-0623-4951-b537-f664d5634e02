package com.hengjian.web.controller;

import cn.dev33.satoken.annotation.SaIgnore;
import cn.hutool.captcha.AbstractCaptcha;
import cn.hutool.captcha.generator.CodeGenerator;
import cn.hutool.core.lang.Validator;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.hengjian.common.core.constant.Constants;
import com.hengjian.common.core.constant.GlobalConstants;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.enums.VerifyType;
import com.hengjian.common.core.exception.user.UserException;
import com.hengjian.common.core.utils.MessageUtils;
import com.hengjian.common.core.utils.SpringUtils;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.core.utils.reflect.ReflectUtils;
import com.hengjian.common.mail.config.properties.MailProperties;
import com.hengjian.common.redis.utils.RedisUtils;
import com.hengjian.common.sms.config.properties.SmsProperties;
import com.hengjian.common.sms.utils.SmsGatewayClient;
import com.hengjian.common.web.config.properties.CaptchaProperties;
import com.hengjian.common.web.enums.CaptchaType;
import com.hengjian.extend.verifycode.constant.VerifyCodeConstant;
import com.hengjian.extend.verifycode.utils.VerifyCodeUtils;
import com.hengjian.system.service.ISysUserService;
import com.hengjian.web.domain.vo.CaptchaVo;
import com.zsmall.common.enums.statuscode.ZSMallStatusCodeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.sms4j.api.entity.SmsResponse;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotBlank;
import java.time.Duration;

/**
 * 验证码操作处理
 *
 * <AUTHOR> Li
 */
@SaIgnore
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
public class CaptchaController {

    private final CaptchaProperties captchaProperties;
    private final SmsProperties smsProperties;
    private final MailProperties mailProperties;
    private final ISysUserService sysUserService;
    private final VerifyCodeUtils verifyCodeUtils;

    /**
     * 短信验证码
     *
     * @param type        短信类型：register/
     * @param phonenumber 用户手机号
     */
//    @GetMapping("/resource/sms/code/{type}")
//    public R<Void> smsCode(@PathVariable String type, @NotBlank(message = "{user.phonenumber.not.blank}") String phonenumber, String areaCode) {
//        if (!smsProperties.getEnabled()) {
//            return R.fail("当前系统没有开启短信功能！");
//        }
////        String key = GlobalConstants.CAPTCHA_CODE_KEY + phonenumber;
////        String code = RandomUtil.randomNumbers(4);
////        RedisUtils.setCacheObject(key, code, Duration.ofMinutes(Constants.CAPTCHA_EXPIRATION));
////        // 验证码模板id 自行处理 (查数据库或写死均可)
////        String templateId = "";
////        Map<String, String> map = new HashMap<>(1);
////        map.put("code", code);
////        SmsTemplate smsTemplate = SpringUtils.getBean(SmsTemplate.class);
////        SmsResult result = smsTemplate.send(phonenumber, templateId, map);
//
//        SmsResponse result = new SmsResponse();
//        // 判断类型
//        if (StrUtil.equalsIgnoreCase(VerifyCodeConstant.Type.REGISTER, type)) {
//            boolean exist = sysUserService.checkAccountExist(phonenumber, VerifyType.SMS.name());
//            if (exist) {
//                return R.fail(MessageUtils.message("user.phonenumber.exists"));
//            }
//            if (StrUtil.isNotBlank(areaCode)) {
//                result = VerifyCodeUtils.registerSendPhoneNumber(phonenumber, areaCode);
//            } else {
//                result = VerifyCodeUtils.registerSendPhoneNumber(phonenumber);
//            }
//        }
//
//        if (!result.isSuccess()) {
//            log.error("验证码短信发送异常 => {}", result);
//            return R.fail(ZSMallStatusCodeEnum.VERIFY_CODE_SEND_FAIL.getMessage());
//        }
//        return R.ok();
//    }

    @GetMapping("/resource/sms/code/{type}")
    public R<Void> smsCode(@PathVariable String type, @NotBlank(message = "{user.phonenumber.not.blank}") String phonenumber, String areaCode) {
        if (!smsProperties.getEnabled()) {
            return R.fail("当前系统没有开启短信功能！");
        }
//        String key = GlobalConstants.CAPTCHA_CODE_KEY + phonenumber;
//        String code = RandomUtil.randomNumbers(4);
//        RedisUtils.setCacheObject(key, code, Duration.ofMinutes(Constants.CAPTCHA_EXPIRATION));
//        // 验证码模板id 自行处理 (查数据库或写死均可)
//        String templateId = "";
//        Map<String, String> map = new HashMap<>(1);
//        map.put("code", code);
//        SmsTemplate smsTemplate = SpringUtils.getBean(SmsTemplate.class);
//        SmsResult result = smsTemplate.send(phonenumber, templateId, map);

        SmsGatewayClient.SendSmsResponse result = new SmsGatewayClient.SendSmsResponse();
        try{
            // 判断类型
            if (StrUtil.equalsIgnoreCase(VerifyCodeConstant.Type.REGISTER, type)) {
                boolean exist = sysUserService.checkAccountExist(phonenumber, VerifyType.SMS.name());
                if (exist) {
                    return R.fail(MessageUtils.message("user.phonenumber.exists"));
                }
                if (StrUtil.isNotBlank(areaCode)) {
                    result = verifyCodeUtils.registerSendPhoneNumberBySms57(phonenumber, areaCode);
                } else {
                    result = verifyCodeUtils.registerSendPhoneNumberBySms57(phonenumber);
                }
            }
        }catch (Exception e){
            log.error("验证码短信发送异常 => {}", e.getMessage());
            return R.fail(ZSMallStatusCodeEnum.VERIFY_CODE_SEND_FAIL.getMessage());
        }
        if (null == result || !"0".equals(result.getStatus())) {
            log.error("验证码短信发送异常 => {}", result);
            return R.fail(ZSMallStatusCodeEnum.VERIFY_CODE_SEND_FAIL.getMessage());
        }
        return R.ok();
    }

    /**
     * 邮箱验证码
     *
     * @param email 邮箱
     */
    @GetMapping("/resource/email/code/{type}")
    public R<Void> emailCode(@PathVariable String type, @NotBlank(message = "{user.email.not.blank}") String email) {
        if (!mailProperties.getEnabled()) {
            return R.fail("当前系统没有开启邮箱功能！");
        }
//        String key = GlobalConstants.CAPTCHA_CODE_KEY + email;
//        String code = RandomUtil.randomNumbers(4);
//        RedisUtils.setCacheObject(key, code, Duration.ofMinutes(Constants.CAPTCHA_EXPIRATION));
        try {
            // 判断类型
            if (StrUtil.equalsIgnoreCase(VerifyCodeConstant.Type.REGISTER, type)) {
                // 邮箱注册
                if(!Validator.isEmail(email)) {
                    throw new UserException("user.email.not.valid");
                }
                boolean exist = sysUserService.checkAccountExist(email, VerifyType.Email.name());
                if (exist) {
                    return R.fail(MessageUtils.message("user.email.exists"));
                }
                VerifyCodeUtils.registerSendEmail(email);
            }
//            MailUtils.sendText(email, "登录验证码", "您本次验证码为：" + code + "，有效性为" + Constants.CAPTCHA_EXPIRATION + "分钟，请尽快填写。");
        } catch (Exception e) {
            log.error("验证码短信发送异常 => {}", e.getMessage());
            return R.fail(e.getMessage());
        }
        return R.ok();
    }

    /**
     * 生成验证码
     */
    @GetMapping("/code")
    public R<CaptchaVo> getCode() {
        CaptchaVo captchaVo = new CaptchaVo();
        boolean captchaEnabled = captchaProperties.getEnable();
        if (!captchaEnabled) {
            captchaVo.setCaptchaEnabled(false);
            return R.ok(captchaVo);
        }
        // 保存验证码信息
        String uuid = IdUtil.simpleUUID();
        String verifyKey = GlobalConstants.CAPTCHA_CODE_KEY + uuid;
        // 生成验证码
        CaptchaType captchaType = captchaProperties.getType();
        boolean isMath = CaptchaType.MATH == captchaType;
        Integer length = isMath ? captchaProperties.getNumberLength() : captchaProperties.getCharLength();
        CodeGenerator codeGenerator = ReflectUtils.newInstance(captchaType.getClazz(), length);
        AbstractCaptcha captcha = SpringUtils.getBean(captchaProperties.getCategory().getClazz());
        captcha.setGenerator(codeGenerator);
        captcha.createCode();
        String code = captcha.getCode();
        if (isMath) {
            ExpressionParser parser = new SpelExpressionParser();
            Expression exp = parser.parseExpression(StringUtils.remove(code, "="));
            code = exp.getValue(String.class);
        }
        RedisUtils.setCacheObject(verifyKey, code, Duration.ofMinutes(Constants.CAPTCHA_EXPIRATION));
        captchaVo.setUuid(uuid);
        captchaVo.setImg(captcha.getImageBase64());
        return R.ok(captchaVo);
    }

}
