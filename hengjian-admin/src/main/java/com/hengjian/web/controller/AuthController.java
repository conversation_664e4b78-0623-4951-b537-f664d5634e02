package com.hengjian.web.controller;

import cn.dev33.satoken.annotation.SaIgnore;
import cn.dev33.satoken.secure.BCrypt;
import cn.dev33.satoken.stp.SaTokenInfo;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Validator;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.hengjian.common.core.constant.GlobalConstants;
import com.hengjian.common.core.constant.TenantConstants;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.domain.model.LoginBodyV2;
import com.hengjian.common.core.domain.model.LoginBodyV3;
import com.hengjian.common.core.domain.model.RegisterBody;
import com.hengjian.common.core.domain.model.VerifyCodeBody;
import com.hengjian.common.core.enums.VerifyType;
import com.hengjian.common.core.exception.user.CaptchaExpireException;
import com.hengjian.common.core.exception.user.UserException;
import com.hengjian.common.core.utils.*;
import com.hengjian.common.redis.utils.RedisUtils;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.hengjian.extend.verifycode.constant.VerifyCodeConstant;
import com.hengjian.extend.verifycode.utils.VerifyCodeUtils;
import com.hengjian.system.domain.bo.SysTenantBo;
import com.hengjian.system.domain.bo.SysUserBo;
import com.hengjian.system.domain.vo.SysTenantVo;
import com.hengjian.system.domain.vo.SysUserVo;
import com.hengjian.system.service.ISysConfigService;
import com.hengjian.system.service.ISysTenantService;
import com.hengjian.system.service.ISysUserService;
import com.hengjian.web.domain.vo.LoginTenantVo;
import com.hengjian.web.domain.vo.LoginVo;
import com.hengjian.web.domain.vo.TenantListVo;
import com.hengjian.web.service.SysLoginService;
import com.hengjian.web.service.SysRegisterService;
import com.zsmall.common.enums.statuscode.ZSMallStatusCodeEnum;
import com.zsmall.system.biz.service.TenantSalesChannelService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotBlank;
import java.net.URL;
import java.util.List;

/**
 * 认证
 *
 * <AUTHOR> Li
 */
@Slf4j
@SaIgnore
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/auth")
public class AuthController {

    private final SysLoginService loginService;
    private final SysRegisterService registerService;
    private final ISysConfigService configService;
    private final ISysTenantService tenantService;
    private final ISysUserService userService;
    private final VerifyCodeUtils verifyCodeUtils;

    /**
     * 登录方法
     *
     * @param body 登录信息
     * @return 结果
     */
    @PostMapping("/login")
    public R<LoginVo> login(@Validated @RequestBody LoginBodyV2 body) {
        LoginVo loginVo = new LoginVo();
        // 生成令牌
        String token = loginService.login(
                body.getUsername(), body.getPassword(),
                body.getCode(), body.getUuid());
        loginVo.setToken(token);
        // 额外处理步骤
        loginExtracted(body);
        return R.ok(loginVo);
    }


    /**
     * 忘记密码 - 发送验证邮件
     */
    @GetMapping(value = "/sendVerifyEmail")
    public R<Void> sendVerifyEmail(VerifyCodeBody bo) throws Exception {
        String verifyType = bo.getVerifyType();
        String verifyAccount = bo.getVerifyAccount();
        if (StrUtil.hasBlank(verifyType, verifyAccount) && StrUtil.equalsAny(verifyType, VerifyType.SMS.name(), VerifyType.Email.name())) {
            return R.fail(ZSMallStatusCodeEnum.REQUIRED_CANNOT_EMPTY);
        }
        SysUserVo sysUserVo = null;
        if (VerifyType.SMS.name().equals(verifyType)) {
            sysUserVo = TenantHelper.ignore(() -> loginService.loadUserByPhonenumber(verifyAccount));
        }
        if (VerifyType.Email.name().equals(verifyType)) {
            // 邮箱注册
            if(!Validator.isEmail(verifyAccount)) {
                throw new UserException("user.email.not.valid");
            }
            sysUserVo = TenantHelper.ignore(() -> loginService.loadUserByEmail(verifyAccount));
        }
        //发送验证码
        if (VerifyType.SMS.name().equals(verifyType)) {
            verifyCodeUtils.forgetPwdSendPhoneNumberBySms57(verifyAccount, sysUserVo.getAreaCode());
        }
        if (VerifyType.Email.name().equals(verifyType)) {
            String language = sysUserVo.getLanguage();
            language = StrUtil.isNotBlank(language) ? language : ServletUtils.getHeaderLanguage();
            VerifyCodeUtils.fogetPwdSendEmail(verifyAccount, language);
        }
        return R.ok();
    }

    /**
     * 校验验证码
     * @return
     */
    @PostMapping("{type}/verificationCode")
    public R<Void> verificationCode(@RequestBody VerifyCodeBody bo, @PathVariable String type) {
        String code = bo.getCode();
        String verifyAccount = bo.getVerifyAccount();
        String verifyType = bo.getVerifyType();
//        if (VerifyType.SMS.name().equals(verifyType)) {
//            TenantHelper.ignore(() -> loginService.loadUserByPhonenumber(verifyAccount));
//        }
//        if (VerifyType.Email.name().equals(verifyType)) {
//            TenantHelper.ignore(() -> loginService.loadUserByEmail(verifyAccount));
//        }
        if (StrUtil.equalsIgnoreCase(VerifyCodeConstant.Type.FORGET_PASSWORD, type)) {
            type = VerifyCodeConstant.MID_FORGET_PASSWORD_KEY;
        }
//        if (StrUtil.equalsIgnoreCase(VerifyCodeConstant.Type.LOGIN, type)) {
//            type = VerifyCodeConstant.;
//        }
        if (StrUtil.equalsIgnoreCase(VerifyCodeConstant.Type.PAYMENT_PASSWORD, type)) {
            type = VerifyCodeConstant.MID_PAYMENT_KEY;
        }
        if (StrUtil.equalsIgnoreCase(VerifyCodeConstant.Type.REGISTER, type)) {
            type = VerifyCodeConstant.MID_REGISTER_KEY;
        }
        //对比缓存中的验证码
        String key = GlobalConstants.CAPTCHA_CODE_KEY + type + verifyAccount;
        String cacheCode = RedisUtils.getCacheObject(key);
        if (StrUtil.isBlank(cacheCode)) {
            throw new CaptchaExpireException();
        }
        if (!cacheCode.equals(code)) {
            return R.fail(ZSMallStatusCodeEnum.VERIFY_CODE_IS_NOT_MATCH);
        }
        return R.ok();
    }

    /**
     * 忘记密码 - 修改密码
     */
    @PostMapping(value = "/updateUserPassword")
    public R<Void> updateUserPassword(@RequestBody VerifyCodeBody bo) {
        String code = bo.getCode();
        String password = bo.getPassword();
        String verifyAccount = bo.getVerifyAccount();
        String verifyType = bo.getVerifyType();
        if (StrUtil.hasBlank(code, password, verifyAccount, verifyType) && StrUtil.equalsAny(verifyType, VerifyType.SMS.name(), VerifyType.Email.name())) {
            return R.fail(ZSMallStatusCodeEnum.REQUIRED_CANNOT_EMPTY);
        }
        //对比缓存中的验证码
        String key = GlobalConstants.CAPTCHA_CODE_KEY + VerifyCodeConstant.MID_FORGET_PASSWORD_KEY + verifyAccount;
        try {
            String cacheCode = RedisUtils.getCacheObject(key);
            if (StringUtils.isBlank(cacheCode)) {
                throw new CaptchaExpireException();
            }
            if (!cacheCode.equals(code)) {
                return R.fail(ZSMallStatusCodeEnum.VERIFY_CODE_IS_NOT_MATCH);
            }
            // 校验密码强弱
            PwdCheckUtils.builder(password).containLength(8, 20).containCase().containDigit().start();
            SysUserVo sysUserVo = null;
            if (VerifyType.SMS.name().equals(verifyType)) {
                sysUserVo = TenantHelper.ignore(() -> loginService.loadUserByPhonenumber(verifyAccount));
            }
            if (VerifyType.Email.name().equals(verifyType)) {
                sysUserVo = TenantHelper.ignore(() -> loginService.loadUserByEmail(verifyAccount));
            }
            String sha1Pwd = SecureUtil.sha1(password);
            SysUserBo userBo = new SysUserBo();
            userBo.setPassword(BCrypt.hashpw(sha1Pwd));
            userBo.setUserId(sysUserVo.getUserId());
            userService.updateUserPassword(userBo);
        } finally {
            //修改密码后删除验证码缓存，防止再次校验
            RedisUtils.deleteObject(key);
        }

        return R.ok();
    }


    /**
     * 判断是否登录
     *
     * @return 结果
     */
    @PostMapping("/isLogin")
    public R<String> isLogin() {
        // 由于此类增加了全局忽略认证，则需要手动调用认证登录方法。
        StpUtil.checkLogin();
        SaTokenInfo tokenInfo = StpUtil.getTokenInfo();
        String tokenValue = tokenInfo.getTokenValue();
        return R.ok("success", tokenValue);
    }

    /**
     * 登录额外处理
     * @param body
     */
    private static void loginExtracted(LoginBodyV2 body) {
        LoginBodyV2.ChannelExtra channelExtra = body.getChannelExtra();
        // 判断渠道配置不为空，且渠道类型和accesstoken都不为空
        try {
            if(channelExtra != null &&
                StrUtil.isAllNotBlank(channelExtra.getChannelType(), channelExtra.getAccess())) {
                // 后续看绑定速度快慢，来决定是否通过异步事件来绑定渠道
                TenantSalesChannelService tenantSalesChannelService = SpringUtils.getBean(TenantSalesChannelService.class);
                tenantSalesChannelService.bindChannel(channelExtra.getChannelType(), channelExtra.getAccess());
            }
        } catch (Exception e) {
            log.error("channelExtra bind error. message = {}", e.getMessage(), e);
        }
    }

    /**
     * 登录方法 - token登录
     *
     * @param body 登录信息
     * @return 结果
     */
    @PostMapping("/tokenLogin")
    public R<LoginVo> login(@Validated @RequestBody LoginBodyV3 body) {
        LoginVo loginVo = new LoginVo();
        // 生成令牌
        String token = loginService.tokenReLogin(body.getToken());
        loginVo.setToken(token);
        return R.ok(loginVo);
    }


//    /**
//     * 登录方法
//     *
//     * @param body 登录信息
//     * @return 结果
//     */
//    @PostMapping("/login")
//    public R<LoginVo> login(@Validated @RequestBody LoginBody body) {
//        LoginVo loginVo = new LoginVo();
//        // 生成令牌
//        String token = loginService.login(
//                body.getTenantId(),
//                body.getUsername(), body.getPassword(),
//                body.getCode(), body.getUuid());
//        loginVo.setToken(token);
//        return R.ok(loginVo);
//    }
//
//    /**
//     * 短信登录
//     *
//     * @param body 登录信息
//     * @return 结果
//     */
//    @PostMapping("/smsLogin")
//    public R<LoginVo> smsLogin(@Validated @RequestBody SmsLoginBody body) {
//        LoginVo loginVo = new LoginVo();
//        // 生成令牌
//        String token = loginService.smsLogin(body.getTenantId(), body.getPhonenumber(), body.getSmsCode());
//        loginVo.setToken(token);
//        return R.ok(loginVo);
//    }
//
//    /**
//     * 邮件登录
//     *
//     * @param body 登录信息
//     * @return 结果
//     */
//    @PostMapping("/emailLogin")
//    public R<LoginVo> emailLogin(@Validated @RequestBody EmailLoginBody body) {
//        LoginVo loginVo = new LoginVo();
//        // 生成令牌
//        String token = loginService.emailLogin(body.getTenantId(), body.getEmail(), body.getEmailCode());
//        loginVo.setToken(token);
//        return R.ok(loginVo);
//    }

    /**
     * 小程序登录(示例)
     *
     * @param xcxCode 小程序code
     * @return 结果
     */
    @PostMapping("/xcxLogin")
    public R<LoginVo> xcxLogin(@NotBlank(message = "{xcx.code.not.blank}") String xcxCode) {
        LoginVo loginVo = new LoginVo();
        // 生成令牌
        String token = loginService.xcxLogin(xcxCode);
        loginVo.setToken(token);
        return R.ok(loginVo);
    }

    /**
     * 退出登录
     */
    @PostMapping("/logout")
    public R<Void> logout() {
        loginService.logout();
        return R.ok("退出成功");
    }

    /**
     * 用户注册
     */
    @PostMapping("/register")
    public R<Void> register(@Validated @RequestBody RegisterBody user) {
        // 修复为根据默认租户下是否开启注册功能
        if (!configService.selectRegisterEnabled(TenantConstants.DEFAULT_TENANT_ID)) {
            return R.fail("当前系统没有开启注册功能！");
        }
        registerService.register(user);
        return R.ok();
    }

    /**
     * 登录页面租户下拉框
     *
     * @return 租户列表
     */
    @GetMapping("/tenant/list")
    public R<LoginTenantVo> tenantList(HttpServletRequest request) throws Exception {
        List<SysTenantVo> tenantList = tenantService.queryList(new SysTenantBo());
        List<TenantListVo> voList = MapstructUtils.convert(tenantList, TenantListVo.class);
        // 获取域名
        String host = new URL(request.getRequestURL().toString()).getHost();
        // 根据域名进行筛选
        List<TenantListVo> list = StreamUtils.filter(voList, vo -> StringUtils.equals(vo.getDomain(), host));
        // 返回对象
        LoginTenantVo vo = new LoginTenantVo();
        vo.setVoList(CollUtil.isNotEmpty(list) ? list : voList);
        vo.setTenantEnabled(TenantHelper.isEnable());
        return R.ok(vo);
    }

}
