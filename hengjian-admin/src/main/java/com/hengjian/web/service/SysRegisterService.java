package com.hengjian.web.service;

import cn.dev33.satoken.secure.BCrypt;
import cn.hutool.core.lang.Validator;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hengjian.common.core.constant.Constants;
import com.hengjian.common.core.constant.GlobalConstants;
import com.hengjian.common.core.domain.model.RegisterBody;
import com.hengjian.common.core.enums.UserType;
import com.hengjian.common.core.exception.user.CaptchaException;
import com.hengjian.common.core.exception.user.CaptchaExpireException;
import com.hengjian.common.core.exception.user.UserException;
import com.hengjian.common.core.utils.*;
import com.hengjian.common.log.event.LogininforEvent;
import com.hengjian.common.redis.utils.RedisUtils;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.hengjian.extend.verifycode.utils.VerifyCodeUtils;
import com.hengjian.system.domain.SysUser;
import com.hengjian.system.domain.bo.SysUserBo;
import com.hengjian.system.domain.vo.SysUserVo;
import com.hengjian.system.enums.SysConstantEnum;
import com.hengjian.system.mapper.SysUserMapper;
import com.hengjian.system.service.ISysUserService;
import com.hengjian.system.service.impl.SysCodeGenerator;
import com.zsmall.common.enums.marketplaceConfig.ModuleTypeEnum;
import com.zsmall.product.entity.domain.ProductSku;
import com.zsmall.product.entity.iservice.IProductSkuService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 注册校验方法
 *
 * <AUTHOR> Li
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class SysRegisterService {

    private final ISysUserService userService;
    private final SysCodeGenerator sysCodeGenerator;
    private final IProductSkuService iProductSkuService;
    private final SysUserMapper userMapper;
    /**
     * 注册
     */
    public void register(RegisterBody registerBody) {
        // 国际化
        String language = ServletUtils.getHeaderLanguage();
//        String tenantId = registerBody.getTenantId();
        String username = registerBody.getUsername();
        String accountName = registerBody.getAccountName();
        String password = registerBody.getPassword();
        String verifyCode = registerBody.getVerifyCode();
        String email = registerBody.getEmail();
        String phoneNumber = registerBody.getPhoneNumber();
        String signType = registerBody.getSignType();
        String identityType = registerBody.getIdentityType();
        String areaCode = registerBody.getAreaCode();
        String productSourceType = registerBody.getProductSourceType();
        String country = registerBody.getCountry();
//        String country = registerBody.getCountry();

        // 校验密码强弱
        PwdCheckUtils.builder(password).containLength(8, 20).containCase().containDigit().start();

        SysUserBo sysUser;
        if(StrUtil.equalsIgnoreCase(signType, "SMS")) {
            // 手机号码注册
            if (StrUtil.length(phoneNumber) != 10 && StrUtil.length(phoneNumber) != 11) {
                throw new UserException("user.mobile.phone.number.not.valid");
            }
            // 检验验证码
            VerifyCodeUtils.registerValidate(phoneNumber, verifyCode);
            // 判断如果是不存在用户名，则随机生成
            if(StrUtil.isBlank(username)) {
                username = sysCodeGenerator.codeGenerate(SysConstantEnum.KEY_RANDOM_USERNAME);
            }
            if(StrUtil.isBlank(accountName)) {
                accountName = phoneNumber;
            }
            sysUser = new SysUserBo();
            sysUser.setPhonenumber(phoneNumber);
            sysUser.setNickName(accountName);
            sysUser.setAreaCode(areaCode);
            sysUser.setUserName(username);
            if (!userService.checkPhoneUnique(sysUser)) {
                throw new UserException("user.register.save.error", phoneNumber);
            }
            // 自检username是否重复，避免异常
            if (!userService.checkUserNameUnique(sysUser)) {
                throw new UserException("user.register.save.error", accountName);
            }
        } else if(StrUtil.equalsIgnoreCase(signType, "Email")) {
            // 邮箱注册
            if(!Validator.isEmail(email)) {
                throw new UserException("user.email.not.valid");
            }
            // 检验验证码
            VerifyCodeUtils.registerValidate(email, verifyCode);
            // 判断如果是不存在用户名，则随机生成
            if(StrUtil.isBlank(username)) {
                username = sysCodeGenerator.codeGenerate(SysConstantEnum.KEY_RANDOM_USERNAME);
            }
            if(StrUtil.isBlank(accountName)) {
                accountName = email;
            }
            sysUser = new SysUserBo();
            sysUser.setEmail(email);
            sysUser.setNickName(accountName);
            sysUser.setUserName(username);
            if (!userService.checkEmailUnique(sysUser)) {
                throw new UserException("user.register.save.error", email);
            }
            // 自检username是否重复，避免异常
            if (!userService.checkUserNameUnique(sysUser)) {
                throw new UserException("user.register.save.error", accountName);
            }
        } else {
            throw new UserException("user.register.error");
//            // 账号密码注册
//            sysUser = new SysUserBo();
//            sysUser.setUserName(username);
//            sysUser.setNickName(accountName);
//
//            // TODO 暂时未处理账号注册逻辑
//
//            if (!userService.checkUserNameUnique(sysUser)) {
//                throw new UserException("user.register.save.error", accountName);
//            }
        }

        // 校验用户类型是否存在
        String userType = UserType.getUserType(registerBody.getUserType()).getUserType();

//        boolean captchaEnabled = captchaProperties.getEnable();
//        // 验证码开关
//        if (captchaEnabled) {
//            validateCaptcha(TenantConstants.DEFAULT_TENANT_ID, username, registerBody.getCode(), registerBody.getUuid());
//        }
//        SysUserBo sysUser = new SysUserBo();

        String sha1Pwd = SecureUtil.sha1(password);
        sysUser.setPassword(BCrypt.hashpw(sha1Pwd));
        sysUser.setUserType(userType);
        sysUser.setIdentityType(identityType);
        sysUser.setLanguage(language);
        sysUser.setProductSourceType(productSourceType);
        sysUser.setCountry(country);

        try {
            userService.registerUserWithNoTenant(sysUser);
        } catch (Exception e) {
            log.error("user.register.error. message: {}", e.getMessage(), e);
            throw new UserException("user.register.error");
        }

        recordLogininfor(sysUser.getTenantId(), accountName, Constants.REGISTER, MessageUtils.message("user.register.success"));
        //查询上面插入的tenantId
        LambdaQueryWrapper<SysUser> wr = new LambdaQueryWrapper<>();
        wr.eq(SysUser::getUserName,username);
        SysUserVo sysUserVo = userMapper.selectVoOne(wr);
        LambdaQueryWrapper<ProductSku> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.orderByDesc(ProductSku::getId);
        queryWrapper.eq(ProductSku::getShelfState,"OnShelf");
        queryWrapper.last("limit 10" );
        List<ProductSku> additionalProductSkus = TenantHelper.ignore(() -> iProductSkuService.getBaseMapper().selectList(queryWrapper));
        Set<String> productSkuCodes = additionalProductSkus.stream()
                                                           .map(ProductSku::getProductSkuCode)
                                                           .collect(Collectors.toSet());
        RedisUtils.deleteKeys(GlobalConstants.GLOBAL_REDIS_KEY+ ModuleTypeEnum.GuessYouLike+sysUserVo.getTenantId());
        RedisUtils.setCacheList(GlobalConstants.GLOBAL_REDIS_KEY+ModuleTypeEnum.GuessYouLike+sysUserVo.getTenantId(),new ArrayList<>(productSkuCodes));
    }

    /**
     * 校验验证码
     *
     * @param username 用户名
     * @param code     验证码
     * @param uuid     唯一标识
     */
    public void validateCaptcha(String tenantId, String username, String code, String uuid) {
        String verifyKey = GlobalConstants.CAPTCHA_CODE_KEY + StringUtils.defaultString(uuid, "");
        String captcha = RedisUtils.getCacheObject(verifyKey);
        RedisUtils.deleteObject(verifyKey);
        if (captcha == null) {
            recordLogininfor(tenantId, username, Constants.REGISTER, MessageUtils.message("user.jcaptcha.expire"));
            throw new CaptchaExpireException();
        }
        if (!code.equalsIgnoreCase(captcha)) {
            recordLogininfor(tenantId, username, Constants.REGISTER, MessageUtils.message("user.jcaptcha.error"));
            throw new CaptchaException();
        }
    }

    /**
     * 记录登录信息
     *
     * @param tenantId 租户ID
     * @param username 用户名
     * @param status   状态
     * @param message  消息内容
     * @return
     */
    private void recordLogininfor(String tenantId, String username, String status, String message) {
        LogininforEvent logininforEvent = new LogininforEvent();
        logininforEvent.setTenantId(tenantId);
        logininforEvent.setUsername(username);
        logininforEvent.setStatus(status);
        logininforEvent.setMessage(message);
        logininforEvent.setRequest(ServletUtils.getRequest());
        SpringUtils.context().publishEvent(logininforEvent);
    }

}
