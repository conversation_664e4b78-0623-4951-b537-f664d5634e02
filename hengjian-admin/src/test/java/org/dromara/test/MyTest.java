package com.hengjian.test;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.net.url.UrlBuilder;
import cn.hutool.core.text.StrJoiner;
import cn.hutool.core.util.XmlUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.hengjian.common.core.constant.GlobalConstants;
import com.hengjian.common.redis.utils.RedisUtils;
import com.zsmall.extend.shop.rakuten.model.RakutenResult;
import com.zsmall.product.entity.domain.ProductWholesaleDetail;
import com.zsmall.product.entity.domain.vo.product.ProductVo;
import org.junit.jupiter.api.Test;
import org.redisson.api.RIdGenerator;
import org.redisson.api.RedissonClient;
import org.w3c.dom.Document;
import org.w3c.dom.Element;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.Base64;
import java.util.Locale;
import java.util.Optional;
import java.util.concurrent.ThreadLocalRandom;

public class MyTest {

    @Test
    public void renameFile() {
        System.out.println(Locale.SIMPLIFIED_CHINESE.toString());
    }

    @Test
    public void randomNum() {
        for (int i = 0; i < 2; i++) {
            StringBuilder builder = new StringBuilder();
            builder.append(ThreadLocalRandom.current().nextInt(10));
            builder.append(ThreadLocalRandom.current().nextInt(10));
            builder.append(ThreadLocalRandom.current().nextInt(10));
            System.out.println(builder);
        }
    }

    @Test
    public void testList() {
        String data = FileUtil.readString("D:\\杂物\\地区.json", StandardCharsets.UTF_8);
        JSONObject jsonObject = JSONUtil.parseObj(data);

        jsonObject.forEach((key, value) -> {

        });
    }

    @Test
    public void testPwd() {
        ProductVo productVo = new ProductVo();
        ProductWholesaleDetail productWholesaleDetail = new ProductWholesaleDetail();
        // productWholesaleDetail.setProductId(111L);
        productVo.setProductWholesaleDetail(productWholesaleDetail);

        Long orderAddress = Optional.ofNullable(productVo)
                                            .flatMap(resp -> Optional.ofNullable(resp.getProductWholesaleDetail()))
                                            .map(ProductWholesaleDetail::getProductId)
                                            .orElse(null);
        System.out.println(orderAddress);
    }

    @Test
    public void base64Test() throws InterruptedException {
        String text = "SP414318_uKlmDwQFlUm0VyQi:SL414318_m4m7tNedEppwcKIY";
        String encodeToString = Base64.getEncoder().encodeToString(text.getBytes());
        System.out.println(encodeToString);

        // String jsonStr = "{\"zh_CN\":\"以下商品 (ItemNo. {itemNo}) 在当前订单的销售渠道上已达到销售极限, 请联系客服申请销售许可 - <EMAIL>\",\"en_US\":\"The following products (ItemNo. {itemNo}) has reached its sales limit on the current order's sales channel, please contact customer service to apply for sales - <EMAIL>\"}";
        // LocaleMessage localeMessage = LocaleMessage.parseByJSONStr(jsonStr);
        // System.out.println(localeMessage.toJsonPrettyStr());
    }

    private final static String PREFIX_INCREASE = GlobalConstants.GLOBAL_REDIS_KEY + "RAKUTEN:PRODUCT_ID:";

    private String generate(String productCode) {
        StrJoiner manageNoJoiner = new StrJoiner("-");
        DateTime now = DateTime.now();
        manageNoJoiner.append(productCode);
        manageNoJoiner.append(DateUtil.format(now, "YYMMddHHmmss"));

        RedissonClient client = RedisUtils.getClient();
        RIdGenerator idGenerator = client.getIdGenerator(PREFIX_INCREASE);
        boolean b = idGenerator.tryInit(1, 999);
        // 初始化，第一次创建，则设置过期时间
        if (b) {
            // 15秒超时，稍微延后
            idGenerator.expire(Duration.ofSeconds(90));
        }

        long id = idGenerator.nextId();
        if (id == 9L) {
            // 删除自增，下次再重新初始化
            idGenerator.delete();
        }
        String strNum = String.format("%03d", id);
        manageNoJoiner.append(strNum);

        return manageNoJoiner.toString();
    }

    @Test
    public void testXml() throws ParserConfigurationException {
        DocumentBuilderFactory docFactory = XmlUtil.createDocumentBuilderFactory();
        DocumentBuilder docBuilder = docFactory.newDocumentBuilder();

        // 创建一个新的Document对象
        Document doc = docBuilder.newDocument();

        // 创建根元素 <request>
        Element requestElement = doc.createElement("request");
        doc.appendChild(requestElement);

        // 创建 <fileInsertRequest> 元素
        Element fileInsertRequestElement = doc.createElement("fileInsertRequest");
        requestElement.appendChild(fileInsertRequestElement);

        // 创建 <file> 元素
        Element fileElement = doc.createElement("file");
        fileInsertRequestElement.appendChild(fileElement);

        // 创建 <fileName> 元素并设置文本内容
        Element fileNameElement = doc.createElement("fileName");
        fileNameElement.appendChild(doc.createTextNode("HJ844651"));
        fileElement.appendChild(fileNameElement);

        // 创建 <folderId> 元素并设置文本内容
        Element folderIdElement = doc.createElement("folderId");
        folderIdElement.appendChild(doc.createTextNode("0"));
        fileElement.appendChild(folderIdElement);

        // 创建 <filePath> 元素并设置文本内容
        Element filePathElement = doc.createElement("filePath");
        filePathElement.appendChild(doc.createTextNode("hj844651.jpg"));
        fileElement.appendChild(filePathElement);

        // 创建 <overWrite> 元素并设置文本内容
        Element overWriteElement = doc.createElement("overWrite");
        overWriteElement.appendChild(doc.createTextNode("false"));
        fileElement.appendChild(overWriteElement);

        System.out.println(XmlUtil.toStr(doc));


        // String body = "<result><status><interfaceId>cabinet.file.insert</interfaceId><systemStatus>OK</systemStatus><message>OK</message><requestId>714a4983-555f-42d9-aeea-89dae89f2f45</requestId><requests /> </status><cabinetFileInsertResult><resultCode>0</resultCode><FileId>0</FileId></cabinetFileInsertResult></result>";
        // Document document = XmlUtil.parseXml(body);
        // System.out.println(XmlUtil.toStr(document));
        //
        // OutCabinetFileInsert outCabinetFileInsert = XmlUtil.xmlToBean(document, OutCabinetFileInsert.class);
        // System.out.println(JSONUtil.toJsonStr(outCabinetFileInsert));

        byte[] bytes = FileUtil.readBytes("F:\\Download\\asdasdasd.jpeg");

        HttpResponse execute = HttpRequest.post("https://api.rms.rakuten.co.jp/es/1.0/cabinet/file/insert")
                                          .header("Authorization", "ESA U1AzOTg4NjdfZUNVR2QyQXREdDJ2RXRTQzpTTDM5ODg2N19rVjRUT0pxN0JNSTNxbDBs")
                                          .header("Content-Type", "multipart/form-data")
                                          .form("file", bytes, "hj844651.jpg")
                                          .form("xml", XmlUtil.toStr(doc))
                                          .execute();

        System.out.println(execute);

    }

    @Test
    public void testRakutenGenre() {
        String text = "SP414318_uKlmDwQFlUm0VyQi:SL414318_KhvlUrZye303EfaX";
        String encodeToString = Base64.getEncoder().encodeToString(text.getBytes());

        UrlBuilder url = UrlBuilder.of("https://api.rms.rakuten.co.jp/es/2.0/navigation/genres/110729")
                                   .addQuery("showChildren", true);

        HttpRequest httpRequest = HttpRequest.get(url.build())
                                             .header("Authorization", "ESA " + encodeToString);
        System.out.println(httpRequest);
        HttpResponse execute = httpRequest.execute();

        String body = execute.body();
        System.out.println(JSONUtil.toJsonPrettyStr(body));
    }

    @Test
    public void testEntity() {
        String body = "<result><status><interfaceId>cabinet.file.insert</interfaceId><systemStatus>OK</systemStatus><message>OK</message><requestId>100f8595-9416-4389-96e8-c889633a9904</requestId><requests/></status><cabinetFileInsertResult><resultCode>0</resultCode><FileId>89704379</FileId></cabinetFileInsertResult></result>";
        Document document = XmlUtil.parseXml(body);

        RakutenResult rakutenResult = XmlUtil.xmlToBean(document, RakutenResult.class);
        System.out.println(JSONUtil.toJsonStr(rakutenResult));
    }

}
