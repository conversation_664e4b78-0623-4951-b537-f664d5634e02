package com.hengjian.test;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.Console;
import cn.hutool.core.text.StrJoiner;
import cn.hutool.core.util.ReUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hengjian.common.core.constant.GlobalConstants;
import com.hengjian.common.redis.utils.RedisUtils;
import com.hengjian.system.domain.bo.SysTenantBo;
import com.hengjian.system.domain.vo.SysTenantVo;
import com.hengjian.system.service.ISysTenantService;
import com.zsmall.activity.entity.iservice.IProductActivityPriceItemService;
import com.zsmall.common.enums.ExcelMessageEnum;
import com.zsmall.common.enums.common.AttachmentTypeEnum;
import com.zsmall.common.enums.downloadRecord.DownloadTypePlusEnum;
import com.zsmall.common.enums.product.AttributeTypeEnum;
import com.zsmall.common.util.DecimalUtil;
import com.zsmall.common.util.ExcelMsgBuilder;
import com.zsmall.common.util.ZExcelUtil;
import com.zsmall.extend.shop.rakuten.constant.OrderConstant;
import com.zsmall.extend.shop.rakuten.constant.ProductConstant;
import com.zsmall.extend.shop.rakuten.enums.product.ImageTypeEnum;
import com.zsmall.extend.shop.rakuten.enums.product.ItemTypeEmun;
import com.zsmall.extend.shop.rakuten.kit.RakutenDelegate;
import com.zsmall.extend.shop.rakuten.kit.RakutenKit;
import com.zsmall.extend.shop.rakuten.model.order.InSearchOrder;
import com.zsmall.extend.shop.rakuten.model.order.OutSearchOrder;
import com.zsmall.extend.shop.rakuten.model.product.InProductUpsert;
import com.zsmall.order.entity.iservice.IOrderItemPriceService;
import com.zsmall.product.entity.domain.*;
import com.zsmall.product.entity.domain.bo.product.ProductQueryBo;
import com.zsmall.product.entity.domain.dto.stock.StockUploadDTO;
import com.zsmall.product.entity.domain.vo.ProductSkuAttachmentVo;
import com.zsmall.product.entity.iservice.*;
import com.zsmall.system.entity.domain.bo.worldLocation.WorldLocationBo;
import com.zsmall.system.entity.iservice.IWorldLocationService;
import com.zsmall.system.entity.util.DownloadRecordUtil;
import org.junit.jupiter.api.Test;
import org.redisson.api.RIdGenerator;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/7/21
 */
@SpringBootTest // 此注解只能在 springboot 主包下使用 需包含 main 方法与 yml 配置文件
public class MySpringTest {

    @Autowired
    private IProductService iProductService;
    @Autowired
    private IProductSkuService iProductSkuService;
    @Autowired
    private IOrderItemPriceService iOrderItemPriceService;
    @Autowired
    private IProductActivityPriceItemService iProductActivityPriceItemService;
    @Autowired
    private IProductSkuStockService iProductSkuStockService;
    @Autowired
    private IWorldLocationService iWorldLocationService;

    @Test
    public void locationTest() {
        // List<WorldLocation> list = iWorldLocationService.lambdaQuery().list();
        //
        // JSONObject jsonObject = new JSONObject();
        // for (WorldLocation worldLocation : list) {
        //     Long locationId = worldLocation.getId();
        //     jsonObject.set(locationId.toString(), worldLocation.getLocationOtherName());
        // }
        //
        // System.out.println(jsonObject.toStringPretty());

        String data = FileUtil.readString("D:\\杂物\\地区.json", StandardCharsets.UTF_8);
        JSONObject jsonObject = JSONUtil.parseObj(data);

        jsonObject.forEach((key, value) -> {
            WorldLocationBo bo = new WorldLocationBo();
            bo.setId(Long.parseLong(key));
            bo.setLocationOtherName(JSONUtil.parseObj(value));
            iWorldLocationService.updateByBo(bo);
        });
    }

    @Test
    public void pageQuery() {
        Page<Product> queryPage = new Page<>(1, 10);
        ProductQueryBo queryBo = new ProductQueryBo();
        queryBo.setQueryType("ProductName");
        queryBo.setQueryValue("asd");

        IPage<Product> productIPage = iProductService.queryPageList(queryPage, queryBo);
        System.out.println(productIPage.getTotal());

        queryPage = new Page<>(2, 10);
        productIPage = iProductService.queryPageList(queryPage, new ProductQueryBo());
        System.out.println(productIPage.getTotal());
    }

    @Test
    public void testMP() {
        DownloadRecordUtil.generate("test.txt", DownloadTypePlusEnum.Orders, tempFileSavePath -> {
            String text = "测试导出工具类";
            File file = FileUtil.newFile(tempFileSavePath);
            file = FileUtil.writeString(text, file, StandardCharsets.UTF_8);
            return file;
        });
    }

    @Test
    public void testEntity1() {
        ExcelReader reader = ExcelUtil.getReader("E:\\StockUpdate_cn.xlsx");
        // 初始化中文表格的builder
        ExcelMsgBuilder<StockUploadDTO> builder = ZExcelUtil.msgBuilder(reader, 0, StockUploadDTO.class);

        ExcelReader reader_en = ExcelUtil.getReader("E:\\StockUpdate_en.xlsx");
        // 初始化英文表格的builder
        ExcelMsgBuilder<StockUploadDTO> builder_en = ZExcelUtil.msgBuilder(reader_en, 0, StockUploadDTO.class);

        // 模拟表格数据行循环
        for (int i = 0; i < 2; i++) {
            // 每次循环开头给builder设置当前行号
            builder.setNowShowRow(i + 1);
            builder_en.setNowShowRow(i + 1);

            System.out.println("--build默认拼接行号和列名--");
            // build默认拼接行号和列名
            System.out.println(builder.build(StockUploadDTO::getWarehouseSystemCode, ExcelMessageEnum.WAREHOUSE_NOT_FOUND));
            System.out.println(builder_en.build(StockUploadDTO::getWarehouseSystemCode, ExcelMessageEnum.WAREHOUSE_NOT_FOUND));
            System.out.println("--build默认拼接行号和列名--");

            System.out.println("");

            System.out.println("--buildOnlyRow（仅有行号）--");
            // buildOnlyRow（仅有行号）
            System.out.println(builder.buildOnlyRow(ExcelMessageEnum.WAREHOUSE_NOT_FOUND));
            System.out.println(builder_en.buildOnlyRow(ExcelMessageEnum.WAREHOUSE_NOT_FOUND));
            System.out.println("--buildOnlyRow（仅有行号）--");

            System.out.println("");

            System.out.println("--build重载，若不传列名，则既不带行号也不带列名--");
            // build重载，若不传列名，则既不带行号也不带列名
            System.out.println(builder.build(ExcelMessageEnum.WAREHOUSE_NOT_FOUND));
            System.out.println(builder_en.build(ExcelMessageEnum.WAREHOUSE_NOT_FOUND));
            System.out.println("--build重载，若不传列名，则既不带行号也不带列名--");

            System.out.println("");
            System.out.println("");
            System.out.println("");
        }
    }

    @Test
    public void testCheckPwd() {
        // Page<Product> productPage = iProductService.queryNormalProductForES();
        // List<Product> records = productPage.getRecords();
        // System.out.println(records == null);
        // for (Product record : records) {
        //
        // }

        // try {
        //     // 必须存在数字、字母、特殊符号
        //     PwdCheckUtils.builder("abc123456..").containCase().containDigit().containSpecialChar().start();
        // } catch (Exception e) {
        //     System.out.println(e.getMessage());
        // }
        //
        // try {
        //     // 长度5-10，必须存在数字、小写字母、大写字母
        //     PwdCheckUtils.builder("asdff123AA").containLength(5, 10).containDigit().containLowerCase().containUpperCase().start();
        // } catch (Exception e) {
        //     System.out.println(e.getMessage());
        // }
    }

    @Autowired
    private ISysTenantService tenantService;

    @Test
    public void testRex() {
        String rex = "^S[A-Z0-9]{6}$";
        List<SysTenantVo> sysTenantVos = tenantService.queryList(new SysTenantBo());
        for (SysTenantVo sysTenantVo : sysTenantVos) {
            String tenantId = sysTenantVo.getTenantId();
            Console.log("{} => {}", tenantId, ReUtil.isMatch(rex, tenantId));
        }

    }

    @Autowired
    private IProductSkuAttachmentService iProductSkuAttachmentService;
    @Autowired
    private IProductAttributeService iProductAttributeService;
    @Autowired
    private IProductSkuAttributeService iProductSkuAttributeService;
    @Autowired
    private IProductSkuPriceService iProductSkuPriceService;

    @Test
    public void rakutenTest() {
        String serviceSecret = "SP414318_uKlmDwQFlUm0VyQi";
        String licenseKey = "SL414318_KhvlUrZye303EfaX";
        RakutenDelegate rakutenDelegate = RakutenKit.create(serviceSecret, licenseKey);

        String productCode = "ZP494436";
        Product product = iProductService.queryByProductCodeNotTenant(productCode);

        InProductUpsert inProductUpsert = new InProductUpsert();

        InProductUpsert.ProductDescription productDescription = new InProductUpsert.ProductDescription();
        productDescription.setPc(product.getDescription());
        productDescription.setSp(product.getDescription());

        inProductUpsert.setItemNumber(productCode);
        inProductUpsert.setTitle(product.getName());
        inProductUpsert.setItemType(ItemTypeEmun.NORMAL);
        inProductUpsert.setProductDescription(productDescription);

        ProductSkuAttachmentVo productSkuAttachmentVo = iProductSkuAttachmentService.queryFirstImageByProductId(product.getId());
        inProductUpsert.addImage(ImageTypeEnum.CABINET, productSkuAttachmentVo.getAttachmentShowUrl());
        inProductUpsert.setGenreId("555555");

        List<ProductAttribute> optionalSpecList = iProductAttributeService.queryByProductIdAndAttributeType(product.getId(), AttributeTypeEnum.OptionalSpec);
        for (ProductAttribute productAttribute : optionalSpecList) {
            String attributeName = productAttribute.getAttributeName();
            List<String> attributeValues = productAttribute.getAttributeValues().toList(String.class);
            inProductUpsert.addVariantSelector(attributeName, attributeName, attributeValues);
        }

        Map<String, InProductUpsert.Variant> variants = new HashMap<>();
        List<ProductSku> productSkuList = iProductSkuService.queryByProductIdAndOnShelf(product.getId());
        for (ProductSku productSku : productSkuList) {
            Long productSkuId = productSku.getId();
            String productSkuCode = productSku.getProductSkuCode();
            InProductUpsert.Variant variant = new InProductUpsert.Variant();

            Map<String, String> selectorValues = new HashMap<>();
            List<ProductSkuAttribute> skuAttributes = iProductSkuAttributeService.queryByProductSkuId(productSkuId);
            for (ProductSkuAttribute skuAttribute : skuAttributes) {
                String attributeName = skuAttribute.getAttributeName();
                String attributeValue = skuAttribute.getAttributeValue();
                selectorValues.put(attributeName, attributeValue);
            }
            variant.setSelectorValues(selectorValues);

            List<ProductSkuAttachment> skuAttachmentList = iProductSkuAttachmentService.queryBySkuIdAndAttachmentTypeOrderBySortAsc(productSkuId, AttachmentTypeEnum.Image);
            for (ProductSkuAttachment productSkuAttachment : skuAttachmentList) {
                variant.addImage(ImageTypeEnum.CABINET, productSkuAttachment.getAttachmentShowUrl());
            }

            ProductSkuPrice productSkuPrice = iProductSkuPriceService.queryByProductSkuId(productSkuId);

            InProductUpsert.Variant.ReferencePrice referencePrice = new InProductUpsert.Variant.ReferencePrice();
            referencePrice.setDisplayType(ProductConstant.ReferencePrice.DisplayType.REFERENCE_PRICE);
            referencePrice.setType(ProductConstant.ReferencePrice.Type.NORMAL_PRICE);
            referencePrice.setValue(DecimalUtil.bigDecimalToString(productSkuPrice.getPlatformDropShippingPrice()));

            variant.setReferencePrice(referencePrice);
            variant.setStandardPrice(DecimalUtil.bigDecimalToString(productSkuPrice.getPlatformDropShippingPrice()));

            InProductUpsert.Variant.ArticleNumber articleNumber = new InProductUpsert.Variant.ArticleNumber();
            articleNumber.setValue(productSkuId.toString());
            variant.setArticleNumber(articleNumber);
            variants.put(productSkuCode, variant);
        }
        inProductUpsert.setVariants(variants);

        rakutenDelegate.productApi().upsert(productCode, inProductUpsert);
    }

    @Test
    public void rakutenOrderTest() {
        String serviceSecret = "SP414318_uKlmDwQFlUm0VyQi";
        String licenseKey = "SL414318_KhvlUrZye303EfaX";
        RakutenDelegate rakutenDelegate = RakutenKit.create(serviceSecret, licenseKey);

        InSearchOrder inSearchOrder = new InSearchOrder();
        inSearchOrder.setDateType(OrderConstant.DateType.OrderDate);
        inSearchOrder.setDateBetweenByOffset(3);
        inSearchOrder.pageDesc(1, 20);

        OutSearchOrder outSearchOrder = rakutenDelegate.orderApi().searchOrder(inSearchOrder);
        System.out.println(JSONUtil.toJsonStr(outSearchOrder));
    }

    @Test
    public void base64Test() throws InterruptedException {
        String pc = "ZP123456";
        List<String> repeat = new ArrayList<>();
        for (int i = 0; i < 1000; i++) {
            String generate = generate(pc);
            System.out.println(generate);
            if (repeat.contains(generate)) {
                System.out.println("出现重复");
            } else {
                repeat.add(generate);
            }
        }
    }

    private final static String PREFIX_INCREASE = GlobalConstants.GLOBAL_REDIS_KEY + "RAKUTEN:PRODUCT_ID";

    private String generate(String productCode) {
        StrJoiner manageNoJoiner = new StrJoiner("-");
        DateTime now = DateTime.now();
        manageNoJoiner.append(productCode);
        manageNoJoiner.append(DateUtil.format(now, "YYMMddHHmmss"));

        RedissonClient client = RedisUtils.getClient();
        RIdGenerator idGenerator = client.getIdGenerator(PREFIX_INCREASE);
        boolean b = idGenerator.tryInit(1, 1);
        // 初始化，第一次创建，则设置过期时间
        if (b) {
            // 15秒超时，稍微延后
            idGenerator.expire(Duration.ofSeconds(90));
        }

        long id = idGenerator.nextId();
        System.out.println("id => " + id);

        if (id == 9L) {
            // 删除自增，下次再重新初始化
            idGenerator.delete();
        }

        int msid = Integer.parseInt(DateUtil.format(now, "S") + id);
        String strNum = String.format("%04d", msid);
        manageNoJoiner.append(strNum);

        return manageNoJoiner.toString();
    }

}
