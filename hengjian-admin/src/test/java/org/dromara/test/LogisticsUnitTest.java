package com.hengjian.test;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.hengjian.common.core.utils.SpringUtils;
import com.zsmall.extend.wms.model.stock.OutStock;
import com.zsmall.order.entity.domain.Tracking17Carrier;
import com.zsmall.order.entity.iservice.ITracking17CarrierService;
import com.zsmall.system.entity.iservice.ITransactionsOrdersService;
import com.zsmall.system.entity.mapper.TenantReceiptAccountMapper;
import com.zsmall.warehouse.entity.domain.event.BizArkEvent;
import com.zsmall.warehouse.entity.iservice.ILogisticsRateCountryRelationService;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

/**
 * 物流单元测试
 *
 * <AUTHOR>
 * @date 2023/6/9
 */
@SpringBootTest // 此注解只能在 springboot 主包下使用 需包含 main 方法与 yml 配置文件
@DisplayName("单元测试案例")
public class LogisticsUnitTest {

    @Autowired
    private ITracking17CarrierService iTracking17CarrierService;
    @Autowired
    private ILogisticsRateCountryRelationService iLogisticsRateCountryRelationService;
    @Autowired
    private TenantReceiptAccountMapper tenantReceiptAccountMapper;
    @Autowired
    private ITransactionsOrdersService iTransactionsOrdersService;

    @Test
    public void testLogistics() throws Exception {
        iTransactionsOrdersService.saveRelation(1679458917073285122L, 1679458905870299137L);
    }

    @Test
    public void testTrack17() {
        String readString = FileUtil.readString("D:\\表暂存\\apicarrier.all.json", StandardCharsets.UTF_8);
        JSONArray jsonArray = JSONUtil.parseArray(readString);
        List<Tracking17Carrier> tracking17CarrierList = new ArrayList<>();
        jsonArray.forEach(object -> {
            JSONObject jsonObject = JSONUtil.parseObj(object);
            Integer key = jsonObject.getInt("key");
            Integer _country = jsonObject.getInt("_country");
            String _country_iso = jsonObject.getStr("_country_iso");
            String _url = jsonObject.getStr("_url");
            String _name = jsonObject.getStr("_name");
            String _name_zh_cn = jsonObject.getStr("_name_zh-cn");
            String _name_zh_hk = jsonObject.getStr("_name_zh-hk");
            String _group = jsonObject.getStr("_group");

            Tracking17Carrier carrierEntity = new Tracking17Carrier();
            carrierEntity.setCarrierKey(key);
            carrierEntity.setCarrierCountry(_country);
            if (StrUtil.isNotBlank(_country_iso)) {
                carrierEntity.setCarrierCountryIso(_country_iso);
            }
            carrierEntity.setCarrierUrl(_url);
            carrierEntity.setCarrierName(_name);
            carrierEntity.setCarrierNameZhCn(_name_zh_cn);
            carrierEntity.setCarrierNameZhHk(_name_zh_hk);
            if (StrUtil.isNotBlank(_group)) {
                carrierEntity.setCarrierGroup(_group);
            }
            tracking17CarrierList.add(carrierEntity);
        });
        iTracking17CarrierService.saveBatch(tracking17CarrierList);
    }

    @Test
    public void bizark() {
        List<String> erpSkuList = CollUtil.newArrayList("SKU_US_TEST_01");
        BizArkEvent queryStockEvent = BizArkEvent.queryStock("S0EUN7U", erpSkuList);
        SpringUtils.context().publishEvent(queryStockEvent);
        List<OutStock> outStockList = queryStockEvent.getQueryStock().getOut();
        System.out.println(JSONUtil.toJsonPrettyStr(outStockList));
    }

}
