package com.hengjian.test;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.zsmall.common.constant.MallConstants;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.Set;

/**
 * 断言单元测试案例
 *
 * <AUTHOR> Li
 */
@DisplayName("断言单元测试案例")
public class AssertUnitTest {

    public enum CompareEnum {
        EQ(0),

        NE(-1, 1),

        GT(1),

        LT(-1),

        ;

        private Integer[] result;

        CompareEnum(Integer... result) {
            this.result = result;
        }

        // public Boolean compare(Integer a, Integer b) {
        //     return this.result.contains(String.valueOf(a.compareTo(b)));
        // }

        public Boolean compare(Integer a, Integer b) {
            return Arrays.stream(this.result).anyMatch(val -> a.compareTo(b) == val);
        }
    }

    @Test
    public void test() {
        System.out.println(CompareEnum.EQ.compare(1, 2));
        System.out.println(CompareEnum.EQ.compare(2, 2));
        System.out.println(CompareEnum.EQ.compare(3, 1));
        System.out.println("=========");
        System.out.println(CompareEnum.NE.compare(1, 2));
        System.out.println(CompareEnum.NE.compare(2, 2));
        System.out.println(CompareEnum.NE.compare(3, 1));
    }

//     @DisplayName("测试 assertEquals 方法")
//     @Test
//     public void testAssertEquals() {
//         // Assertions.assertEquals("666", new String("666"));
//         // Assertions.assertNotEquals("666", new String("666"));
//
//         List<String> zh_CN_List = new LinkedList<>();
//         List<String> en_US_List = new LinkedList<>();
//         List<String> statusCodeList = new LinkedList<>();
//         ZSMallStatusCodeEnum[] values = ZSMallStatusCodeEnum.values();
//         String categoryName = "";
//         for (ZSMallStatusCodeEnum value : values) {
//             String category = value.getCategory();
//             if (StrUtil.isNotBlank(category)) {
//                 categoryName = category;
//             }
//             String str = "zsmall" + "." + categoryName + "." + StrUtil.toCamelCase(value.name());
//             zh_CN_List.add(str + "=" + value.getMessageCN());
//             en_US_List.add(str + "=" + value.getMessage());
//         }
//         FileUtil.writeLines(zh_CN_List, "D://zsmallStatusCode_zh_CN.txt", "UTF-8");
//         FileUtil.writeLines(en_US_List, "D://statusCode_en_US.txt", "UTF-8");
//
//         Map<String, String> map = MapUtil.builder(new HashMap<String, String>()).put("code", "1234").put("expiration", "2").build();
//         String newContent = StrUtil.format("您本次验证码为：{code}，有效性为{expiration}分钟，请尽快使用。", map);
//         System.out.println(newContent);
// //
//         String pwd = SecureUtil.sha1("123456");
//         System.out.println(pwd);
//         System.out.println(BCrypt.hashpw(pwd));
//         String adminPwd = SecureUtil.sha1("admin123");
//         System.out.println(adminPwd);
//         System.out.println(BCrypt.hashpw(adminPwd));
//     }

    // @DisplayName("测试 assertEquals1 方法")
    // @Test
    // public void testAssertEquals1() {
    //     ZSMallStatusCodeEnum_COPY[] values = ZSMallStatusCodeEnum_COPY.values();
    //     String categoryName = "";
    //
    //     String template = "{}(\"{}\", \"{}\"),\n";
    //     String noteTemplate = "/** {} */\n";
    //     // List<String> list = new LinkedList<>();
    //     StringBuilder stringBuilder = new StringBuilder();
    //     for (ZSMallStatusCodeEnum_COPY value : values) {
    //         String category = value.getCategory();
    //         if (StrUtil.isNotBlank(category)) {
    //             stringBuilder.append("\n");
    //             categoryName = category;
    //         }
    //         String str = "zsmall" + "." + categoryName + "." + StrUtil.toCamelCase(value.name());
    //         String result = StrUtil.format(template, value.name(), value.getCode(), str);
    //         String note = StrUtil.format(noteTemplate, value.getMessageCN());
    //         stringBuilder.append(note);
    //         stringBuilder.append(result);
    //     }
    //     FileUtil.writeString(stringBuilder.toString(), "D://statusCode.txt", "UTF-8");
    // }

    @Test
    public void testList() {
        String json = "{\"skuNotExist\":\"The Item No. has been taken off the shelf or cannot be found (If you fill in the warehouse code, it is also possible that the product does not exist in the specified warehouse)!\",\"irregularity\":\"Non compliance with rules !\",\"country\":\"Unsupported countries\",\"state\":\"Must be 2-letter codes used by the United States Postal Service.\",\"tracking\":\"[Carrier] and [Tracking No.] must fill in together or not fill in.\",\"carrierCode\":\"[Carrier] must be UPS or FedEx\",\"trackingNum\":\"[Tracking No.] quantity must be the same as the number of product or only one.\",\"trackingFormat\":\"[Tracking No.] too long, if you have multiple Tracking No., please separate them whit ','\",\"zipCode\":\"[Zip code] does not meet the specifications(00000 or 00000-0000). If the excel format is incorrect, please download the import template again\",\"shippingType\":\"Only 'Pick Up' or 'DropShipping' can be filled in\",\"dropShippingLimit\":\"If [Shipping] selected the 'DropShipping', [Carrier], [Shipping Service], [Tracking No], [3rd Billing], [Carrier Account], [Carrier Account Zip Code] cannot be filled in.\",\"pickUpCarrier\":\"If [Shipping] selected the 'Pick Up', [Carrier] must be fill in.\",\"thirdBillingTrackingNo\":\"If [3rd Billing] selected the 'No', [Tracking No.] must be filled in.\",\"pickUpThirdBilling\":\"If [Shipping] selected the 'Pick Up', [3rd Billing] must be select 'Yes' or 'No'.\",\"thirdBillingCA\":\"If [3rd Billing] selected the 'Yes', [Carrier Account] must be fill in.\",\"thirdBillingCAZipCode\":\"If [3rd Billing] selected the 'Yes', [Carrier Account Zip Code] must be fill in.\",\"phoneNumberLength\":\"The length of the phone number cannot exceed 20 digits.\",\"notThirdBillingTrackingNo\":\"If [3rd Billing] selected the 'Yes', [Tracking No.] can't be filled in.\",\"notThirdBillingCA\":\"If [3rd Billing] selected the 'No', [Carrier Account] can't be filled in.\",\"notThirdBillingCAZipCode\":\"If [3rd Billing] selected the 'No', [Carrier Account Zip Code] can't be filled in.\",\"thirdBillingNotSupportWarehouseCode\":\"[3rd Billing] selected the 'YES', but warehouse is not support. You can refill [Warehouse Code] or not fill it, if not fill it, we will automatically match a supported repository.\",\"repeatStoreOrderID\":\"[Store Order ID] is repeat.\",\"itemNoNotExist\":\"The Item NO. dost not exist.\",\"dropShippingOnly\":\"This product only supports 'DropShipping'.\",\"pickUpOnly\":\"This product only supports 'Pick Up'.\",\"activityNotExist\":\"The activity does not exist or has ended.\",\"unrecognizableTrackingNo\":\"The carrier cannot be identified by the Tracking No.\",\"itemNoNotJoinActivity\":\"Not participating in the specified activity\"}";
        JSONObject errorMsg = JSONUtil.parseObj(json);
        Set<String> strings = errorMsg.keySet();
        String template = "{}(\"{}\"),";
        for (String string : strings) {
            String underlineCase = StrUtil.toUnderlineCase(string);
            System.out.println(StrUtil.format(template, underlineCase.toUpperCase(), "zsmall.excelImport." + string));
        }
    }

    @DisplayName("测试 assertSame 方法")
    @Test
    public void testAssertSame() {
        Object obj = new Object();
        Object obj1 = obj;
//        Assertions.assertSame(obj, obj1);
//        Assertions.assertNotSame(obj, obj1);

        String name = "zh_CN";
        String sub = StrUtil.sub(name, 0, StrUtil.indexOf(name, '_'));
        System.out.println(sub);
        String replace = StrUtil.replace(MallConstants.Config.Lang_PrivacyPolicy, "{lang}", sub);
        System.out.println(replace);
    }

    @DisplayName("测试 assertTrue 方法")
    @Test
    public void testAssertTrue() {
        Assertions.assertTrue(true);
        Assertions.assertFalse(true);
    }

    @DisplayName("测试 assertNull 方法")
    @Test
    public void testAssertNull() {
        Assertions.assertNull(null);
        Assertions.assertNotNull(null);
    }

}
