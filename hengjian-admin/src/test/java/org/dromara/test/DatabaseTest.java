package com.hengjian.test;

import cn.smallbun.screw.core.Configuration;
import cn.smallbun.screw.core.engine.EngineConfig;
import cn.smallbun.screw.core.engine.EngineFileType;
import cn.smallbun.screw.core.engine.EngineTemplateType;
import cn.smallbun.screw.core.execute.DocumentationExecute;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;

import javax.sql.DataSource;

/**
 * 数据库测试用例
 *
 * <AUTHOR>
 * @date 2023/4/11
 */
@SpringBootTest // 此注解只能在 springboot 主包下使用 需包含 main 方法与 yml 配置文件
public class DatabaseTest {

    @Autowired
    private ApplicationContext applicationContext;

    @Test
    public void generateDatabaseDoc() {
        //数据源
        HikariConfig hikariConfig = new HikariConfig();
        hikariConfig.setDriverClassName("com.mysql.cj.jdbc.Driver");
        hikariConfig.setJdbcUrl("********************************************************************************************************************************************************************************************************");
        hikariConfig.setUsername("root");
        hikariConfig.setPassword("123456");
        //设置可以获取tables remarks信息
        hikariConfig.addDataSourceProperty("useInformationSchema", "true");
        hikariConfig.setMinimumIdle(2);
        hikariConfig.setMaximumPoolSize(5);
        DataSource dataSource = new HikariDataSource(hikariConfig);
        // 生成文件配置 创建screw的引擎配置
        EngineConfig engineConfig = EngineConfig.builder()
                                                // 生成文件路径
                                                .fileOutputDir("D:\\file")
                                                // 打开目录
                                                .openOutputDir(true)
                                                // 文件类型 HTML->HTML文件  WORD->WORD文件  MD->Markdown文件
                                                .fileType(EngineFileType.WORD)
                                                // 生成模板实现
                                                .produceType(EngineTemplateType.freemarker)
                                                // 自定义文件名称，即生成的数据库文档名称
                                                .fileName("ZSMall数据库设计文档").build();
        // 生成文档配置（包含以下自定义版本号、描述等配置连接）
        Configuration config = Configuration.builder()
                                            // 版本
                                            .version("1.0.0")
                                            // 描述
                                            .description("ZSMall数据库设计文档")
                                            // 数据源
                                            .dataSource(dataSource)
                                            // 生成配置
                                            .engineConfig(engineConfig)
                                            // 生成配置
                                            // .produceConfig(getProcessConfig())
                                            .build();
        // 执行screw，生成数据库文档
        new DocumentationExecute(config).execute();
    }

}
