package com.hengjian.test;

import cn.hutool.json.JSONUtil;
import com.hengjian.common.core.config.HengJianConfig;
import com.zsmall.product.entity.iservice.IProductAttachmentService;
import com.zsmall.product.entity.iservice.IProductService;
import com.zsmall.product.entity.iservice.IProductSkuService;
import com.zsmall.product.entity.iservice.IViewProductStockService;
import com.zsmall.system.entity.domain.TenantSupSettleInBasic;
import com.zsmall.system.entity.util.SettleInReflectUtil;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 单元测试案例
 *
 * <AUTHOR> Li
 */
// @SpringBootTest // 此注解只能在 springboot 主包下使用 需包含 main 方法与 yml 配置文件
@DisplayName("单元测试案例")
public class DemoUnitTest {

    @Autowired
    private HengJianConfig hengJianConfig;
    @Autowired
    private IProductService iProductService;
    @Autowired
    private IProductAttachmentService iProductAttachmentService;
    @Autowired
    private IProductSkuService iProductSkuService;
    @Autowired
    private IViewProductStockService iViewProductStockService;

    @DisplayName("测试 @SpringBootTest @Test @DisplayName 注解")
    @Test
    public void testTest() throws Exception {
        /*System.out.println(Locale.SIMPLIFIED_CHINESE.getLanguage());
        System.out.println(Locale.SIMPLIFIED_CHINESE.getCountry());
        System.out.println(Locale.SIMPLIFIED_CHINESE);
        System.out.println(Locale.SIMPLIFIED_CHINESE.getDisplayScript());
        System.out.println(Locale.SIMPLIFIED_CHINESE.getDisplayLanguage());
        System.out.println(Locale.SIMPLIFIED_CHINESE.getDisplayVariant());
        System.out.println(Locale.SIMPLIFIED_CHINESE.getDisplayCountry());*/
        try {
            TenantSupSettleInBasic oldO = new TenantSupSettleInBasic();
            oldO.setStateText("海口");
            oldO.setCityText("123456");

//            oldO.setLegalPersonName("123456");
            TenantSupSettleInBasic new1 = new TenantSupSettleInBasic();
            TenantSupSettleInBasic new2 = new TenantSupSettleInBasic();
            new1.setCityText("123456");
            new1.setStateText("海口");

//            new1.setLegalPersonName("1234567");

            new2.setCityText("========");
            new2.setStateText("海口");

//            new2.setLegalPersonName("123456");
//            TenantSupSettleInBasic tenantSupSettleInBasic = SettleInReflectUtil.compareTenantSupSettleInObj(oldO, new1, TenantSupSettleInBasic.class);
//            System.out.println("isChange1 = " + JSONUtil.toJsonStr(tenantSupSettleInBasic));
            TenantSupSettleInBasic tenantSupSettleInBasic1 = SettleInReflectUtil.compareTenantSupSettleInObj(oldO, new2, TenantSupSettleInBasic.class);
            System.out.println("===========================");
            System.out.println("isChange2 = " + JSONUtil.toJsonStr(tenantSupSettleInBasic1));
        }catch (Exception e) {
            e.printStackTrace();
        }

    }

    public String getProductName() {
        return "";
    }

    // @Disabled
    // @DisplayName("测试 @Disabled 注解")
    // @Test
    // public void testDisabled() {
    //     System.out.println(hengJianConfig);
    // }
    //
    // @Timeout(value = 2L, unit = TimeUnit.SECONDS)
    // @DisplayName("测试 @Timeout 注解")
    // @Test
    // public void testTimeout() throws InterruptedException {
    //     Thread.sleep(3000);
    //     System.out.println(hengJianConfig);
    // }
    //
    //
    // @DisplayName("测试 @RepeatedTest 注解")
    // @RepeatedTest(3)
    // public void testRepeatedTest() {
    //     System.out.println(666);
    // }
    //
    // @BeforeAll
    // public static void testBeforeAll() {
    //     System.out.println("@BeforeAll ==================");
    // }
    //
    // @BeforeEach
    // public void testBeforeEach() {
    //     System.out.println("@BeforeEach ==================");
    // }
    //
    // @AfterEach
    // public void testAfterEach() {
    //     System.out.println("@AfterEach ==================");
    // }
    //
    // @AfterAll
    // public static void testAfterAll() {
    //     System.out.println("@AfterAll ==================");
    // }

}
