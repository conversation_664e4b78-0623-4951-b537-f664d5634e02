<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>hengjian-distribution</artifactId>
        <groupId>com.hengjian</groupId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <packaging>jar</packaging>
    <artifactId>hengjian-admin</artifactId>

    <description>
        web服务入口
    </description>

    <dependencies>
        <!-- 引入数据库表结构文档生成器Screw依赖-->
        <dependency>
            <groupId>cn.smallbun.screw</groupId>
            <artifactId>screw-core</artifactId>
            <version>1.0.5</version>
        </dependency>

        <!-- Mysql驱动包 -->
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
        </dependency>
<!--        &lt;!&ndash; Oracle &ndash;&gt;-->
<!--        <dependency>-->
<!--            <groupId>com.oracle.database.jdbc</groupId>-->
<!--            <artifactId>ojdbc8</artifactId>-->
<!--        </dependency>-->
<!--        &lt;!&ndash; PostgreSql &ndash;&gt;-->
<!--        <dependency>-->
<!--            <groupId>org.postgresql</groupId>-->
<!--            <artifactId>postgresql</artifactId>-->
<!--        </dependency>-->
<!--        &lt;!&ndash; SqlServer &ndash;&gt;-->
<!--        <dependency>-->
<!--            <groupId>com.microsoft.sqlserver</groupId>-->
<!--            <artifactId>mssql-jdbc</artifactId>-->
<!--        </dependency>-->

        <dependency>
            <groupId>com.hengjian</groupId>
            <artifactId>hengjian-common-doc</artifactId>
        </dependency>

        <dependency>
            <groupId>com.hengjian</groupId>
            <artifactId>hengjian-system</artifactId>
        </dependency>

        <dependency>
            <groupId>com.hengjian</groupId>
            <artifactId>hengjian-common-encrypt</artifactId>
        </dependency>

        <dependency>
            <groupId>com.hengjian</groupId>
            <artifactId>hengjian-extend-emailsms</artifactId>
        </dependency>

        <dependency>
            <groupId>com.hengjian</groupId>
            <artifactId>hengjian-job</artifactId>
        </dependency>

        <!-- 代码生成-->
        <dependency>
            <groupId>com.hengjian</groupId>
            <artifactId>hengjian-generator</artifactId>
        </dependency>

        <!--  demo模块  -->
<!--        <dependency>
            <groupId>com.hengjian</groupId>
            <artifactId>hengjian-demo</artifactId>
        </dependency>-->

        <dependency>
            <groupId>de.codecentric</groupId>
            <artifactId>spring-boot-admin-starter-client</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.zsmall</groupId>
            <artifactId>zsmall-system-controller</artifactId>
        </dependency>

        <dependency>
            <groupId>com.zsmall</groupId>
            <artifactId>zsmall-product-controller</artifactId>
        </dependency>

        <dependency>
            <groupId>com.zsmall</groupId>
            <artifactId>zsmall-marketplace</artifactId>
        </dependency>

        <dependency>
            <groupId>com.zsmall</groupId>
            <artifactId>zsmall-warehouse-controller</artifactId>
        </dependency>

        <dependency>
            <groupId>com.zsmall</groupId>
            <artifactId>zsmall-order-controller</artifactId>
        </dependency>

        <dependency>
            <groupId>com.zsmall</groupId>
            <artifactId>zsmall-activity-controller</artifactId>
        </dependency>

        <dependency>
            <groupId>com.hengjian.openapi</groupId>
            <artifactId>hengjian-openapi</artifactId>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>com.zsmall</groupId>-->
<!--            <artifactId>zsmall-xxl-job</artifactId>-->
<!--        </dependency>-->

        <!-- skywalking 整合 logback -->
        <!--        <dependency>-->
        <!--            <groupId>org.apache.skywalking</groupId>-->
        <!--            <artifactId>apm-toolkit-logback-1.x</artifactId>-->
        <!--            <version>${与你的agent探针版本保持一致}</version>-->
        <!--        </dependency>-->
        <!--        <dependency>-->
        <!--            <groupId>org.apache.skywalking</groupId>-->
        <!--            <artifactId>apm-toolkit-trace</artifactId>-->
        <!--            <version>${与你的agent探针版本保持一致}</version>-->
        <!--        </dependency>-->

    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>${maven-jar-plugin.version}</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-war-plugin</artifactId>
                <version>${maven-war-plugin.version}</version>
                <configuration>
                    <failOnMissingWebXml>false</failOnMissingWebXml>
                    <warName>${project.artifactId}</warName>
                </configuration>
            </plugin>
            <plugin>
                <groupId>com.idea-aedi</groupId>
                <artifactId>class-winter-maven-plugin</artifactId>
                <version>2.7.7</version>
                <configuration>
                    <finalName>${artifactId}-${zsmall.version}</finalName>
                    <includeLibs>zsmall-system-biz-${zsmall.version}.jar</includeLibs>
                    <includePrefix>com.zsmall.system</includePrefix>
                    <jvmArgCheck>-XX:+DisableAttachMechanism</jvmArgCheck>
                    <debug>true</debug>
                </configuration>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>class-winter</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
