package ${packageName}.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.hengjian.common.idempotent.annotation.RepeatSubmit;
import com.hengjian.common.log.annotation.Log;
import com.hengjian.common.web.core.BaseController;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.validate.AddGroup;
import com.hengjian.common.core.validate.EditGroup;
import com.hengjian.common.log.enums.BusinessType;
import com.hengjian.common.excel.utils.ExcelUtil;
import ${packageName}.domain.vo.${ClassName}Vo;
import ${packageName}.domain.bo.${ClassName}Bo;
import ${packageName}.service.I${ClassName}Service;
#if($table.crud || $table.sub)
import com.hengjian.common.mybatis.core.page.TableDataInfo;
#elseif($table.tree)
#end

/**
 * ${functionName}
 *
 * <AUTHOR>
 * @date ${datetime}
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/${moduleName}/${businessName}")
public class ${ClassName}Controller extends BaseController {

    private final I${ClassName}Service ${className}Service;

    /**
     * 查询${functionName}列表
     */
    @SaCheckPermission("${permissionPrefix}:list")
    @GetMapping("/list")
#if($table.crud || $table.sub)
    public TableDataInfo<${ClassName}Vo> list(${ClassName}Bo bo, PageQuery pageQuery) {
        return ${className}Service.queryPageList(bo, pageQuery);
    }
#elseif($table.tree)
    public R<List<${ClassName}Vo>> list(${ClassName}Bo bo) {
        List<${ClassName}Vo> list = ${className}Service.queryList(bo);
        return R.ok(list);
    }
#end

    /**
     * 导出${functionName}列表
     */
    @SaCheckPermission("${permissionPrefix}:export")
    @Log(title = "${functionName}", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(${ClassName}Bo bo, HttpServletResponse response) {
        List<${ClassName}Vo> list = ${className}Service.queryList(bo);
        ExcelUtil.exportExcel(list, "${functionName}", ${ClassName}Vo.class, response);
    }

    /**
     * 获取${functionName}详细信息
     *
     * @param ${pkColumn.javaField} 主键
     */
    @SaCheckPermission("${permissionPrefix}:query")
    @GetMapping("/{${pkColumn.javaField}}")
    public R<${ClassName}Vo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable ${pkColumn.javaType} ${pkColumn.javaField}) {
        return R.ok(${className}Service.queryById(${pkColumn.javaField}));
    }

    /**
     * 新增${functionName}
     */
    @SaCheckPermission("${permissionPrefix}:add")
    @Log(title = "${functionName}", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody ${ClassName}Bo bo) {
        return toAjax(${className}Service.insertByBo(bo));
    }

    /**
     * 修改${functionName}
     */
    @SaCheckPermission("${permissionPrefix}:edit")
    @Log(title = "${functionName}", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody ${ClassName}Bo bo) {
        return toAjax(${className}Service.updateByBo(bo));
    }

    /**
     * 删除${functionName}
     *
     * @param ${pkColumn.javaField}s 主键串
     */
    @SaCheckPermission("${permissionPrefix}:remove")
    @Log(title = "${functionName}", businessType = BusinessType.DELETE)
    @DeleteMapping("/{${pkColumn.javaField}s}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable ${pkColumn.javaType}[] ${pkColumn.javaField}s) {
        return toAjax(${className}Service.deleteWithValidByIds(List.of(${pkColumn.javaField}s), true));
    }
}
