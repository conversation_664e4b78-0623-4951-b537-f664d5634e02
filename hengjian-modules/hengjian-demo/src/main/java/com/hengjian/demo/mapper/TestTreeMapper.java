package com.hengjian.demo.mapper;

import com.hengjian.common.mybatis.annotation.DataColumn;
import com.hengjian.common.mybatis.annotation.DataPermission;
import com.hengjian.common.mybatis.core.mapper.BaseMapperPlus;
import com.hengjian.demo.domain.TestTree;
import com.hengjian.demo.domain.vo.TestTreeVo;

/**
 * 测试树表Mapper接口
 *
 * <AUTHOR> Li
 * @date 2021-07-26
 */
@DataPermission({
    @DataColumn(key = "deptName", value = "dept_id"),
    @DataColumn(key = "userName", value = "user_id")
})
public interface TestTreeMapper extends BaseMapperPlus<TestTree, TestTreeVo> {

}
