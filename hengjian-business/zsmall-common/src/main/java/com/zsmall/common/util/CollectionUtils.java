package com.zsmall.common.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024年5月28日  09:29
 * @description:
 */
public class CollectionUtils {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 将list对象集合转化为 json String
     *
     * @param list
     * @return
     * @throws JsonProcessingException
     */
    public static String listToJson(List<?> list) throws JsonProcessingException {
        ObjectMapper mapper = new ObjectMapper();
        return mapper.writeValueAsString(list);
    }

    /**
     * 将对象转换为JSON字符串。
     *
     * @param object 要转换的对象
     * @return JSON字符串
     * @throws JsonProcessingException 如果转换过程中发生错误
     */
    public static String objectToJson(Object object) throws JsonProcessingException {
        return objectMapper.writeValueAsString(object);
    }

    /**
     * 将JSON字符串转换为对象。
     *
     * @param json    JSON字符串
     * @param clazz   要转换的目标类
     * @param <T>     目标类的类型
     * @return 转换后的对象
     * @throws IOException 如果转换过程中发生错误
     */
    public static <T> T jsonToObject(String json, Class<T> clazz) throws IOException {
        return objectMapper.readValue(json, clazz);
    }

    /**
     * 将JSON字符串转换为List对象。
     *
     * @param json    JSON字符串
     * @param clazz   列表中元素的类
     * @param <T>     列表中元素的类型
     * @return 转换后的List对象
     * @throws IOException 如果转换过程中发生错误
     */
    public static <T> List<T> jsonToList(String json, Class<T> clazz) throws IOException {
        return objectMapper.readValue(json, objectMapper.getTypeFactory().constructCollectionType(List.class, clazz));
    }

}
