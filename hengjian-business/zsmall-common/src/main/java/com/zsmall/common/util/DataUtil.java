package com.zsmall.common.util;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;

/**
 * <AUTHOR>
 * @date 2022-08-09
 **/
@Slf4j
public class DataUtil {

  /**
   * 空字符串转成null   反射
   *
   * @param object
   */
  public static void convertNullString2Null(Object object) throws Exception {
    try {
      Field[] declaredFields = object.getClass().getDeclaredFields();
      if (ArrayUtil.isNotEmpty(declaredFields)) {
        for (Field field : declaredFields) {
          field.setAccessible(true);
          Object val = field.get(object);
          String type = field.getGenericType().getTypeName();
          if (StrUtil.equals(type, "java.lang.String")) {
            if (StrUtil.isBlank((String) val)) {
              field.set(object, null);
            }
          }
        }
      }
    } catch (Exception e) {
      throw new Exception("空字符串转成null发生错误 " + e.getMessage());
    }
  }

  /**
   * 获取注解上的字段名
   *
   * @param beanClass 类
   * @param fieldName 字段名
   * @return
   */
  public static String getAnnotationsValue(Class<?> beanClass, String fieldName) {
    //获取字段
    Field field = ReflectUtil.getField(beanClass, fieldName);
    return field.getAnnotation(TableField.class).value();
  }

}
