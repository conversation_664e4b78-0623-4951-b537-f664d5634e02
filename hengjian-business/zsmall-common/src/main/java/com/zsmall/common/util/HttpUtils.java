package com.zsmall.common.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/6/25 10:27
 */
public class HttpUtils {

//    public static void main(String[] args){
//        try {
//            String url = "http://8.210.198.240:11111/api/v1/distribution_oss";
//            ObjectMapper objectMapper = new ObjectMapper();
//
//            // 创建一个空的ObjectNode
//            ObjectNode node = objectMapper.createObjectNode();
//
//            // 手动添加属性到ObjectNode
//            node.put("url", "https://p19-oec-ttp.tiktokcdn-us.com/tos-useast5-i-omjb5zjo8w-tx/55471c76771443698ec6a800c263b331~tplv-omjb5zjo8w-origin-jpeg.jpeg");
//            String json;
//            // 将ObjectNode转换为JSON字符串
//            try {
//                json = objectMapper.writeValueAsString(node);
//            } catch (Exception e) {
//                throw new RuntimeException(e);
//            }
//            String response = sendPost(url,json);
//            System.out.println(response);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }

    public static String sendGet(String urlStr) throws Exception {
        URL url = new URL(urlStr);
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        conn.setRequestMethod("GET");

        int responseCode = conn.getResponseCode();
        if (responseCode == HttpURLConnection.HTTP_OK) {
            BufferedReader in = new BufferedReader(new InputStreamReader(conn.getInputStream()));
            String inputLine;
            StringBuilder content = new StringBuilder();
            while ((inputLine = in.readLine()) != null) {
                content.append(inputLine);
            }
            in.close();
            return content.toString();
        } else {
            throw new Exception("GET request not worked");
        }
    }

    public static String sendPost(String urlStr, String urlParameters) throws Exception {
        URL url = new URL(urlStr);
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        conn.setRequestMethod("POST");
        conn.setDoOutput(true);

        OutputStream os = conn.getOutputStream();
        os.write(urlParameters.getBytes());
        os.flush();
        os.close();

        int responseCode = conn.getResponseCode();
        if (responseCode == HttpURLConnection.HTTP_OK) {
            BufferedReader in = new BufferedReader(new InputStreamReader(conn.getInputStream()));
            String inputLine;
            StringBuilder content = new StringBuilder();
            while ((inputLine = in.readLine()) != null) {
                content.append(inputLine);
            }
            in.close();
            return content.toString();
        } else {
            throw new Exception("POST request not worked");
        }
    }
}
