package com.zsmall.common.util;

import cn.hutool.core.util.StrUtil;
import com.zsmall.common.annotaion.BaseEnum;
import com.zsmall.common.exception.AppRuntimeException;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/1/26 11:10
 */
public class AssertUtil {
    public static void isTrue(boolean expression, String message) {
        if (!expression) {
            throw new AppRuntimeException(message);
        }
    }


    public static void isTrue(boolean expression, BaseEnum<?> baseEnum) {
        if (!expression) {
            throw new AppRuntimeException(baseEnum.getName());
        }
    }

    public static void isTrue(boolean expression, BaseEnum<?> baseEnum, Object... params) {
        if (!expression) {
            throw new AppRuntimeException(StrUtil.format(baseEnum.getName(), params));
        }
    }

    public static void isFase(boolean expression, BaseEnum<?> baseEnum, Object... params) {
        if (expression) {
            throw new AppRuntimeException(StrUtil.format(baseEnum.getName(), params));
        }
    }

    public static void isFalse(boolean expression, BaseEnum<?> baseEnum) {
        if (expression) {
            throw new AppRuntimeException(baseEnum.getName());
        }
    }

    public static void isFail(BaseEnum<?> baseEnum) {
        throw new AppRuntimeException(baseEnum.getName());
    }

    public static void isFail(BaseEnum<?> baseEnum, Exception e) {
        throw new AppRuntimeException(StrUtil.format(baseEnum.getName(), e.getMessage()), e);
    }

    public static void isFail(Exception e) {
        throw new AppRuntimeException(e.getMessage(), e);
    }
}
