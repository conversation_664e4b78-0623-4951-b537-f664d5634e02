package com.zsmall.common.util;

import com.itextpdf.text.Document;
import com.itextpdf.text.pdf.PdfCopy;
import com.itextpdf.text.pdf.PdfImportedPage;
import com.itextpdf.text.pdf.PdfReader;
import com.itextpdf.text.pdf.parser.PdfTextExtractor;
import lombok.Cleanup;
import lombok.extern.slf4j.Slf4j;

import java.io.ByteArrayOutputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Base64;
import java.util.List;

/**
 * PDF工具类
 * <AUTHOR>
 * @create 2022/4/12 15:13
 */
@Slf4j
public class PDFUtil {

  /**
   * 将PDF文件切分成多个PDF
   *
   * @param filename  文件名
   * @param splitSize 拆分单个文件页数
   */
  public static void splitPdf(String filename, int splitSize) throws Exception {
    PdfReader reader;
    try {
      reader = new PdfReader(filename);
    } catch (IOException e) {
      log.error(e.getMessage(), e);
      throw new Exception("读取PDF文件失败");
    }
    int numberOfPages = reader.getNumberOfPages();
    int newFileCount = 0;
    // PageNumber是从1开始计数的
    int pageNumber = 1;
    while (pageNumber <= numberOfPages) {
      Document doc = new Document();
      String splitFileName = filename.substring(0, filename.length() - 4) + "(" + newFileCount + ").pdf";
      PdfCopy pdfCopy;
      try {
        pdfCopy = new PdfCopy(doc, new FileOutputStream(splitFileName));
      } catch (Exception e) {
        log.error(e.getMessage(), e);
        throw new Exception("切割文件副本创建失败");
      }
      doc.open();
      // 将pdf按页复制到新建的PDF中
      for (int i = 1; pageNumber <= numberOfPages && i <= splitSize; ++i, pageNumber++) {
        doc.newPage();
        PdfImportedPage page = pdfCopy.getImportedPage(reader, pageNumber);
        pdfCopy.addPage(page);
      }
      doc.close();
      newFileCount++;
      pdfCopy.close();
    }
  }
  /**
   * 拆分PDF并打包成Base64集合
   * @param inputStream
   * @param splitSize
   * @return
   * @throws Exception
   */
  public static List<String> splitPdfToBase64(InputStream inputStream, int splitSize) throws Exception {
    List<String> base64List = new ArrayList<>();
    PdfReader reader;
    try {
      reader = new PdfReader(inputStream);
    } catch (IOException e) {
      log.error(e.getMessage(), e);
      throw new Exception("读取PDF文件失败");
    }
    int numberOfPages = reader.getNumberOfPages();
    // PageNumber是从1开始计数的
    int pageNumber = 1;
    while (pageNumber <= numberOfPages) {
      Document doc = new Document();
      @Cleanup ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
      PdfCopy pdfCopy;
      try {
        pdfCopy = new PdfCopy(doc, byteArrayOutputStream);
      } catch (Exception e) {
        log.error(e.getMessage(), e);
        throw new Exception("切割文件副本创建失败");
      }
      doc.open();
      // 将pdf按页复制到新建的PDF中
      for (int i = 1; pageNumber <= numberOfPages && i <= splitSize; ++i, pageNumber++) {
        doc.newPage();
        PdfImportedPage page = pdfCopy.getImportedPage(reader, pageNumber);
        pdfCopy.addPage(page);
      }
      doc.close();
      pdfCopy.close();
      String base64encodedString = Base64.getEncoder().encodeToString(byteArrayOutputStream.toByteArray());
      base64List.add(base64encodedString);
    }
    reader.close();
    return base64List;
  }
    public static List<String> splitPdfToBase64V2(InputStream inputStream, int splitSize) throws Exception {
        List<String> base64List = new ArrayList<>();
        PdfReader reader = null;
        Document doc = null;
        PdfCopy pdfCopy = null;

        try {
            reader = new PdfReader(inputStream);
            int numberOfPages = reader.getNumberOfPages();
            int pagesCopied = 0;
            int currentPage = 1;

            while (pagesCopied < numberOfPages) {
                ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
                doc = new Document();
                pdfCopy = new PdfCopy(doc, byteArrayOutputStream);
                doc.open();

                int endPage = Math.min(currentPage + splitSize - 1, numberOfPages);
                for (int i = currentPage; i <= endPage; i++) {
                    PdfImportedPage importedPage = pdfCopy.getImportedPage(reader, i);
                    pdfCopy.addPage(importedPage);
                }

                doc.close();
                String base64encodedString = Base64.getEncoder().encodeToString(byteArrayOutputStream.toByteArray());
                base64List.add(base64encodedString);

                pagesCopied += (endPage - currentPage + 1);
                currentPage = endPage + 1;
            }
        } catch (IOException e) {
            // 处理 IO 异常，例如读取 PDF 文件失败
            throw new Exception("读取PDF文件失败", e);
        } finally {
            // 确保资源被关闭
            try {
                if (doc != null && doc.isOpen()) {
                    doc.close();
                }
            } catch (Exception e) {
                // 处理关闭文档时的异常
                // 这里通常不需要重新抛出异常，因为我们已经在处理资源清理
            }

            try {
                if (pdfCopy != null) {
                    pdfCopy.close();
                }
            } catch (Exception e) {
                // 处理关闭 PdfCopy 时的异常
            }

            try {
                if (reader != null) {
                    reader.close();
                }
            } catch (Exception e) {
                // 处理关闭 PdfReader 时的异常
            }

            // 注意：InputStream 通常不由 PdfReader 关闭，因为它可能由外部管理
            // 如果您需要关闭 InputStream，请在调用此方法之前或之后进行
        }

        return base64List;
    }


    /**
     * 将PDF文件转换为Base64字符串
     *
     * @param inputStream 输入流，包含PDF文件的内容
     * @return Base64编码的字符串
     * @throws IOException 如果读取输入流时发生错误
     */
    public static String convertPdfToBase64(InputStream inputStream) throws IOException {
        // 使用ByteArrayOutputStream来暂存PDF文件的内容
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        byte[] buffer = new byte[1024];
        int bytesRead;

        // 将输入流中的数据读取到ByteArrayOutputStream中
        while ((bytesRead = inputStream.read(buffer)) != -1) {
            byteArrayOutputStream.write(buffer, 0, bytesRead);
        }

        // 将ByteArrayOutputStream中的数据转换为字节数组
        byte[] pdfBytes = byteArrayOutputStream.toByteArray();

        // 使用Base64编码将字节数组转换为字符串
        String base64encodedString = Base64.getEncoder().encodeToString(pdfBytes);

        // 关闭流
        byteArrayOutputStream.close();
        inputStream.close();

        return base64encodedString;
    }
  public List<String> readPdf(String filename) {
    List<String> listArr = new ArrayList<>();
    try {
      PdfReader reader = new PdfReader(filename);
      int pageNum = reader.getNumberOfPages(); // 获得页数
      for (int i = 1; i <= pageNum; i++) { // 只能从第1页开始读
        String textFromPageContent = PdfTextExtractor.getTextFromPage(reader, i);
        String[] splitArray = textFromPageContent.split("\n");
        if (splitArray.length > 0) {
          listArr.addAll(Arrays.asList(splitArray));
        }
      }
    } catch (Exception ex) {
      log.info("readPdf error {}", ex.getMessage(), ex);
    }
    return listArr;
  }
}
