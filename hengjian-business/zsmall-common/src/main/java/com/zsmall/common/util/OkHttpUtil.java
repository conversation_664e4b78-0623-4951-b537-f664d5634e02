package com.zsmall.common.util;

import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.*;
import java.io.IOException;
import java.util.concurrent.TimeUnit;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/6/12 16:50
 */
@Slf4j
public class OkHttpUtil {

    private static volatile OkHttpClient okHttpClient = null;
    private static OkHttpClient client = new OkHttpClient();

    static  {
        // 设置超时时间
        int timeout = 60; // 30s
        OkHttpClient.Builder builder = new OkHttpClient.Builder();
        builder.connectTimeout(timeout, TimeUnit.SECONDS);
        builder.readTimeout(timeout, TimeUnit.SECONDS);
        builder.writeTimeout(timeout, TimeUnit.SECONDS);
        client = builder.build();
    }

    public static String get(String url) throws IOException {
        Request request = new Request.Builder()
            .url(url)
            .build();

        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            } else {
                return response.body().string();
            }
        }
    }

    public static String post(String url, String json) throws IOException {
        MediaType parse = MediaType.parse("application/json; charset=utf-8");
        RequestBody body = RequestBody.create(parse, json);
        Request request = new Request.Builder()
            .url(url)
            .post(body)
            .build();

        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response.code()); // 这里建议使用 response.code() 而不是整个 Response 对象
            } else {
                return response.body().string();
            }
        }
    }

    public static String sendPost(String url, String json) throws IOException {
        MediaType parse = MediaType.parse("application/json; charset=utf-8");
        RequestBody body = RequestBody.create(parse, json);
        Request request = new Request.Builder()
            .url(url)
            .post(body)
            .build();

        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response.code()); // 这里建议使用 response.code() 而不是整个 Response 对象
            } else {
                return response.body().string();
            }
        }
    }
}
