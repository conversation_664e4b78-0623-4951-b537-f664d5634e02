package com.zsmall.common.util;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;

/**
 * <AUTHOR>
 * @date 2022-07-11
 **/
public class XMLUtil {

  /**
   * 字符串非空
   * @param str
   * @return
   */
  public static boolean strIsNotBlank(String str){
    return StrUtil.isNotBlank(str);
  }

  /**
   * Boolean非空且为true
   * @param b
   * @return
   */
  public static boolean booleanIsTrue(Boolean b){
    if(ObjectUtil.isNull(b)){
      return false;
    } else {
      return b;
    }
  }

  /**
   * Boolean非空且为false
   * @param b
   * @return
   */
  public static boolean booleanIsFalse(Boolean b){
    if(ObjectUtil.isNull(b)){
      return false;
    } else {
      return !b;
    }
  }

}
