package com.zsmall.common.util;

import com.alibaba.fastjson.JSON;
import com.zsmall.common.domain.airwallex.AirwallexToken;
import com.zsmall.common.enums.airwallex.AirwallexEnums;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/2/27 10:57
 */
@Slf4j
@Component
public class AirwallexUtil {
    @Value("${distribution.airwallex.clientId}")
    public String airwallexClientId;
    @Value("${distribution.airwallex.apiKey}")
    public String airwallexApiKey;
    @Resource
    private AirwallexApiCallUtils airwallexApiCallUtils;


    /**
     * 功能描述：获取访问令牌
     *
     * @return {@link AirwallexToken }
     * <AUTHOR>
     * @date 2024/02/27
     */
    public AirwallexToken getAccessToken() {
        String url = AirwallexEnums.GET_ACCESS_TOKEN.getUrl()+ AirwallexEnums.GET_ACCESS_TOKEN.getPath();
        HashMap<String, String> headerMap = new HashMap<>();
        headerMap.put("x-api-key",airwallexApiKey);
        headerMap.put("x-client-id",airwallexClientId);
        String result = airwallexApiCallUtils.postApi(headerMap, url, null);

        AirwallexToken airwallexToken = JSON.parseObject(result, AirwallexToken.class);

        return airwallexToken;
    }
}
