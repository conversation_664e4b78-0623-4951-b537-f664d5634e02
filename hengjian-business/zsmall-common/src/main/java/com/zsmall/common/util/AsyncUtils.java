package com.zsmall.common.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.function.Supplier;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/4/9 11:48
 */
@Slf4j
@Component
public class AsyncUtils {
    /**
     * 功能描述：执行异步
     *
     * @param tasks 任务
     * @return {@link CompletableFuture }<{@link Void }>
     * <AUTHOR>
     * @date 2024/04/09
     */
    public static CompletableFuture<Void> executeAsync(Supplier<Boolean>... tasks) {
        CompletableFuture<Void>[] futures = new CompletableFuture[tasks.length];
        for (int i = 0; i < tasks.length; i++) {
            final int index = i;
            futures[i] = CompletableFuture.supplyAsync(() -> {
                boolean result = tasks[index].get();
                log.info("Result of task " + index + ": " + result);
                return null; // 因为是Void类型的CompletableFuture，所以返回null
            });
        }
        return CompletableFuture.allOf(futures);
    }

    /**
     * 功能描述：执行并等待完成
     *
     * @param tasks 任务
     * <AUTHOR>
     * @date 2024/04/09
     */
    public static void executeAndWaitForCompletion(Supplier<Boolean>... tasks) throws InterruptedException, ExecutionException {
        CompletableFuture<Void> allFutures = executeAsync(tasks);
        allFutures.get(); // 等待所有任务完成
    }
}
