package com.zsmall.common.util;

import cn.hutool.core.util.ReUtil;

/**
 * 正则工具类
 * <AUTHOR>
 * @create 2021/8/10 15:42
 */
public class RegexUtil {

  // 非负整数（不可以为零）
  public static final String NON_NEGATIVE_INTEGER = "^[1-9][0-9]*$";
  // 非负整数（可以为零）
  public static final String NON_NEGATIVE_INTEGER_ZERO = "^[0-9]*$";

  // 中国手机号
  public static final String PHONE_NUMBER_ZH_CN = "^(\\+?0?86\\-?)?1[345789]\\d{9}$";
  // 美国手机号
  public static final String PHONE_NUMBER_EN_US = "^(\\+?1)?[2-9]\\d{2}[2-9](?!11)\\d{6}$";
  // 美国邮编
  public static final String US_POSTAL_CODE = "^([0-9]{5})(?:[-\\s]*([0-9]{4}))?$";
  // 日期
  public static final String DATE = "/^\\d{1,4}(-)(1[0-2]|0?[1-9])\\1(0?[1-9]|[1-2]\\d|30|31)$/";

  /**
   * 匹配美国邮编
   * @param content
   * @return
   */
  public static boolean matchUSPostalCode(String content) {
    return ReUtil.isMatch(US_POSTAL_CODE, content);
  }

  /**
   * 匹配电话号码
   * @param content
   * @return
   */
  public static boolean matchPhoneNumber(String content) {
    return ReUtil.isMatch(PHONE_NUMBER_ZH_CN, content) || ReUtil.isMatch(PHONE_NUMBER_EN_US, content);
  }

  /**
   * 匹配数量（不可以为零）
   * @param content
   * @return
   */
  public static boolean matchQuantity(String content) {
    return ReUtil.isMatch(NON_NEGATIVE_INTEGER, content);
  }

  /**
   * 匹配数量（可以为零）
   * @param content
   * @return
   */
  public static boolean matchQuantityCanBeZero(String content) {
    return ReUtil.isMatch(NON_NEGATIVE_INTEGER_ZERO, content);
  }

}
