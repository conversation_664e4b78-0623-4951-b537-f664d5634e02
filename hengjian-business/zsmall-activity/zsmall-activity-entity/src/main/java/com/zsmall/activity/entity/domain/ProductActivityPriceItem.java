package com.zsmall.activity.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hengjian.common.mybatis.core.domain.NoDeptBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
    * 商品活动定价子表（分销商）
    */
@Data
@EqualsAndHashCode(callSuper=true)
@TableName(value = "product_activity_price_item")
public class ProductActivityPriceItem extends NoDeptBaseEntity {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.NONE)
    private Long id;

    /**
     * 子活动编号（分销商）
     */
    @TableField(value = "activity_code")
    private String activityCode;

    /**
     * 主活动编号（供货商）
     */
    @TableField(value = "activity_code_parent")
    private String activityCodeParent;

    /**
     * 商品活动子表（分销商）主键
     */
    @TableField(value = "product_activity_item_id")
    private Long productActivityItemId;

    /**
     * 活动单价（供货商设置）
     */
    @TableField(value = "activity_unit_price")
    private BigDecimal activityUnitPrice;

    /**
     * 活动操作费（供货商设置）
     */
    @TableField(value = "activity_operation_fee")
    private BigDecimal activityOperationFee;

    /**
     * 活动尾程派送费（供货商设置）
     */
    @TableField(value = "activity_final_delivery_fee")
    private BigDecimal activityFinalDeliveryFee;

    /**
     * 活动仓储费（一件/天，供货商设置）
     */
    @TableField(value = "activity_storage_fee")
    private BigDecimal activityStorageFee;

    /**
     * 活动自提价（活动单价+活动操作费）
     */
    @TableField(value = "activity_pick_up_price")
    private BigDecimal activityPickUpPrice;

    /**
     * 活动代发价（活动自提价+活动尾程派送费）
     */
    @TableField(value = "activity_drop_shipping_price")
    private BigDecimal activityDropShippingPrice;

    /**
     * 活动订金单价（活动自提价*一定比例后得出）
     */
    @TableField(value = "activity_deposit_unit_price")
    private BigDecimal activityDepositUnitPrice;

    /**
     * 活动尾款单价（活动自提价-活动订金单价后得出）
     */
    @TableField(value = "activity_balance_unit_price")
    private BigDecimal activityBalanceUnitPrice;

    /**
     * 活动订金总价（活动订金单价*数量得出）
     */
    @TableField(value = "activity_deposit_total_price")
    private BigDecimal activityDepositTotalPrice;

    /**
     * 活动订金剩余
     */
    @TableField(value = "activity_deposit_surplus_price")
    private BigDecimal activityDepositSurplusPrice;

    /**
     * 活动总价（活动自提价*数量得出）
     */
    @TableField(value = "activity_total_price")
    private BigDecimal activityTotalPrice;

    /**
     * 平台单价（平台设置）
     */
    @TableField(value = "platform_unit_price")
    private BigDecimal platformUnitPrice;

    /**
     * 平台操作费（平台设置）
     */
    @TableField(value = "platform_operation_fee")
    private BigDecimal platformOperationFee;

    /**
     * 平台尾程派送费（平台设置）
     */
    @TableField(value = "platform_final_delivery_fee")
    private BigDecimal platformFinalDeliveryFee;

    /**
     * 平台自提价（平台单价+平台操作费）
     */
    @TableField(value = "platform_pick_up_price")
    private BigDecimal platformPickUpPrice;

    /**
     * 平台代发价（平台自提价+平台尾程派送费）
     */
    @TableField(value = "platform_drop_shipping_price")
    private BigDecimal platformDropShippingPrice;

    /**
     * 平台订金单价（平台自提价*一定比例后得出）
     */
    @TableField(value = "platform_deposit_unit_price")
    private BigDecimal platformDepositUnitPrice;

    /**
     * 平台尾款单价（平台自提价-平台订金单价后得出）
     */
    @TableField(value = "platform_balance_unit_price")
    private BigDecimal platformBalanceUnitPrice;

    /**
     * 平台订金总价（平台订金单价*数量后得出，需要用于分销商参加活动时支付）
     */
    @TableField(value = "platform_deposit_total_price")
    private BigDecimal platformDepositTotalPrice;

    /**
     * 平台订金剩余
     */
    @TableField(value = "platform_deposit_surplus_price")
    private BigDecimal platformDepositSurplusPrice;

    /**
     * 平台总价（平台自提价*数量后得出，表示分销商参与此次活动的总价）
     */
    @TableField(value = "platform_total_price")
    private BigDecimal platformTotalPrice;
}
