package com.zsmall.activity.entity.domain.vo.productActivity;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-09-15
 **/
@Data
public class StockLockDetailVo {
    /**
     * 活动ID
     */
    private String activityID;
    /**
     * 活动类型
     */
    private String activityType;
    /**
     * 活动时间
     */
    private String createDateTime;
    /**
     * 活动状态
     */
    private String status;
    /**
     * 状态(中文)
     */
    private String status_zh_CN;
    /**
     * 状态(英文)
     */
    private String status_en_US;
    /**
     * 活动信息
     */
    private ActivityInformation activityInformation;
    /**
     * 锁货商品信息
     */
    private Product product;
    /**
     * 分销商锁货出单信息
     */
    private List<Order> orderList;

    /**
     * 活动信息
     */
    @Data
    public static class ActivityInformation {
        /**
         * 商品总库存
         */
        private Integer quantity;
        /**
         * 锁货总库存
         */
        private Integer stockLockTotalQuantity;
        /**
         * 锁货单价
         */
        private String stockLockUnitPrice;
        /**
         * 总售出商品数
         */
        private Integer stockLockSold;
        /**
         * 免仓期
         */
        private Integer freeStoragePeriod;
        /**
         * 总订金
         */
        private String totalDeposit;
        /**
         * 已支付总订金
         */
        private String paidTotalDeposit;
        /**
         * 总仓储费
         */
        private String totalStorageFee;
        /**
         * 最小锁货数量
         */
        private Integer minimumStockLockQuantity;
        /**
         * 活动期限
         */
        private Integer activityDays;
        /**
         * 操作费
         */
        private String operationFee;
        /**
         * 尾程派送费（件）
         */
        private String finalDeliveryFee;
        /**
         * 库存信息
         */
        private List<ActivityStockVo> inventoryList;
    }

    /**
     * 锁货商品信息
     */
    public static class Product {
        /**
         * 图片
         */
        private String productImg;
        /**
         * 商品名
         */
        private String productName;
        /**
         * sku
         */
        private String productSku;
        /**
         * 商品编码
         */
        private String productCode;
        /**
         * Item NO.
         */
        private String itemNo;
        /**
         * 仓库ID
         */
        private String warehouseID;
        /**
         * 仓储费（件/天）
         */
        private String storageFee;
        /**
         * 商品价格
         */
        private String price;
    }

    /**
     * 订单信息
     */
    public static class Order {
        /**
         * 剩余时间（天）
         */
        private Long surplusDays;
        /**
         * 订单创建时间
         */
        private String orderCreateTime;
        /**
         * 订单状态
         */
        private String orderStatus;
        private String status_zh_CN;
        private String status_en_US;
        /**
         * 锁货数量
         */
        private Integer orderStockLockQuantity;
        /**
         * 已售数量
         */
        private Integer orderStockLockSold;
        /**
         * 已支付订金
         */
        private String paidDeposit;
        /**
         * 已支付仓储费
         */
        private String paidStorageFee;
        /**
         * 分销商剩余免仓期（天）
         */
        private Long freeStoragePeriod;
    }
}
