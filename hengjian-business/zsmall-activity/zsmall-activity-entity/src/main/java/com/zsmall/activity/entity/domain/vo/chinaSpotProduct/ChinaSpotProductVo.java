package com.zsmall.activity.entity.domain.vo.chinaSpotProduct;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 响应体-国内现货商品
 *
 * <AUTHOR>
 * @date 2023/1/12
 */
@Data
@NoArgsConstructor
public class ChinaSpotProductVo {

    /**
     * 商品编号
     */
    private String productCode;
    /**
     * 商品名称
     */
    private String productName;
    /**
     * 图片URL
     */
    private String imageShowUrl;
    /**
     * SKU数量
     */
    private Integer skuQuantity;
    /**
     * 库存数量
     */
    private Long inventoryQuantity;
    /**
     * 创建时间
     */
    private String createDateTime;
    /**
     * 审核状态
     */
    private Integer reviewStatus;
    /**
     * 供货商编号（员工才能看到）
     */
    private String supCode;

}
