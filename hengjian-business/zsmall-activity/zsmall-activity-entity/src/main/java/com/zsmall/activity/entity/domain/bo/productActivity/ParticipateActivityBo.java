package com.zsmall.activity.entity.domain.bo.productActivity;

import com.zsmall.common.constant.ValidationMessage;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 请求体-参与活动
 **/
@Data
public class ParticipateActivityBo {
    /**
     * 活动ID
     */
    @NotBlank(message = ValidationMessage.API_REQUIRED)
    private String activityID;
    /**
     * 活动类型
     */
    @NotBlank(message = ValidationMessage.API_REQUIRED)
    private String activityType;
    /**
     * 锁货仓库库存
     */
    @NotEmpty(message = ValidationMessage.API_REQUIRED)
    private List<ActivityStockBo> stockList;
    /**
     * 支付密码
     */
    private String paymentPassword;
}
