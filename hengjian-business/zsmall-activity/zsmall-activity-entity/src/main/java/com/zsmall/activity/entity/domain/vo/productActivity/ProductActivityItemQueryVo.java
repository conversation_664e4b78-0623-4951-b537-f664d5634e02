package com.zsmall.activity.entity.domain.vo.productActivity;

import lombok.Data;

import java.util.Date;

/**
 * 响应体-商品活动子表信息
 **/
@Data
public class ProductActivityItemQueryVo {
    /**
     * 图片
     */
    private String productImg;
    /**
     * 商品名
     */
    private String productName;
    /**
     * 商品sku唯一编码
     */
    private String productSkuCode;
    /**
     * 活动ID
     */
    private String activityID;
    /**
     * 活动类型
     */
    private String activityType;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 活动状态
     */
    private String activityState;
    /**
     * 活动商品剩余数量
     */
    private Integer remainingPieces;
    /**
     * 到期时间
     */
    private String remainingTime;
}
