package com.zsmall.activity.entity.domain.bo.productActivity;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-09-15
 **/
@Data
public class DistributorsBuyoutBo {
    /**
     * 活动ID
     */
    private String activityID;
    /**
     * 活动类型
     */
    private String activityType;
    /**
     * 仓库库存
     */
    private List<ActivityStockBo> inventoryList;
    /**
     * 支付密码
     */
    private String paymentPassword;
    /**
     * 免密设置
     */
    private Boolean paymentSetting;
}
