package com.zsmall.activity.entity.domain.vo.productActivity;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 响应体-商品活动详情基类
 *
 * <AUTHOR>
 * @date 2023/7/27
 */
@Data
public class ProductActivityDetailBaseVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 活动ID
     */
    private String activityID;

    /**
     * 所属主活动ID
     */
    private String activityParentID;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 活动类型
     */
    private String activityType;

    /**
     * 活动状态
     */
    private String activityState;

    /**
     * ItemNo
     */
    private String productSkuCode;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 活动价
     */
    private BigDecimal unitPrice;

    /**
     * 操作费
     */
    private BigDecimal operationFee;

    /**
     * 尾程派送费
     */
    private BigDecimal finalDeliveryFee;

    /**
     * 仓储费
     */
    private BigDecimal storageFee;

    /**
     * 已支付定金
     */
    private BigDecimal paidDeposit;

    /**
     * 活动仓库库存
     */
    private List<ActivityStockVo> stockList;

    /**
     * 商品信息
     */
    private List<ActivitySimpleProductInfoVo> product;
}
