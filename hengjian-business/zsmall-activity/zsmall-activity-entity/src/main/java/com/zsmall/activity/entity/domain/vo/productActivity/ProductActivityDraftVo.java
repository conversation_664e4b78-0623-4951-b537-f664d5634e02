package com.zsmall.activity.entity.domain.vo.productActivity;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 响应体-促销活动草稿
 *
 * <AUTHOR>
 * @date 2022/11/1
 */
@Data
public class ProductActivityDraftVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 活动ID
     */
    private String activityID;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 活动类型
     */
    private String activityType;

    /**
     * 活动状态（Draft-保存为草稿，UnderReview-直接提交审核）
     */
    private String activityState;

    /**
     * ItemNo
     */
    private String productSkuCode;

    /**
     * 活动单价
     */
    private BigDecimal activityUnitPrice;

    /**
     * 操作费（件）
     */
    private BigDecimal activityOperationFee;

    /**
     * 尾程派送费（件）
     */
    private BigDecimal activityFinalDeliveryFee;

    /**
     * 仓储费（每天每件）
     */
    private BigDecimal activityStorageFee;

    /**
     * 活动最小参与数量
     */
    private Integer quantityMinimum;

    /**
     * 活动商品可用总数
     */
    private Integer quantityTotal;

    /**
     * 活动期限（天）
     */
    private Integer activityTime;

    /**
     * 免仓期（天）
     */
    private Integer freeStoragePeriod;

    /**
     * 活动商品信息
     */
    private ActivityProductInfoVo product;

    /**
     * 活动仓库库存
     */
    private List<ActivityStockVo> stockList;

}
