package com.zsmall.activity.entity.domain.vo.productActivity;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 响应体-活动商品信息
 *
 * <AUTHOR>
 * @date 2023/7/26
 */
@Data
public class ActivityProductInfoVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 图片
     */
    private String productImg;
    /**
     * 商品名
     */
    private String productName;
    /**
     * sku
     */
    private String productSku;
    /**
     * Item NO.
     */
    private String productSkuCode;
    /**
     * 总库存
     */
    private Integer stockTotal;
    /**
     * 支持的物流：All-都支持，PickUpOnly-仅支持自提，DropShippingOnly-仅支持代发
     */
    private String supportedLogistics;
    /**
     * 自提价
     */
    private BigDecimal pickUpPrice;
    /**
     * 代发价
     */
    private BigDecimal dropShippingPrice;
    /**
     * 库存信息
     */
    private List<ActivityStockVo> stockList;

}
