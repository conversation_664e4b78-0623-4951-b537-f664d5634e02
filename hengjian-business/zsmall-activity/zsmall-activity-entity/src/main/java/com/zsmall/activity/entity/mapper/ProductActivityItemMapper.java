package com.zsmall.activity.entity.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hengjian.common.tenant.mapper.TenantMapperPlus;
import com.zsmall.activity.entity.domain.ProductActivityItem;
import com.zsmall.activity.entity.domain.bo.productActivity.ProductActivityQueryBo;
import com.zsmall.activity.entity.domain.dto.ExpiringActivityItemDTO;
import com.zsmall.activity.entity.domain.dto.StorageFeeDisDTO;
import com.zsmall.activity.entity.domain.vo.productActivity.*;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface ProductActivityItemMapper extends TenantMapperPlus<ProductActivityItem, ProductActivityItem> {

    List<ProductActivityItem> queryByAll(ProductActivityItem productActivityItem);

    IPage<ProductActivityItemQueryVo> queryPage(@Param("bo") ProductActivityQueryBo bo, Page page);

    List<ActivityOrderInfoVo> queryActivityOrderInfoForSupplier(@Param("activityCodeParent") String activityCodeParent);

    List<ActivityOrderInfoVo> queryActivityOrderInfoForManager(@Param("activityCodeParent") String activityCodeParent);

    ProductActivityDetailDVo queryDetailVo(@Param("activityCode") String activityCode);

    @InterceptorIgnore(tenantLine = "true")
    long isStockLockToBuyout(@Param("activityItemId") Long activityItemId);

    @InterceptorIgnore(tenantLine = "true")
    IPage<ExpiringActivityItemQueryVo> getExpiringActivityItem(@Param("dto") ExpiringActivityItemDTO dto, Page page);

    @InterceptorIgnore(tenantLine = "true")
    List<ExpiringActivityItemQueryVo> getExpiringActivityItemOneThird(@Param("today") Date today, @Param("tenantId") String tenantId);

    List<ActivityItemSimpleVo> querySelectByProductSkuCode(@Param("productSkuCode") String productSkuCode, @Param("activityType") String activityType, @Param("channelType") String channelType);

    @InterceptorIgnore(tenantLine = "true")
    List<StorageFeeDisDTO> queryStorageFeeBulkDTO(String nowDate);

    @InterceptorIgnore(tenantLine = "true")
    List<ProductActivityItem> queryWillExpiredActivity();

    @InterceptorIgnore(tenantLine = "true")
    Integer queryItemSurplusByActivity(@Param("activityCodeParent") String activityCodeParent);

    @InterceptorIgnore(tenantLine = "true")
    List<ProductActivityItem> queryInProgressAndParentCanceled(@Param("productCode") String productCode);

}
