package com.zsmall.activity.entity.domain.dto;

import com.zsmall.activity.entity.domain.*;
import com.zsmall.activity.entity.domain.bo.productActivity.ActivityStockBo;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * 促销活动开锁操作通用DTO
 * <AUTHOR>
 * @date 2022/11/1
 */
@Data
@Builder
public class ActivityLockDTO {

  // 分销商租户编号
  private String distributorId;
  // 供货商租户编号
  private String supplierId;
  // 供货商活动信息实体类
  private ProductActivity activity;
  // 分销商活动信息实体类（生成日志时需要）
  private ProductActivityItem activityItem;

  // 供货商锁货信息实体类
  private ProductActivityStockLock stockLock;
  // 分销商锁货信息实体类（生成日志时需要）
  private ProductActivityStockLockItem stockLockItem;

  // 供货商圈货信息实体类
  private ProductActivityBuyout buyout;
  // 分销商圈货信息实体类（生成日志时需要）
  private ProductActivityBuyoutItem buyoutItem;

  // 需求总数量
  private Integer quantityRequired;
  // 最小起订量
  private Integer quantityMinimum;

  // 活动库存集合（请求实体）
  private List<ActivityStockBo> reqStockList;

  // 活动库存集合（供货商）
  private List<ProductActivityStock> stockList;
  // 活动库存集合（分销商）
  private List<ProductActivityStockItem> stockItemList;

}
