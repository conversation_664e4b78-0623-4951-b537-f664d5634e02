package com.zsmall.activity.entity.domain.vo.productActivity;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-09-15
 **/
@Data
public class StockLockDetailByDisVo {
    /**
     * 活动ID
     */
    private String activityID;
    /**
     * 活动类型
     */
    private String activityType;
    /**
     * 活动时间
     */
    private String createDateTime;
    private Long stockLockDaysSurplus;
    /**
     * 活动信息
     */
    private ActivityInformation activityInformation;
    /**
     * 锁货商品信息
     */
    private Product product;

    /**
     * 活动信息
     */
    @Data
    public static class ActivityInformation {
        /**
         * 锁货总库存
         */
        private Integer stockLockTotalQuantity;
        /**
         * 锁货单价
         */
        private String stockLockUnitPrice;
        /**
         * 锁货总价
         */
        private String TotalOrdersAmount;
        /**
         * 操作费
         */
        private String operationFee;
        /**
         * 仓储费（件/天）
         */
        private String storageFee;
        /**
         * 尾程派送费（件）
         */
        private String finalDeliveryFee;
        /**
         * 免仓期剩余时间
         */
        private long freeStoragePeriodSurplus;
        /**
         * 总订金
         */
        private String totalDeposit;
        /**
         * 余额是否充足（false-不足，true-充足）
         */
        private Boolean balanceEnough;
        /**
         * 库存信息
         */
        private List<ActivityStockVo> inventoryList;
        /**
         * 运输方式
         */
        private String shippingMethod;
    }

    /**
     * 锁货商品信息
     */
    public static class Product {
        /**
         * 图片
         */
        private String productImg;
        /**
         * 商品名
         */
        private String productName;
        /**
         * sku
         */
        private String productSku;
        /**
         * 商品编码
         */
        private String productCode;
        /**
         * Item NO.
         */
        private String itemNo;
        /**
         * 仓库ID
         */
        private String warehouseID;
        /**
         * 商品剩余库存
         */
        private Integer quantitySurplus;
        /**
         * 总售出商品数
         */
        private Integer quantityPaid;
        /**
         * 尾款单价
         */
        private String balanceUnitPrice;
        /**
         * 状态
         */
        private String status;
        /**
         * 状态(中文)
         */
        private String status_zh_CN;
        /**
         * 状态(英文)
         */
        private String status_en_US;
    }
}
