package com.zsmall.activity.entity.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zsmall.activity.entity.domain.ProductActivityCheckout;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Date;

public interface ProductActivityCheckoutMapper extends BaseMapper<ProductActivityCheckout> {

    BigDecimal sumStorageFeeAmount(@Param("actItemId") Long actItemId, @Param("activityCode") String activityCode, @Param("distributorId") String distributorId);

    BigDecimal sumCheckoutAmountByParams(@Param("checkoutType") String checkoutType, @Param("startDate") Date startDate, @Param("endDate") Date endDate);

}
