package com.zsmall.activity.entity.domain.vo.productActivity;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-09-15
 **/
@Data
public class BuyoutDetailVo {
    /**
     * 活动ID
     */
    private String activityID;
    /**
     * 活动类型
     */
    private String activityType;
    /**
     * 活动时间
     */
    private String createDateTime;
    /**
     * 活动状态
     */
    private String status;
    /**
     * 状态(中文)
     */
    private String status_zh_CN;
    /**
     * 状态(英文)
     */
    private String status_en_US;
    /**
     * 活动信息
     */
    private ActivityInformation activityInformation;
    /**
     * 圈货商品信息
     */
    private Product product;
    /**
     * 分销商圈货出单信息
     */
    private List<BuyoutDetailVo.Order> orderList;

    /**
     * 活动信息
     */
    @Data
    public static class ActivityInformation {
        /**
         * 最小圈货数
         */
        private Integer buyoutQuantityMinimum;
        /**
         * 圈货总库存
         */
        private Integer buyoutTotalQuantity;
        /**
         * 已圈货总库存
         */
        private Integer buyoutTotalQuantityAlready;
        /**
         * 圈货单价
         */
        private String buyoutUnitPrice;
        /**
         * 圈货总价
         */
        private String buyoutTotalPrice;
        /**
         * 操作费
         */
        private String operationFee;
        /**
         * 仓储费（件/天）
         */
        private String storageFee;
        /**
         * 总仓储费
         */
        private String totalStorageFee;
        /**
         * 尾程派送费（件）
         */
        private String finalDeliveryFee;
        /**
         * 总售出商品数
         */
        private Integer buyoutSold;
        /**
         * 余额是否充足（false-不足，true-充足）
         */
        private Boolean balanceEnough;
        /**
         * 库存信息
         */
        private List<BuyoutInventoryVo> inventoryList;
        /**
         * 运输方式
         */
        private String shippingMethod;
    }

    /**
     * 圈货商品信息
     */
    public static class Product {
        /**
         * 图片
         */
        private String productImg;
        /**
         * 商品名
         */
        private String productName;
        /**
         * sku
         */
        private String productSku;
        /**
         * 商品编码
         */
        private String productCode;
        /**
         * Item NO.
         */
        private String itemNo;
        /**
         * 仓库ID
         */
        private String warehouseID;
        /**
         * 商品剩余库存
         */
        private Integer quantitySurplus;
        /**
         * 总售出商品数
         */
        private Integer quantityPaid;
        /**
         * 仓储费（件/天）
         */
        private String storageFee;
        /**
         * 状态
         */
        private String status;
        /**
         * 状态(中文)
         */
        private String status_zh_CN;
        /**
         * 状态(英文)
         */
        private String status_en_US;
    }

    /**
     * 订单信息
     */
    public static class Order {
        /**
         * 分销商活动编号
         */
        private String bulkActivityCode;
        /**
         * 剩余时间（天）
         */
        private Long surplusDays;
        /**
         * 订单创建时间
         */
        private String orderCreateTime;
        /**
         * 订单状态
         */
        private String orderStatus;
        /**
         * 圈货数量
         */
        private Integer orderBuyoutQuantity;
        /**
         * 已售数量
         */
        private Integer orderBuyoutSold;
        /**
         * 已支付订金
         */
        private String paidDeposit;
        /**
         * 已支付仓储费
         */
        private String paidStorageFee;
        /**
         * 分销商ID
         */
        private String userCode;
    }
}
