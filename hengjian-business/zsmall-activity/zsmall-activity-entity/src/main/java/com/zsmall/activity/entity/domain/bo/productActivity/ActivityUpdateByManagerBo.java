package com.zsmall.activity.entity.domain.bo.productActivity;

import com.zsmall.common.constant.ValidationMessage;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2022-09-15
 **/
@Data
public class ActivityUpdateByManagerBo {
    /**
     * 活动ID
     */
    @NotBlank(message = ValidationMessage.API_REQUIRED)
    private String activityID;

    /**
     * 活动类型
     */
    @NotBlank(message = ValidationMessage.API_REQUIRED)
    private String activityType;

    /**
     * 当前活动状态：UnderReview-审核中，Canceling-取消中
     */
    @NotBlank(message = ValidationMessage.API_REQUIRED)
    private String originState;

    /**
     * 是否同意状态变更
     */
    @NotBlank(message = ValidationMessage.API_REQUIRED)
    private Boolean approved;
}
