package com.zsmall.activity.entity.domain.vo.productActivity;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 响应体-Marketplace商品活动信息
 *
 * <AUTHOR>
 * @date 2023/7/31
 */
@Data
public class MpProductActivityVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 锁货
     */
    private List<MpProductActivitySelectVo> StockLock;

    /**
     * 圈货
     */
    private List<MpProductActivitySelectVo> Buyout;

}
