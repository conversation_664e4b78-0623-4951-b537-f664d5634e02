package com.zsmall.activity.entity.domain.vo.productActivity;

import com.hengjian.common.mybatis.core.page.TableDataInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 响应体-商品活动资金
 **/
@Data
@EqualsAndHashCode(callSuper=false)
public class ProductActivityPriceTableVo<T> extends TableDataInfo<T> {

    /**
     * 已收货活动订单总金额
     */
    private BigDecimal platformTotalAmount;

}
