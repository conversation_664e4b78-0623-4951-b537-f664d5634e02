package com.zsmall.activity.entity.domain.dto;

import com.zsmall.common.annotaion.ExcelFieldAnnotation;
import com.zsmall.common.domain.dto.ExcelBaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 批量发货导入DTO
 *
 * <AUTHOR>
 * @date 2023/6/8
 */
@Data
@EqualsAndHashCode(callSuper=false)
public class ChinaSpotProductExcelDTO extends ExcelBaseDTO {


    @ExcelFieldAnnotation(required = true)
    private String spu;

    @ExcelFieldAnnotation(column = "类目")
    private String categoryName;

    @ExcelFieldAnnotation(column = "商品名称")
    private String productName;

    @ExcelFieldAnnotation(column = "最小起订量")
    private Integer minimumQuantity;

    @ExcelFieldAnnotation(required = true)
    private String sku;

    @ExcelFieldAnnotation(required = true)
    private String specValue;

    @ExcelFieldAnnotation(required = true)
    private String warehouseBelongCity;

    @ExcelFieldAnnotation(required = true)
    private Integer quantity;

    @ExcelFieldAnnotation(required = true)
    private BigDecimal referencePrice;

    @ExcelFieldAnnotation(required = true)
    private BigDecimal length;

    @ExcelFieldAnnotation(required = true)
    private BigDecimal width;

    @ExcelFieldAnnotation(required = true)
    private BigDecimal height;

    @ExcelFieldAnnotation(required = true)
    private BigDecimal weight;

    @ExcelFieldAnnotation(required = true)
    private BigDecimal packLength;

    @ExcelFieldAnnotation(required = true)
    private BigDecimal packWidth;

    @ExcelFieldAnnotation(required = true)
    private BigDecimal packHeight;

    @ExcelFieldAnnotation(required = true)
    private BigDecimal packWeight;

    @ExcelFieldAnnotation(required = true)
    private String imageUrl1;

    @ExcelFieldAnnotation(column = "商品图片2")
    private String imageUrl2;

    @ExcelFieldAnnotation(column = "商品图片3")
    private String imageUrl3;

    @ExcelFieldAnnotation(column = "商品图片4")
    private String imageUrl4;

    @ExcelFieldAnnotation(column = "商品图片5")
    private String imageUrl5;

    @ExcelFieldAnnotation(column = "描述")
    private String description;






}
