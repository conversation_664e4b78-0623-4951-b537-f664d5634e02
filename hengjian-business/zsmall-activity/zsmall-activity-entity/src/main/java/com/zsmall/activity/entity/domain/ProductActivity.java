package com.zsmall.activity.entity.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.hengjian.common.tenant.core.NoDeptTenantEntity;
import com.zsmall.common.enums.productActivity.ProductActivityStateEnum;
import com.zsmall.common.enums.productActivity.ProductActivityTypeEnum;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * 商品活动表（供货商）
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName(value = "product_activity")
public class ProductActivity extends NoDeptTenantEntity {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.NONE)
    private Long id;

    /**
     * 活动编号（供货商）
     */
    @TableField(value = "activity_code")
    private String activityCode;

    /**
     * 活动名称
     */
    @TableField(value = "activity_name")
    private String activityName;

    /**
     * 活动类型
     */
    @TableField(value = "activity_type")
    private ProductActivityTypeEnum activityType;

    /**
     * 活动状态（Draft-草稿、UnderReview-审核中、Published-已发布、InProgress-进行中、Canceling-取消中、NotApproved-未通过审核、Canceled-已取消）
     */
    @TableField(value = "activity_state")
    private ProductActivityStateEnum activityState;

    /**
     * 商品名称
     */
    @TableField(value = "product_name")
    private String productName;

    /**
     * 商品图片
     */
    @TableField(value = "product_img")
    private String productImg;

    /**
     * 商品唯一编码
     */
    @TableField(value = "product_code")
    private String productCode;

    /**
     * 商品Sku唯一编码
     */
    @TableField(value = "product_sku_code")
    private String productSkuCode;

    /**
     * 商品Sku
     */
    @TableField(value = "product_sku")
    private String productSku;

    /**
     * 活动商品可用总数
     */
    @TableField(value = "quantity_total")
    private Integer quantityTotal;

    /**
     * 活动商品已消耗总数
     */
    @TableField(value = "quantity_already")
    private Integer quantityAlready;

    /**
     * 活动最小起订量
     */
    @TableField(value = "quantity_minimum")
    private Integer quantityMinimum;

    /**
     * 活动商品剩余总数
     */
    @TableField(value = "quantity_surplus")
    private Integer quantitySurplus;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

    /**
     * 重置剩余数量
     */
    public void resetSurplus() {
        this.quantitySurplus = this.quantityTotal - this.quantityAlready;
    }
}
