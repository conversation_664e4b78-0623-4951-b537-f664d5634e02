package com.zsmall.activity.entity.domain.vo.productActivity;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-09-15
 **/
@Data
public class StockLockDetailByMdVo {
    /**
     * 活动ID
     */
    private String activityID;
    /**
     * 活动类型
     */
    private String activityType;
    /**
     * 活动时间
     */
    private String createDateTime;
    /**
     * 活动有效时间
     */
    private Integer stockLockDays;
    /**
     * 活动信息
     */
    private ActivityInformation activityInformation;
    /**
     * 锁货商品信息
     */
    private Product product;

    private List<Order> orderList;

    /**
     * 活动信息
     */
    @Data
    public static class ActivityInformation {
        /**
         * 已锁货总库存
         */
        private Integer stockLockTotalQuantity;
        /**
         * 锁货单价
         */
        private String stockLockUnitPrice;
        /**
         * 锁货总价
         */
        private String TotalOrdersAmount;
        /**
         * 仓储费（件/天）
         */
        private String storageFee;
        /**
         * 已支付订金
         */
        private String plaidDeposit;
        /**
         * 总订金
         */
        private String totalDeposit;
        /**
         * 活动期限
         */
        private Integer activityDays;
        /**
         * 操作费
         */
        private String operationFee;
        /**
         * 尾程派送费（件）
         */
        private String finalDeliveryFee;
        /**
         * 库存信息
         */
        private List<ActivityStockVo> inventoryList;
    }

    /**
     * 锁货商品信息
     */
    public static class Product {
        /**
         * 图片
         */
        private String productImg;
        /**
         * 商品名
         */
        private String productName;
        /**
         * sku
         */
        private String productSku;
        /**
         * 商品编码
         */
        private String productCode;
        /**
         * Item NO.
         */
        private String itemNo;
        /**
         * 仓库ID
         */
        private String warehouseID;
        /**
         * 商品剩余库存
         */
        private Integer stockLockSurplus;
        /**
         * 总售出商品数
         */
        private Integer stockLockSold;
        /**
         * 状态
         */
        private String status;
        /**
         * 状态(中文)
         */
        private String status_zh_CN;
        /**
         * 状态(英文)
         */
        private String status_en_US;
        /**
         * 原价
         */
        private String price;
    }

    /**
     * 订单信息
     */
    public static class Order {
        /**
         * 分销商活动编号
         */
        private String bulkActivityCode;
        /**
         * 分销商剩余时间（天）
         */
        private Long surplusDays;
        /**
         * 分销商锁货创建时间
         */
        private String orderCreateTime;
        /**
         * 分销商锁货状态
         */
        private String orderStatus;
        /**
         * 分销商锁货数量
         */
        private Integer orderStockLockQuantity;
        /**
         * 分销商已售数量
         */
        private Integer orderStockLockSold;
        /**
         * 分销商已支付订金
         */
        private String paidDeposit;
        /**
         * 分销商已支付仓储费
         */
        private String paidStorageFee;
        /**
         * 分销商剩余免仓期（天）
         */
        private Long freeStoragePeriod;
        /**
         * 分销商ID
         */
        private String userCode;
    }
}
