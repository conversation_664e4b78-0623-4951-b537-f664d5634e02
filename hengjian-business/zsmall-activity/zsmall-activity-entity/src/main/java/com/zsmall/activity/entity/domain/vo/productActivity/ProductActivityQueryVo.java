package com.zsmall.activity.entity.domain.vo.productActivity;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022-09-15
 **/
@Data
public class ProductActivityQueryVo {
    /**
     * 供应商ID
     */
    private String supplierId;
    /**
     * 图片
     */
    private String productImg;
    /**
     * 商品名
     */
    private String productName;
    /**
     * 商品sku唯一编码
     */
    private String productSkuCode;
    /**
     * 活动ID
     */
    private String activityID;
    /**
     * 活动类型
     */
    private String activityType;
    /**
     * 活动名称
     */
    private String activityName;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 活动状态
     */
    private String activityState;
    /**
     * 活动商品剩余数量
     */
    private Integer remainingPieces;
    /**
     * 到期时间
     */
    private String remainingTime;
    /**
     * 活动单价（供货商设置）
     */
    private String activityUnitPrice;
    /**
     * 活动仓储费（一件/天，供货商设置）
     */
    private String activityStorageFee;
    /**
     * 平台单价（平台设置）
     */
    private String platformUnitPrice;
    /**
     * 平台尾程派送费（平台设置）
     */
    private String platformFinalDeliveryFee;
}
