package com.zsmall.activity.entity.domain.vo.productActivity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.LinkedList;
import java.util.List;

/**
 * 响应体-分销商活动库存信息
 *
 * <AUTHOR>
 * @date 2023/5/11
 */
@Data
public class ActivityStockDVo {
    /**
     * 库存信息集合
     */
    private List<StockDVo> stockList = new LinkedList<>();

    @Getter
    @Setter
    @AllArgsConstructor
    public class StockDVo {
        private String warehouseSystemCode;
        private Integer quantityMax;
        private Integer quantityTotal;
        private Integer quantitySurplus;
        private Integer quantitySold;
    }

    /**
     * 增加库存信息
     *
     * @param warehouseSystemCode 仓库系统编号
     * @param quantityTotal       当前仓库总库存
     * @param quantitySold        当前仓库已售出数
     */
    public void addInventory(String warehouseSystemCode, Integer quantityMax, Integer quantityTotal, Integer quantitySurplus, Integer quantitySold) {
        stockList.add(new StockDVo(warehouseSystemCode, quantityMax, quantityTotal, quantitySurplus, quantitySold));
    }
}
