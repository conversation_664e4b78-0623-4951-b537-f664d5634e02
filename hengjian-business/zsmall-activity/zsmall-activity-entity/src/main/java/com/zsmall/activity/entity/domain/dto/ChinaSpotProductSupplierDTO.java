package com.zsmall.activity.entity.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.hengjian.common.excel.annotation.ExcelI18nFormat;
import lombok.Data;

import java.math.BigDecimal;


/**
 * 国内现货商品视图对象 china_spot_product
 *
 * <AUTHOR>
 * @date 2023-08-15
 */
@Data
@ExcelIgnoreUnannotated
public class ChinaSpotProductSupplierDTO {

    private static final long serialVersionUID = 1L;

    @ExcelProperty(value = "商品编号")
    @ExcelI18nFormat(code = "zsmall.excel.chinaSpotProduct.productCode")
    private String productCode;

    @ExcelProperty(value = "类目")
    @ExcelI18nFormat(code = "zsmall.excel.chinaSpotProduct.categoryName")
    private String categoryName;

    @ExcelProperty(value = "商品名称")
    @ExcelI18nFormat(code = "zsmall.excel.chinaSpotProduct.productName")
    private String productName;

    @ExcelProperty(value = "最小起订量")
    @ExcelI18nFormat(code = "zsmall.excel.chinaSpotProduct.minimumQuantity")
    private String minimumQuantity;

    @ExcelProperty(value = "SKU")
    @ExcelI18nFormat(code = "zsmall.excel.chinaSpotProduct.sku")
    private String sku;

    @ExcelProperty(value = "属性")
    @ExcelI18nFormat(code = "zsmall.excel.chinaSpotProduct.fixedSpecValue")
    private String fixedSpecValue;

    @ExcelProperty(value = "仓库所在城市")
    @ExcelI18nFormat(code = "zsmall.excel.chinaSpotProduct.warehouseBelongCity")
    private String warehouseBelongCity;

    @ExcelProperty(value = "数量")
    @ExcelI18nFormat(code = "zsmall.excel.chinaSpotProduct.quantity")
    private Long quantity;

    @ExcelProperty(value = "长度")
    @ExcelI18nFormat(code = "zsmall.excel.chinaSpotProduct.length")
    private String length;

    @ExcelProperty(value = "宽度")
    @ExcelI18nFormat(code = "zsmall.excel.chinaSpotProduct.width")
    private String width;

    @ExcelProperty(value = "高度")
    @ExcelI18nFormat(code = "zsmall.excel.chinaSpotProduct.height")
    private String height;

    @ExcelProperty(value = "重量")
    @ExcelI18nFormat(code = "zsmall.excel.chinaSpotProduct.weight")
    private String weight;

    @ExcelProperty(value = "打包长度")
    @ExcelI18nFormat(code = "zsmall.excel.chinaSpotProduct.packLength")
    private String packLength;

    @ExcelProperty(value = "打包宽度")
    @ExcelI18nFormat(code = "zsmall.excel.chinaSpotProduct.packWidth")
    private String packWidth;

    @ExcelProperty(value = "打包高度")
    @ExcelI18nFormat(code = "zsmall.excel.chinaSpotProduct.packHeight")
    private String packHeight;

    @ExcelProperty(value = "打包重量")
    @ExcelI18nFormat(code = "zsmall.excel.chinaSpotProduct.packWeight")
    private String packWeight;

    @ExcelProperty(value = "参考供价")
    @ExcelI18nFormat(code = "zsmall.excel.chinaSpotProduct.referencePrice")
    private BigDecimal referencePrice;

    @ExcelProperty(value = "商品图片1")
    @ExcelI18nFormat(code = "zsmall.excel.chinaSpotProduct.image1")
    private String image1;

    @ExcelProperty(value = "商品图片2")
    @ExcelI18nFormat(code = "zsmall.excel.chinaSpotProduct.image2")
    private String image2;

    @ExcelProperty(value = "商品图片3")
    @ExcelI18nFormat(code = "zsmall.excel.chinaSpotProduct.image3")
    private String image3;

    @ExcelProperty(value = "商品图片4")
    @ExcelI18nFormat(code = "zsmall.excel.chinaSpotProduct.image4")
    private String image4;

    @ExcelProperty(value = "商品图片5")
    @ExcelI18nFormat(code = "zsmall.excel.chinaSpotProduct.image5")
    private String image5;

    @ExcelProperty(value = "描述")
    @ExcelI18nFormat(code = "zsmall.excel.chinaSpotProduct.description")
    private String description;
}
