package com.zsmall.activity.entity.domain.vo.productActivity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 响应体-商品活动详情（分销商）
 *
 * <AUTHOR>
 * @date 2023/7/27
 */
@Data
@EqualsAndHashCode(callSuper=false)
public class ProductActivityDetailDVo extends ProductActivityDetailBaseVo {

    /**
     * 子活动商品成交总数
     */
    private Integer quantityTotal;

    /**
     * 子活动商品已售数量
     */
    private Integer quantitySold;

    /**
     * 子活动商品剩余总数
     */
    private Integer quantitySurplus;

    /**
     * 活动时间剩余（天）
     */
    private Integer surplusDays;

    /**
     * 免仓期剩余（天）
     */
    private Integer freeStoragePeriodSurplus;

    /**
     * 尾款单价
     */
    private BigDecimal balanceUnitPrice;

}
