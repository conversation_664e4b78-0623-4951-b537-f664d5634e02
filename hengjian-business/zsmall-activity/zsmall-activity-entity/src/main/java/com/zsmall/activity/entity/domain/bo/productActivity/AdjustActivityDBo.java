package com.zsmall.activity.entity.domain.bo.productActivity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Map;

/**
 * 请求体-员工调整分销商活动
 *
 * <AUTHOR>
 * @date 2023/4/21
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AdjustActivityDBo {
    /**
     * 分销商的活动ID
     */
    private String activityCode;
    /**
     * 库存调整信息：key-仓库系统编号，value-调整后库存数
     */
    private Map<String, Integer> stockAdjustMap;
    /**
     * 处理方式：Refund-退款至分销商钱包，Deduction-从分销商钱包扣除
     */
    private String processMethod;
    /**
     * 差价金额
     */
    private BigDecimal differencePrice;
    /**
     * 交易备注
     */
    private String remark;

    public enum ProcessMethod {
        /**
         * 退还
         */
        Refund,
        /**
         * 扣除
         */
        Deduction,
        ;
    }

    public ProcessMethod getProcessMethod() {
        ProcessMethod processMethod = null;
        try {
            processMethod = ProcessMethod.valueOf(this.processMethod);
        } catch (IllegalArgumentException e) {
            e.printStackTrace();
        }
        return processMethod;
    }
}
