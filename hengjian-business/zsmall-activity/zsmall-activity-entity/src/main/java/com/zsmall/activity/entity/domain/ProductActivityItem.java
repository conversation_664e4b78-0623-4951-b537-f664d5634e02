package com.zsmall.activity.entity.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hengjian.common.tenant.core.NoDeptTenantEntity;
import com.zsmall.common.enums.productActivity.ProductActivityItemStateEnum;
import com.zsmall.common.enums.productActivity.ProductActivityTypeEnum;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * 商品活动子表（分销商）
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName(value = "product_activity_item")
public class ProductActivityItem extends NoDeptTenantEntity {
    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 子活动编号（分销商）
     */
    @TableField(value = "activity_code")
    private String activityCode;

    /**
     * 主活动编号（供货商）
     */
    @TableField(value = "activity_code_parent")
    private String activityCodeParent;

    /**
     * 活动类型
     */
    @TableField(value = "activity_type")
    private ProductActivityTypeEnum activityType;

    /**
     * 活动状态（InProgress-进行中，Completed-已完成，Expired-已过期，Canceled-已取消）
     */
    @TableField(value = "activity_state")
    private ProductActivityItemStateEnum activityState;

    /**
     * 商品名称
     */
    @TableField(value = "product_name")
    private String productName;

    /**
     * 商品图片
     */
    @TableField(value = "product_img")
    private String productImg;

    /**
     * 商品唯一编码
     */
    @TableField(value = "product_code")
    private String productCode;

    /**
     * 商品Sku唯一编码
     */
    @TableField(value = "product_sku_code")
    private String productSkuCode;

    /**
     * 商品Sku
     */
    @TableField(value = "product_sku")
    private String productSku;

    /**
     * 子活动商品成交总数
     */
    @TableField(value = "quantity_total")
    private Integer quantityTotal;

    /**
     * 子活动商品已售数量
     */
    @TableField(value = "quantity_sold")
    private Integer quantitySold;

    /**
     * 子活动商品剩余总数
     */
    @TableField(value = "quantity_surplus")
    private Integer quantitySurplus;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


}
