package com.zsmall.activity.entity.domain.bo.productActivity;

import com.zsmall.common.constant.ValidationMessage;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 请求体-活动信息更新
 **/
@Data
public class ActivityUpdateBo {

    /**
     * 活动ID
     */
    @NotBlank(message = ValidationMessage.API_REQUIRED)
    private String activityID;

    /**
     * 活动类型
     */
    @NotBlank(message = ValidationMessage.API_REQUIRED)
    private String activityType;

    /**
     * 活动状态：UnderReview-审核中，Canceling-取消中
     */
    @NotBlank(message = ValidationMessage.API_REQUIRED)
    private String activityState;

    /**
     * 是否同意状态变更（员工使用接口时必填）
     */
    private Boolean approved;
}
