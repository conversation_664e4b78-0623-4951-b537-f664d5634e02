package com.zsmall.activity.entity.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.hengjian.common.tenant.core.NoDeptTenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
    * 圈货活动信息子表（分销商）
    */
@Data
@EqualsAndHashCode(callSuper=true)
@TableName(value = "product_activity_buyout_item")
public class ProductActivityBuyoutItem extends NoDeptTenantEntity {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.NONE)
    private Long id;

    /**
     * 子活动编号（分销商）
     */
    @TableField(value = "activity_code")
    private String activityCode;

    /**
     * 主活动编号（供货商）
     */
    @TableField(value = "activity_code_parent")
    private String activityCodeParent;

    /**
     * 商品活动子表（分销商）主键
     */
    @TableField(value = "product_activity_item_id")
    private Long productActivityItemId;

    /**
     * 锁货活动信息子表（分销商）主键（锁货转圈货会存）
     */
    @TableField(value = "product_activity_stock_lock_item_id")
    private Long productActivityStockLockItemId;

    /**
     * 圈货总数
     */
    @TableField(value = "buyout_quantity")
    private Integer buyoutQuantity;

    /**
     * 已售数量
     */
    @TableField(value = "buyout_sold")
    private Integer buyoutSold;

    /**
     * 剩余数量
     */
    @TableField(value = "buyout_surplus")
    private Integer buyoutSurplus;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;
}
