package com.zsmall.activity.entity.domain.vo.productActivity;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 响应体-Marketplace商品活动可选信息
 *
 * <AUTHOR>
 * @date 2023/7/31
 */
@Data
public class MpProductActivitySelectVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 商品主活动编号
     */
    private String activityID;

    /**
     * 商品活动类型
     */
    private String activityType;

    /**
     * 商品图片
     */
    private String productImg;

    /**
     * 商品SKU编号
     */
    private String productSkuCode;

    /**
     * 活动商品剩余总数
     */
    private Integer quantitySurplus;

    /**
     * 活动最小起订量
     */
    private Integer quantityMinimum;

    /**
     * 活动期限
     */
    private Integer activityTime;

    /**
     * 免仓期
     */
    private Integer freeStoragePeriod;

    /**
     * 活动仓储费
     */
    private BigDecimal activityStorageFee;

    /**
     * 平台单价（平台设置）
     */
    private BigDecimal platformUnitPrice;

    /**
     * 平台操作费（平台设置）
     */
    private BigDecimal platformOperationFee;

    /**
     * 平台尾程派送费（平台设置）
     */
    private BigDecimal platformFinalDeliveryFee;

    /**
     * 平台自提价（平台单价+平台操作费）
     */
    private BigDecimal platformPickUpPrice;

    /**
     * 平台代发价（平台自提价+平台尾程派送费）
     */
    private BigDecimal platformDropShippingPrice;

    /**
     * 平台订金单价（平台自提价*一定比例后得出）
     */
    private BigDecimal platformDepositUnitPrice;

    /**
     * 平台尾款单价（平台自提价-平台订金单价后得出）
     */
    private BigDecimal platformBalanceUnitPrice;

    /**
     * 活动仓库库存
     */
    private List<ActivityStockVo> stockList;

}
