package com.zsmall.activity.entity.domain.dto;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;

/**
 * 临期活动DTO
 **/
@Data
@Slf4j
public class ExpiringActivityItemDTO {

    private String tenantId;

    private String queryType;

    private String queryValue;

    private Date endDate;

    private Date today;

    private Date plusDay;

    public void formatDTO(String tenantId, String queryType, String queryValue, Integer endDay, Integer remainingTime) {

        if (!(StrUtil.isNotBlank(queryType) && StrUtil.isNotBlank(queryValue))) {
            queryType = null;
            queryValue = null;
        }
        Date endDate = null;
        if (endDay != null) {
            Date today = DateUtil.beginOfDay(new Date());
            endDate = DateUtil.offsetDay(today, endDay);
        }

        this.tenantId = tenantId;
        this.queryType = queryType;
        this.queryValue = queryValue;
        this.endDate = endDate;
        this.today = DateUtil.beginOfDay(new Date());
        this.plusDay = DateUtil.offsetDay(today, remainingTime);
        log.info("today = {} , plusDay = {}", today, plusDay);

    }

}
