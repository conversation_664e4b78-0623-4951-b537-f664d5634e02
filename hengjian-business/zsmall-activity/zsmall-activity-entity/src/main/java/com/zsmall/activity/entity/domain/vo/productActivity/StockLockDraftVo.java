package com.zsmall.activity.entity.domain.vo.productActivity;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-09-15
 **/
@Data
public class StockLockDraftVo {
    /**
     * 活动名称
     */
    private String activityName;
    /**
     * 活动类型
     */
    private String activityType;
    /**
     * 活动ID
     */
    private String activityID;
    /**
     * 活动期限（天）
     */
    private Integer activityTime;
    /**
     * 活动状态
     */
    private String status;
    /**
     * 锁货库存
     */
    private Integer stockQuantity;
    /**
     * 分销商最小锁货数量
     */
    private Integer minLockNum;
    /**
     * 仓储费（每天每件）
     */
    private BigDecimal storageFee;
    /**
     * 锁货价
     */
    private BigDecimal stockLockUnitPrice;
    /**
     * 免仓期（天）
     */
    private Integer freeStoragePeriod;
    /**
     * 操作费
     */
    private BigDecimal operationFee;
    /**
     * 尾程派送费（件）
     */
    private BigDecimal finalDeliveryFee;
    /**
     * 商品信息
     */
    private Product product;
    /**
     * 库存信息
     */
    private List<Inventory> inventoryList;

    /**
     * 锁货商品信息
     */
    @Data
    public static class Product {
        /**
         * 图片
         */
        private String productImg;
        /**
         * 商品名
         */
        private String productName;
        /**
         * sku
         */
        private String productSku;
        /**
         * Item NO.
         */
        private String itemNo;
        /**
         * 仓库ID
         */
        private String warehouseID;
        /**
         * 商品库存
         */
        private Integer quantity;
        /**
         * 商品单价
         */
        private BigDecimal price;
        /**
         * 库存信息
         */
        private List<Inventory> inventoryList;
    }

    /**
     * 仓库信息
     */
    public static class Inventory {

        /**
         * 仓库ID
         */
        private String warehouseID;

        /**
         * 库存（每个仓库）
         */
        private Integer quantity;

        /**
         * 锁货库存（每个仓库）
         */
        private Integer stockQuantity;
    }
}
