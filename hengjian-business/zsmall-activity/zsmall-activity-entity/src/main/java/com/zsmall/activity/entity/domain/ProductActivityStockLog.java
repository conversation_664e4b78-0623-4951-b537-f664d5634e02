package com.zsmall.activity.entity.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.hengjian.common.mybatis.core.domain.NoDeptBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
    * 商品活动库存日志表（供应商）
    */
@Data
@EqualsAndHashCode(callSuper=true)
@TableName(value = "product_activity_stock_log")
public class ProductActivityStockLog extends NoDeptBaseEntity {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.NONE)
    private Long id;

    /**
     * 活动编号（供货商）
     */
    @TableField(value = "activity_code")
    private String activityCode;

    /**
     * 商品活动表（供货商）主键
     */
    @TableField(value = "product_activity_id")
    private Long productActivityId;

    /**
     * 商品活动库存表（供应商）主键
     */
    @TableField(value = "product_activity_inventory_id")
    private Long productActivityInventoryId;

    /**
     * 仓库系统编号
     */
    @TableField(value = "warehouse_system_code")
    private String warehouseSystemCode;

    /**
     * 总数量
     */
    @TableField(value = "quantity_total")
    private Integer quantityTotal;

    /**
     * 已售数量
     */
    @TableField(value = "quantity_sold")
    private Integer quantitySold;

    /**
     * 剩余数量
     */
    @TableField(value = "quantity_surplus")
    private Integer quantitySurplus;

    /**
     * 逻辑删除字段（1-已删除，0-未删除）
     */
    @TableField(value = "delete_mark")
    private Integer deleteMark;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;
}
