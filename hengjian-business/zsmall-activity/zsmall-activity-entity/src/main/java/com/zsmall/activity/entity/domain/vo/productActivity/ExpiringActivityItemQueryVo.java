package com.zsmall.activity.entity.domain.vo.productActivity;

import lombok.Data;

/**
 * 响应体-临期子活动
 **/
@Data
public class ExpiringActivityItemQueryVo {
    /**
     * 分销商ID
     */
    private String dTenantId;
    /**
     * 图片
     */
    private String productImg;
    /**
     * 商品名
     */
    private String productName;
    /**
     * 子活动ID（分销商）
     */
    private String activityID;
    /**
     * 所属主活动ID（供应商）
     */
    private String activityParentID;
    /**
     * 活动类型（英）
     */
    private String activityType;
    /**
     * Item NO.
     */
    private String productSkuCode;
    /**
     * 剩余数量
     */
    private Integer quantitySurplus;
    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 到期时间
     */
    private String expiryDateTime;
    /**
     * 过期时间
     */
    private String remainingTime;
    /**
     * 活动状态
     */
    private String activityState;

}
