package com.zsmall.activity.entity.domain.vo.chinaSpotProduct;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 响应体-国内现货商品Sku详情
 *
 * <AUTHOR>
 * @date 2023/1/12
 */
@Data
@NoArgsConstructor
public class RespChinaSpotProductSkuDetailBody {

    /**
     * 商品编号
     */
    private String sku;
    /**
     * 商品名称
     */
    private String productSkuCode;
    /**
     * 固定规格值
     */
    private String fixedSpecValue;
    /**
     * 数量
     */
    private Integer quantity;
    /**
     * 参考供价
     */
    private String referencePrice;
    /**
     * 仓库所在城市
     */
    private String warehouseBelongCity;
    /**
     * 长
     */
    private String length;
    /**
     * 宽
     */
    private String width;
    /**
     * 高
     */
    private String height;
    /**
     * 长度单位
     */
    private String lengthUnit;
    /**
     * 重量
     */
    private String weight;
    /**
     * 重量单位
     */
    private String weightUnit;
    /**
     * 打包的长
     */
    private String packLength;
    /**
     * 打包的宽
     */
    private String packWidth;
    /**
     * 打包的高
     */
    private String packHeight;
    /**
     * 打包长度单位
     */
    private String packLengthUnit;
    /**
     * 打包的重量
     */
    private String packWeight;
    /**
     * 打包重量单位
     */
    private String packWeightUnit;
    /**
     * 描述
     */
    private String description;
    /**
     * 图片集合
     */
    private List<String> imageList;

}
