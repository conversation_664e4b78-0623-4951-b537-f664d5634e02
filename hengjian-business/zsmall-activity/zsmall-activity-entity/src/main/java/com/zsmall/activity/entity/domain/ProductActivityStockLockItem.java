package com.zsmall.activity.entity.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.hengjian.common.tenant.core.NoDeptTenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
    * 锁货活动信息子表（分销商）
    */
@Data
@EqualsAndHashCode(callSuper=true)
@TableName(value = "product_activity_stock_lock_item")
public class ProductActivityStockLockItem extends NoDeptTenantEntity {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.NONE)
    private Long id;

    /**
     * 子活动编号（分销商）
     */
    @TableField(value = "activity_code")
    private String activityCode;

    /**
     * 主活动编号（供货商）
     */
    @TableField(value = "activity_code_parent")
    private String activityCodeParent;

    /**
     * 商品活动子表（分销商）主键
     */
    @TableField(value = "product_activity_item_id")
    private Long productActivityItemId;

    /**
     * 锁货总数
     */
    @TableField(value = "stock_lock_quantity")
    private Integer stockLockQuantity;

    /**
     * 已售数量
     */
    @TableField(value = "stock_lock_sold")
    private Integer stockLockSold;

    /**
     * 剩余数量
     */
    @TableField(value = "stock_lock_surplus")
    private Integer stockLockSurplus;

    /**
     * 锁货时间（天）
     */
    @TableField(value = "stock_lock_days")
    private Integer stockLockDays;

    /**
     * 免仓期（天）
     */
    @TableField(value = "free_storage_period")
    private Integer freeStoragePeriod;

    /**
     * 免仓截止日期
     */
    @TableField(value = "free_storage_expiry_date")
    private Date freeStorageExpiryDate;

    /**
     * 到期时间（根据商家设置的天数计算得出）
     */
    @TableField(value = "expiry_date_time")
    private Date expiryDateTime;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;
}
