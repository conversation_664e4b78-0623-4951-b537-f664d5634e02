package com.zsmall.activity.entity.domain.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * DTO-分销商仓储费数据
 **/
@Data
public class StorageFeeDisDTO {

    /**
     * 供货商活动ID
     */
    private Long productActivityId;

    /**
     * 分销商子活动ID
     */
    private Long productActivityItemId;

    /**
     * 分销商租户编号
     */
    private String dTenantId;

    /**
     * 供货商租户编号
     */
    private String sTenantId;

    /**
     * 仓储费
     */
    private BigDecimal storageFee;

    /**
     * 子活动剩余数量
     */
    private Integer quantitySurplus;
}
