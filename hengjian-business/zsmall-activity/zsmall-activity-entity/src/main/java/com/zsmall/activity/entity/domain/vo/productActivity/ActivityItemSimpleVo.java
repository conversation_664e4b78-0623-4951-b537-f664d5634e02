package com.zsmall.activity.entity.domain.vo.productActivity;

import lombok.Data;

import java.io.Serializable;

/**
 * 响应体-子活动简略信息
 *
 * <AUTHOR>
 * @date 2023/8/2
 */
@Data
public class ActivityItemSimpleVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 子活动ID
     */
    private String activityID;

    /**
     * 活动单价
     */
    private String activityUnitPrice;

    /**
     * 剩余时间
     */
    private String remainingTime;

    /**
     * 剩余数量
     */
    private String quantitySurplus;

}
