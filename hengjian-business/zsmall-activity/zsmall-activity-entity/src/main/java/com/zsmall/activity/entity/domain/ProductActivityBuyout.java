package com.zsmall.activity.entity.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.hengjian.common.mybatis.core.domain.NoDeptBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 圈货活动信息表（供货商）
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName(value = "product_activity_buyout")
public class ProductActivityBuyout extends NoDeptBaseEntity {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.NONE)
    private Long id;

    /**
     * 活动编号（供货商）
     */
    @TableField(value = "activity_code")
    private String activityCode;

    /**
     * 商品活动表（供货商）主键
     */
    @TableField(value = "product_activity_id")
    private Long productActivityId;

    /**
     * 可圈货总数
     */
    @TableField(value = "buyout_quantity_total")
    private Integer buyoutQuantityTotal;

    /**
     * 已圈货总数
     */
    @TableField(value = "buyout_quantity_already")
    private Integer buyoutQuantityAlready;

    /**
     * 圈货剩余数
     */
    @TableField(value = "buyout_quantity_surplus")
    private Integer buyoutQuantitySurplus;

    /**
     * 最小圈货数
     */
    @TableField(value = "buyout_quantity_minimum")
    private Integer buyoutQuantityMinimum;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

    /**
     * 重置剩余数量
     */
    public void resetSurplus() {
        this.buyoutQuantitySurplus = this.buyoutQuantityTotal - this.buyoutQuantityAlready;
    }
}
