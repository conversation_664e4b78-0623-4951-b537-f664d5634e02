package com.zsmall.activity.entity.domain.bo.productActivity;

import com.zsmall.common.constant.ValidationMessage;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * 促销活动信息基类
 *
 * <AUTHOR>
 * @date 2022/11/1
 */
@Data
public class ProductActivityBaseBo {

    /**
     * 活动ID
     */
    private String activityID;

    /**
     * 活动名称
     */
    @NotBlank(message = ValidationMessage.API_REQUIRED)
    private String activityName;

    /**
     * 活动类型
     */
    @NotBlank(message = ValidationMessage.API_REQUIRED)
    private String activityType;

    /**
     * 活动状态（Draft-保存为草稿，UnderReview-直接提交审核）
     */
    @NotBlank(message = ValidationMessage.API_REQUIRED)
    private String activityState;

    /**
     * ItemNo
     */
    @NotBlank(message = ValidationMessage.API_REQUIRED)
    private String productSkuCode;

    /**
     * 活动单价
     */
    @NotNull(message = ValidationMessage.API_REQUIRED)
    private BigDecimal activityUnitPrice;

    /**
     * 操作费（件）
     */
    @NotNull(message = ValidationMessage.API_REQUIRED)
    private BigDecimal activityOperationFee;

    /**
     * 尾程派送费（件）
     */
    @NotNull(message = ValidationMessage.API_REQUIRED)
    private BigDecimal activityFinalDeliveryFee;

    /**
     * 仓储费（每天每件）
     */
    @NotNull(message = ValidationMessage.API_REQUIRED)
    private BigDecimal activityStorageFee;

    /**
     * 活动最小参与数量
     */
    @NotNull(message = ValidationMessage.API_REQUIRED)
    private Integer quantityMinimum;

    /**
     * 活动商品可用总数
     */
    private Integer quantityTotal;

    /**
     * 活动期限（天）
     */
    private Integer activityTime;

    /**
     * 免仓期（天）
     */
    private Integer freeStoragePeriod;

    /**
     * 活动仓库库存
     */
    @NotEmpty(message = ValidationMessage.API_REQUIRED)
    private List<ActivityStockBo> stockList;

}
