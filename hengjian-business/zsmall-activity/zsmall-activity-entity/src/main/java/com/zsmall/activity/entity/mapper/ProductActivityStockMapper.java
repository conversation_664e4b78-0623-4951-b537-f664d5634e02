package com.zsmall.activity.entity.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zsmall.activity.entity.domain.ProductActivityStock;
import com.zsmall.activity.entity.domain.vo.productActivity.ActivityStockVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ProductActivityStockMapper extends BaseMapper<ProductActivityStock> {

    List<ActivityStockVo> queryActivityStock(@Param("activityCode") String activityCode);

    List<ActivityStockVo> queryActivityStockSurplus(@Param("activityCode") String activityCode);

    Integer queryActivityStockOccupation(@Param("productSkuCode") String productSkuCode, @Param("warehouseSystemCode") String warehouseSystemCode);

    Long countValidByWarehouseSystemCode(@Param("warehouseSystemCode") String warehouseSystemCode);

}
