package com.zsmall.activity.entity.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.hengjian.common.tenant.core.NoDeptTenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

/**
 * 国内现货商品SKU表
 * @TableName china_spot_product_sku
 */
@TableName(value ="china_spot_product_sku")
@Data
@EqualsAndHashCode(callSuper=false)
public class ChinaSpotProductSku extends NoDeptTenantEntity {
    /**
     *
     */
    @TableId(value = "id", type = IdType.NONE)
    private Long id;

    /**
     * 清货商品id
     */
    private Long chinaSpotProductId;

    /**
     * sku
     */
    private String sku;

    /**
     * 当前SKU规格组合（JSON，格式为规格名1：规格值，规格名2：规格值）
     */
    private String skuSpec;

    /**
     * 固定属性值
     */
    private String fixedSpecValue;

    /**
     * 参考供价
     */
    private BigDecimal referencePrice;

    /**
     * 商品sku名
     */
    private String productSkuName;

    /**
     * 商品sku编号（ItemNo）
     */
    private String productSkuCode;

    /**
     * 描述
     */
    private String description;

    /**
     * 长
     */
    private BigDecimal length;

    /**
     * 宽
     */
    private BigDecimal width;

    /**
     * 高
     */
    private BigDecimal height;

    /**
     * 长度单位
     */
    private String lengthUnit;

    /**
     * 重量
     */
    private BigDecimal weight;

    /**
     * 重量单位
     */
    private String weightUnit;

    /**
     * 打包的长
     */
    private BigDecimal packLength;

    /**
     * 打包的宽
     */
    private BigDecimal packWidth;

    /**
     * 打包的高
     */
    private BigDecimal packHeight;

    /**
     * 打包的长度单位
     */
    private String packLengthUnit;

    /**
     * 打包的重量
     */
    private BigDecimal packWeight;

    /**
     * 打包的重量单位
     */
    private String packWeightUnit;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @TableField(exist = false)
    private List<ChinaSpotProductSkuStock> chinaSpotProductSkuStockList;

    @TableField(exist = false)
    private List<ChinaSpotProductSkuAttachment> chinaSpotProductSkuAttachmentList;

}
