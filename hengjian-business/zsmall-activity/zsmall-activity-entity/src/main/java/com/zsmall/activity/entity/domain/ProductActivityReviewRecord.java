package com.zsmall.activity.entity.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.hengjian.common.tenant.core.NoDeptTenantEntity;
import com.zsmall.common.enums.productActivity.ProductActivityReviewState;
import com.zsmall.common.enums.productActivity.ProductActivityStateEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
    * 商品活动审核记录表
    */
@Data
@EqualsAndHashCode(callSuper=true)
@TableName(value = "product_activity_review_record")
public class ProductActivityReviewRecord extends NoDeptTenantEntity {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.NONE)
    private Long id;

    /**
     * 商品活动表（供货商）主键
     */
    @TableField(value = "product_activity_id")
    private Long productActivityId;

    /**
     * 活动初始状态（Draft-草稿、UnderReview-审核中、Published-已发布、InProgress-进行中、Canceling-取消中、NotApproved-未通过审核、Canceled-已取消）
     */
    @TableField(value = "activity_origin_state")
    private ProductActivityStateEnum activityOriginState;

    /**
     * 审核租户编号（管理员）
     */
    @TableField(value = "review_manager")
    private String reviewManager;

    /**
     * 审核时间
     */
    @TableField(value = "review_time")
    private Date reviewTime;

    /**
     * 审核意见
     */
    @TableField(value = "review_opinion")
    private String reviewOpinion;

    /**
     * 审核状态（1-审核中，2-通过，3-驳回）
     */
    @TableField(value = "review_state")
    private ProductActivityReviewState reviewState;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;
}
