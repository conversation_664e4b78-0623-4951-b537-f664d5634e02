package com.zsmall.activity.entity.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.hengjian.common.mybatis.core.domain.NoDeptBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
    * 商品活动库存子表（分销商）
    */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper=true)
@TableName(value = "product_activity_stock_item")
public class ProductActivityStockItem extends NoDeptBaseEntity {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.NONE)
    private Long id;

    /**
     * 子活动编号（分销商）
     */
    @TableField(value = "activity_code")
    private String activityCode;

    /**
     * 商品活动子表（分销商）主键
     */
    @TableField(value = "product_activity_item_id")
    private Long productActivityItemId;

    /**
     * 库存所在国家（不存在于数据库，仅作为参数携带）
     */
    @TableField(exist = false)
    private String country;

    /**
     * 仓库系统编号
     */
    @TableField(value = "warehouse_system_code")
    private String warehouseSystemCode;

    /**
     * 总数量
     */
    @TableField(value = "quantity_total")
    private Integer quantityTotal;

    /**
     * 已售数量
     */
    @TableField(value = "quantity_sold")
    private Integer quantitySold;

    /**
     * 剩余数量
     */
    @TableField(value = "quantity_surplus")
    private Integer quantitySurplus;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;




}
