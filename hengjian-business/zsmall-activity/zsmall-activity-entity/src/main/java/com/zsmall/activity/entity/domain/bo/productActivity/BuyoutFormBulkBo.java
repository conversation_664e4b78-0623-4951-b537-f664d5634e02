package com.zsmall.activity.entity.domain.bo.productActivity;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-10-09
 **/
@Data
public class BuyoutFormBulkBo {
    /**
     * 锁货活动ID（分销商）
     */
    private String activityID;
    /**
     * 锁货仓库库存
     */
    private List<BuyoutInventoryBo> inventoryList;
    /**
     * 支付密码
     */
    private String paymentPassword;
    /**
     * 免密设置
     */
    private Boolean paymentSetting;
}
