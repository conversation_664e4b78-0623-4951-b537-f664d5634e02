package com.zsmall.activity.entity.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zsmall.activity.entity.domain.ChinaSpotProduct;
import org.apache.ibatis.annotations.Param;

/**
* <AUTHOR>
* @description 针对表【china_spot_product(国内现货商品表)】的数据库操作Mapper
* @createDate 2023-08-15 16:40:48
* @Entity com.zsmall.activity.entity.domain.ChinaSpotProduct
*/
public interface ChinaSpotProductMapper extends BaseMapper<ChinaSpotProduct> {


    Page<ChinaSpotProduct> queryPage(@Param("queryType") String queryType,
                                       @Param("queryValue") String queryValue, Page<ChinaSpotProduct> page);

    Long countByProductCode(@Param("productCode") String productCode);


}




