package com.zsmall.activity.entity.domain.vo.productActivity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.hengjian.common.excel.annotation.ExcelI18nFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 响应体-商品活动资金
 **/
@Data
public class ProductActivityPriceVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 供应商ID
     */
    @ExcelProperty(value = "供应商", order = 25)
    @ExcelI18nFormat(code = "zsmall.excel.supplier")
    private String supplierId;
    /**
     * 商品sku唯一编码
     */
    @ExcelProperty(value = "Item No.", order = 15)
    @ExcelI18nFormat(code = "zsmall.excel.productSkuCode")
    private String productSkuCode;
    /**
     * 活动ID
     */
    @ExcelProperty(value = "活动编码", order = 10)
    @ExcelI18nFormat(code = "zsmall.excel.activityID")
    private String activityID;
    /**
     * 活动类型
     */
    @ExcelProperty(value = "活动类型", order = 5)
    @ExcelI18nFormat(code = "zsmall.excel.activityType")
    private String activityType;
    /**
     * 开始时间
     */
    @ExcelProperty(value = "开始时间", order = 20)
    @ExcelI18nFormat(code = "zsmall.excel.startTime")
    private Date startTime;
    /**
     * 活动状态
     */
    @ExcelProperty(value = "活动状态", order = 50)
    @ExcelI18nFormat(code = "zsmall.excel.activityState")
    private String activityState;
    /**
     * 商品原本自提价（非活动）
     */
    @ExcelProperty(value = "商品原本自提价（非活动）", order = 30)
    @ExcelI18nFormat(code = "zsmall.excel.productPickUpPrice")
    private String productPickUpPrice;

    /**
     * 活动自提价（平台）
     */
    @ExcelProperty(value = "活动自提价（平台）", order = 35)
    @ExcelI18nFormat(code = "zsmall.excel.platformPickUpPrice")
    private String platformPickUpPrice;
    /**
     * 活动尾程派送费（平台）
     */
    @ExcelProperty(value = "活动尾程派送费（平台）", order = 40)
    @ExcelI18nFormat(code = "zsmall.excel.platformFinalDeliveryFee")
    private String platformFinalDeliveryFee;
    /**
     * 活动仓储费
     */
    @ExcelProperty(value = "活动仓储费", order = 45)
    @ExcelI18nFormat(code = "zsmall.excel.activityStorageFee")
    private String activityStorageFee;
}
