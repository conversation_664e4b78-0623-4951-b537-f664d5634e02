package com.zsmall.activity.entity.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hengjian.common.log.annotation.InMethodLog;
import com.zsmall.activity.entity.domain.ProductActivityPrice;
import com.zsmall.activity.entity.domain.vo.productActivity.ActivitySimpleProductInfoVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ProductActivityPriceMapper extends BaseMapper<ProductActivityPrice> {

    @InMethodLog("根据商品活动定价表实体类查询")
    List<ProductActivityPrice> queryByAll(ProductActivityPrice productActivityPrice);

    @InterceptorIgnore(tenantLine = "true")
    List<ActivitySimpleProductInfoVo> querySimpleProductInfoForSupplier(@Param("activityCode") String activityCode);

    @InterceptorIgnore(tenantLine = "true")
    List<ActivitySimpleProductInfoVo> querySimpleProductInfoForManager(@Param("activityCode") String activityCode);
}
