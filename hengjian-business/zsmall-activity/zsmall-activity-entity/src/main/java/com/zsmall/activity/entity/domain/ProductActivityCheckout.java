package com.zsmall.activity.entity.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.hengjian.common.mybatis.core.domain.NoDeptBaseEntity;
import com.zsmall.common.enums.productActivity.ActivityCheckoutTypeEnum;
import com.zsmall.common.enums.productActivity.CheckoutPayEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 商品活动结账表
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "product_activity_checkout")
public class ProductActivityCheckout extends NoDeptBaseEntity {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.NONE)
    private Long id;

    /**
     * 供货商租户编号
     */
    @TableField(value = "supplier_tenant_id")
    private String supplierTenantId;

    /**
     * 分销商租户编号
     */
    @TableField(value = "distributor_tenant_id")
    private String distributorTenantId;

    /**
     * 商品活动表（供货商）主键
     */
    @TableField(value = "product_activity_id")
    private Long productActivityId;

    /**
     * 商品活动子表（分销商）主键
     */
    @TableField(value = "product_activity_item_id")
    private Long productActivityItemId;

    /**
     * 结账类型（仓储费，违约金等）
     */
    @TableField(value = "checkout_type")
    private ActivityCheckoutTypeEnum checkoutType;

    /**
     * 结账单价
     */
    @TableField(value = "checkout_unit_price")
    private BigDecimal checkoutUnitPrice;

    /**
     * 结账单价（平台）
     */
    @TableField(value = "platform_checkout_unit_price")
    private BigDecimal platformCheckoutUnitPrice;

    /**
     * 结账数量
     */
    @TableField(value = "checkout_quantity")
    private Integer checkoutQuantity;

    /**
     * 结账总金额
     */
    @TableField(value = "checkout_amount")
    private BigDecimal checkoutAmount;

    /**
     * 结账总金额（平台）
     */
    @TableField(value = "platform_checkout_amount")
    private BigDecimal platformCheckoutAmount;

    /**
     * 分销商支付状态（0-未支付，1-已支付，2-支付失败）
     */
    @TableField(value = "distributor_pay")
    private CheckoutPayEnum distributorPay;

    /**
     * 支付失败原因
     */
    @TableField(value = "pay_fail_massage")
    private String payFailMassage;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;
}
