package com.zsmall.activity.entity.domain.vo.productActivity;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 响应体-活动成交信息
 *
 * <AUTHOR>
 * @date 2023/7/26
 */
@Data
public class ActivityOrderInfoVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 成交时间
     */
    private Date createTime;

    /**
     * 子活动状态
     */
    private String activityItemState;

    /**
     * 活动编号
     */
    private String activityID;

    /**
     * 所属主活动编号
     */
    private String activityParentID;

    /**
     * 子活动参与者编号
     */
    private String tenantId;

    /**
     * 剩余时间（天）
     */
    private Integer surplusDays;

    /**
     * 子活动成交总数量
     */
    private Integer quantityTotal;

    /**
     * 子活动出单数量
     */
    private Integer quantitySold;

    /**
     * 已支付仓储费
     */
    private BigDecimal paidStorageFee;

    /**
     * 已支付定金
     */
    private BigDecimal paidDeposit;

}
