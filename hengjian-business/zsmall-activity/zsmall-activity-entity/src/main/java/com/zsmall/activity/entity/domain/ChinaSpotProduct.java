package com.zsmall.activity.entity.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.hengjian.common.tenant.core.NoDeptTenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 国内现货商品表
 * @TableName china_spot_product
 */
@TableName(value ="china_spot_product")
@Data
@EqualsAndHashCode(callSuper=false)
public class ChinaSpotProduct extends NoDeptTenantEntity {
    /**
     *
     */
    @TableId(value = "id", type = IdType.NONE)
    private Long id;

    /**
     * 供货商租户编号
     */
    private String tenantId;

    /**
     * 归属分类id
     */
    private Long belongCategoryId;

    /**
     * 商品名
     */
    private String productName;

    /**
     * 商品编号
     */
    private String productCode;

    /**
     * 所有规格（JSON，格式为规格名：规格值数组）
     */
    private String allSpec;

    /**
     * 可能的规格组合（JSON，格式为规格值1：规格值2数组）
     */
    private String possibleSpec;

    /**
     * 支持选择数量
     */
    private Integer selectableQuantity;

    /**
     * 禁售渠道（JSON数组）
     */
    private Object forbiddenChannel;

    /**
     * 商品SKU数量
     */
    private Integer skuQuantity;

    /**
     * 最小购买数量
     */
    private Integer minimumQuantity;

    /**
     * 国内现货商品审核状态（1-UnderReview-审核中，2-NotApproved-未通过审核，3-Reviewed-已审核）
     */
    private Integer reviewState;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @TableField(exist = false)
    private List<ChinaSpotProductSku> chinaSpotProductSkuList;
}
