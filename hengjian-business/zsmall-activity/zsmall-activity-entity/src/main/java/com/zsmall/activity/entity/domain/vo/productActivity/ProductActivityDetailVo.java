package com.zsmall.activity.entity.domain.vo.productActivity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

/**
 * 响应体-促销活动草稿
 *
 * <AUTHOR>
 * @date 2022/11/1
 */
@Data
@EqualsAndHashCode(callSuper=false)
public class ProductActivityDetailVo extends ProductActivityDetailBaseVo {

    /**
     * 已支付仓储费
     */
    private BigDecimal paidStorageFee;

    /**
     * 最小起订量
     */
    private Integer quantityMinimum;

    /**
     * 活动数量
     */
    private Integer quantityTotal;

    /**
     * 成交数量
     */
    private Integer quantityAlready;

    /**
     * 出单数量
     */
    private Integer quantitySold;

    /**
     * 活动期限（天）
     */
    private Integer activityTime;

    /**
     * 免仓期（天）
     */
    private Integer freeStoragePeriod;

    /**
     * 活动成交数据
     */
    private List<ActivityOrderInfoVo> orderList;

}
