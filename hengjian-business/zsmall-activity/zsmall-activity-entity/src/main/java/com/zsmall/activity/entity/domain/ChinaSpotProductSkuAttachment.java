package com.zsmall.activity.entity.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.hengjian.common.mybatis.core.domain.NoDeptBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 国内现货商品SKU附件表
 * @TableName china_spot_product_sku_attachment
 */
@TableName(value ="china_spot_product_sku_attachment")
@Data
@EqualsAndHashCode(callSuper=false)
public class ChinaSpotProductSkuAttachment extends NoDeptBaseEntity {
    /**
     *
     */
    @TableId(value = "id", type = IdType.NONE)
    private Long id;

    /**
     * 清货商品id
     */
    private Long chinaSpotProductId;

    /**
     * 清货商品skuId
     */
    private Long chinaSpotProductSkuId;

    /**
     * 存储对象主键
     */
    private Long ossId;

    /**
     * 附件名称
     */
    private String attachmentName;

    /**
     * 附件原名
     */
    private String attachmentOriginalName;

    /**
     * 附件后缀
     */
    private String attachmentSuffix;

    /**
     * 附件存放路径
     */
    private String attachmentSavePath;

    /**
     * 附件展示地址
     */
    private String attachmentShowUrl;

    /**
     * 附件排序
     */
    private Integer attachmentSort;

    /**
     * 附件类型
     */
    private String attachmentType;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
