package com.zsmall.activity.entity.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.hengjian.common.mybatis.core.domain.NoDeptBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 锁货活动信息表（供货商）
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName(value = "product_activity_stock_lock")
public class ProductActivityStockLock extends NoDeptBaseEntity {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.NONE)
    private Long id;

    /**
     * 活动编号（供货商）
     */
    @TableField(value = "activity_code")
    private String activityCode;

    /**
     * 商品活动表（供货商）主键
     */
    @TableField(value = "product_activity_id")
    private Long productActivityId;

    /**
     * 锁货时间（天）
     */
    @TableField(value = "stock_lock_days")
    private Integer stockLockDays;

    /**
     * 可锁货总数
     */
    @TableField(value = "stock_lock_quantity_total")
    private Integer stockLockQuantityTotal;

    /**
     * 已锁货总数
     */
    @TableField(value = "stock_lock_quantity_already")
    private Integer stockLockQuantityAlready;

    /**
     * 锁货剩余数
     */
    @TableField(value = "stock_lock_quantity_surplus")
    private Integer stockLockQuantitySurplus;

    /**
     * 最小锁货数
     */
    @TableField(value = "stock_lock_quantity_minimum")
    private Integer stockLockQuantityMinimum;

    /**
     * 免仓期（单位：天）
     */
    @TableField(value = "free_storage_period")
    private Integer freeStoragePeriod;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

    /**
     * 重置剩余数量
     */
    public void resetSurplus() {
        this.stockLockQuantitySurplus = this.stockLockQuantityTotal - this.stockLockQuantityAlready;
    }
}
