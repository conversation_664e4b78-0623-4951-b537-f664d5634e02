package com.zsmall.activity.entity.domain.bo.productActivity;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022-09-15
 **/
@Data
public class ProductActivityQueryBo {
    /**
     * 查询类型
     */
    private String queryType;
    /**
     * 查询条件
     */
    private String queryValue;
    /**
     * 状态
     */
    private String activityState;
    /**
     * 排除状态
     */
    private String excludeActivityState;
    /**
     * 排序字段
     */
    private String sortValue;
    /**
     * 排序类型（倒序：DESC，正序：ASC）
     */
    private String sortType;
    /**
     * 剩余时间（天）
     */
    private Integer endDay;
    /**
     * 创建时间
     */
    private String startTime;
    /**
     * 导出类型（excel，csv）
     */
    private String exportType;
    /**
     * 排序条件
     */
    private String orderBy;
}
