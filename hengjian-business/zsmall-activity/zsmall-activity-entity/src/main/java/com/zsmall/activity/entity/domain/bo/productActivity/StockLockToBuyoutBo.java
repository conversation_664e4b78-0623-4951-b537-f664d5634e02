package com.zsmall.activity.entity.domain.bo.productActivity;

import com.zsmall.common.constant.ValidationMessage;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 请求体-锁货转圈货
 **/
@Data
public class StockLockToBuyoutBo {

    /**
     * 子活动ID
     */
    @NotBlank(message = ValidationMessage.API_REQUIRED)
    private String activityID;

    /**
     * 支付密码
     */
    private String paymentPassword;

}
