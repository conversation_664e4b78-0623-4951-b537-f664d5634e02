package com.zsmall.activity.entity.domain.bo.productActivity;

import com.zsmall.common.constant.ValidationMessage;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 请求体-员工取消分销商活动
 *
 * <AUTHOR>
 * @date 2023/4/21
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CancelBulkActivityBo {

    /**
     * 分销商的子活动ID
     */
    @NotBlank(message = ValidationMessage.API_REQUIRED)
    private String activityID;
    /**
     * 备注
     */
    private String remark;
    /**
     * 扣除供货商的金额
     */
    @NotNull(message = ValidationMessage.API_REQUIRED)
    private BigDecimal deductionAmount;
    /**
     * 退还给分销商的金额
     */
    @NotNull(message = ValidationMessage.API_REQUIRED)
    private BigDecimal refundAmount;

}
