package com.zsmall.activity.entity.domain.vo.productActivity;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 响应体-简略活动商品信息
 *
 * <AUTHOR>
 * @date 2023/7/26
 */
@Data
public class ActivitySimpleProductInfoVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 图片
     */
    private String productImg;
    /**
     * 商品名
     */
    private String productName;
    /**
     * sku
     */
    private String productSku;
    /**
     * Item NO.
     */
    private String productSkuCode;
    /**
     * 活动商品自提价（供应商调用时返回原价，平台、分销商调用时返回平台价）
     */
    private BigDecimal pickUpPrice;
    /**
     * 活动商品代发价（供应商调用时返回原价，平台、分销商调用时返回平台价）
     */
    private BigDecimal dropShippingPrice;

}
