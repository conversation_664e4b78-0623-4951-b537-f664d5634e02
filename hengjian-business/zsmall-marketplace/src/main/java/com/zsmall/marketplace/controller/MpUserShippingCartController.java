package com.zsmall.marketplace.controller;

import cn.dev33.satoken.annotation.SaIgnore;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.log.annotation.Log;
import com.hengjian.common.log.enums.BusinessType;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.hengjian.common.web.core.BaseController;
import com.zsmall.marketplace.domain.dto.ShippingCartUpdateAddress;
import com.zsmall.marketplace.service.MpUserShippingCartService;
import com.zsmall.product.entity.domain.ProductSkuPrice;
import com.zsmall.product.entity.domain.RuleLevelProductPrice;
import com.zsmall.product.entity.domain.bo.userShippingCart.ShippingCartOrderBo;
import com.zsmall.product.entity.domain.bo.userShippingCart.ShippingCartUpdateBo;
import com.zsmall.product.entity.domain.bo.userShippingCart.UserShippingCartAddBo;
import com.zsmall.product.entity.domain.vo.userShippingCart.ShippingCartInfoVo;
import com.zsmall.product.entity.domain.vo.userShippingCart.ShippingCartOrderReadyVo;
import com.zsmall.product.entity.domain.vo.userShippingCart.ShippingCartOrderVo;
import com.zsmall.product.entity.iservice.IProductSkuPriceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotEmpty;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * MP用户购物车相关接口
 */
@SaIgnore
@Slf4j
@RestController
@RequestMapping(value = "mp/userShippingCart")
public class MpUserShippingCartController extends BaseController {

    @Autowired
    private MpUserShippingCartService mpUserShippingCartService;
    @Autowired
    private  IProductSkuPriceService productSkuPriceService;
    /**
     * 购物车列表
     */
    @GetMapping(value = "/list/{site}")
    public R<List<ShippingCartInfoVo>> queryList(@PathVariable("site") String site) {
        return mpUserShippingCartService.queryList(site);
    }

    /**
     * 添加购物车
     */
    @PostMapping(value = "/addToCart")
    public R<Void> addToCart(@RequestBody @Validated UserShippingCartAddBo bo) {
        return mpUserShippingCartService.addToCart(bo);
    }

    /**
     * 查询购物车中商品种类
     */
    @GetMapping(value = "/cartQuantity")
    public R<Long> queryCartQuantity() {
        return mpUserShippingCartService.queryCartQuantity();
    }

    /**
     * 修改数量
     */
    @PostMapping(value = "/changeShoppingCartNum")
    public R<Void> changeShoppingCartNum(@RequestBody ShippingCartUpdateBo bo) {
        return toAjax(mpUserShippingCartService.updateByBo(bo));
    }

    /**
     * 删除购物车
     */
    @Log(title = "商品分类", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(mpUserShippingCartService.deleteWithValidByIds(List.of(ids), true));
    }

    /**
     * 购物车下单准备
     */
    @GetMapping("/orderReady")
    public R<ShippingCartOrderReadyVo> shippingCartOrderReady(@RequestParam("currency")String site) {
        if(StringUtils.isEmpty(site)){
            return R.fail("请选择站点");
        }
        return mpUserShippingCartService.shippingCartOrderReady(site);
    }



    /**
     * 购物车下单校验
     * @param productSkuCode
     * @param logisticType
     * @return
     */
    @GetMapping("/shippingCartCheck/{productSkuCode}/{logisticType}")
    public R shippingCartCheck(@PathVariable String productSkuCode, @PathVariable String logisticType) {
        return mpUserShippingCartService.shippingCartCheck(productSkuCode, logisticType);
    }

    /**
     * 购物车下单
     */
    @PostMapping("/order")
    public R<ShippingCartOrderVo> shippingCartOrder(@RequestBody ShippingCartOrderBo bo) throws Exception {
        return mpUserShippingCartService.shippingCartOrder(bo);
    }

    /**
     * 购物车订购商品切换地址展示价格
     * @param countryCode 国家编码
     * @param shippingCartUpdateAddresses 商城商品
     * @return
     */
    @PostMapping("/shippingCartUpdateAddress/{countryCode}")
    public R shippingCartUpdateAddress(@PathVariable String countryCode,@RequestBody List<ShippingCartUpdateAddress> shippingCartUpdateAddresses){
        if (StrUtil.isEmpty(countryCode)){
            return R.fail("国家编码不能为空！");
        }
//        List<String> us = List.of("US", "DE");
//        if (!us.contains(countryCode)){
//            return R.fail("当前不支持切换非美国/德国地址展示价格！");
//        }
        if (CollUtil.isEmpty(shippingCartUpdateAddresses)){
            return R.fail("商品信息不能为空！");
        }
        BigDecimal  totalPrice = BigDecimal.ZERO;
        for (ShippingCartUpdateAddress s : shippingCartUpdateAddresses) {
            if (StrUtil.isEmpty(s.getProductSkuCode()) || s.getQuantity() <= 0) {
                return R.fail(StrUtil.format("商品信息异常！商品SKU:{},数量:{}", s.getProductSkuCode(), s.getQuantity()));
            }
            Set<String> productSkuSet = Set.of(s.getProductSkuCode());
            Map<String, ProductSkuPrice> productSkuSitePriceMapByCode = productSkuPriceService.getProductSkuSitePriceMapByCode(productSkuSet);
            Map<String, RuleLevelProductPrice> ruleLevelProductPriceSitePriceMapByCode = productSkuPriceService.getRuleLevelProductPriceSitePriceMapByCode(productSkuSet, LoginHelper.getTenantId());
            RuleLevelProductPrice ruleLevelProductPrice = ruleLevelProductPriceSitePriceMapByCode.get(s.getProductSkuCode() + "-" + countryCode);
            if (ObjectUtil.isNotNull(ruleLevelProductPrice)) {
                totalPrice = totalPrice.add(ruleLevelProductPrice.getPlatformPickUpPrice().multiply(new BigDecimal(s.getQuantity())));
                continue;
            }else {
                ProductSkuPrice productSkuPrice = productSkuSitePriceMapByCode.get(s.getProductSkuCode() + "-" + countryCode);
                if (ObjectUtil.isNull(productSkuPrice)) {
                    return R.fail(StrUtil.format("当前商品:{},在当前国家:{}下没价格!", s.getProductSkuCode(), countryCode));
                }
                totalPrice = totalPrice.add(productSkuPrice.getPlatformPickUpPrice().multiply(new BigDecimal(s.getQuantity())));
            }
            if (CollUtil.isEmpty(productSkuSitePriceMapByCode) ) {
                return R.fail(StrUtil.format("当前商品:{}未匹配到价格!", s.getProductSkuCode()));
            }
        }
        return R.ok(totalPrice);
    }
}
