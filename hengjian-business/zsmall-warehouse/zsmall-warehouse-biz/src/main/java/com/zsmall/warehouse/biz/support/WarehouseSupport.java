package com.zsmall.warehouse.biz.support;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zsmall.common.enums.BusinessCodeEnum;
import com.zsmall.common.enums.common.GlobalStateEnum;
import com.zsmall.order.biz.test.service.impl.WarehouseServiceV2Impl;
import com.zsmall.product.entity.domain.ProductSku;
import com.zsmall.product.entity.domain.ProductSkuStock;
import com.zsmall.product.entity.iservice.IProductSkuService;
import com.zsmall.product.entity.iservice.IProductSkuStockService;
import com.zsmall.product.entity.util.ProductCodeGenerator;
import com.zsmall.warehouse.entity.domain.Warehouse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024年7月15日  15:17
 * @description: 仓库支持类
 */

@Slf4j
@Component
@RequiredArgsConstructor
public class WarehouseSupport {

    private final IProductSkuService iProductSkuService;
    private final ProductCodeGenerator productCodeGenerator;
    private final IProductSkuStockService iProductSkuStockService;
    private final WarehouseServiceV2Impl warehouseServiceV2Impl;

    /**
     * 新增产品库存数据
     *
     * @param tenantId
     * @param warehouseSystemCode
     * @param userId
     */
    @Async
    @Transactional
    public void addProductSkuStockDataAsync(String tenantId, String warehouseSystemCode, Long userId) {
        // 产品库存信息软删
        LambdaUpdateWrapper<ProductSkuStock> lambdaUpdateProductSkuStockWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateProductSkuStockWrapper.set(ProductSkuStock::getDelFlag,"2").set(ProductSkuStock::getUpdateBy,userId).set(ProductSkuStock::getUpdateTime,new Date()).eq(ProductSkuStock::getTenantId,tenantId).eq(ProductSkuStock::getWarehouseSystemCode,warehouseSystemCode);
        iProductSkuStockService.update(lambdaUpdateProductSkuStockWrapper);
        // 新增产品库存数据
        List<ProductSkuStock> productSkuStockList = new ArrayList<>();
        List<ProductSku> productSkus = iProductSkuService.listProductSkuByTenantId(tenantId);
        if (CollUtil.isNotEmpty(productSkus)) {
            for (ProductSku productSku : productSkus) {
                String stockCode = productCodeGenerator.codeGenerate(BusinessCodeEnum.StockCode);
                ProductSkuStock productSkuStock = new ProductSkuStock();
                productSkuStock.setStockCode(stockCode).setStockTotal(0).setStockReserved(0).setStockAvailable(0)
                               .setStockState(GlobalStateEnum.Valid)
                               .setErpSku(productSku.getErpSku()).setProductCode(productSku.getProductCode())
                               .setProductSkuCode(productSku.getProductSkuCode())
                               .setWarehouseSystemCode(warehouseSystemCode).setCreateTime(new Date());
                productSkuStock.setTenantId(tenantId);
                productSkuStock.setCreateBy(userId);
                productSkuStockList.add(productSkuStock);
            }
            // 新增数据
            if (CollUtil.isNotEmpty(productSkuStockList)) {
                iProductSkuStockService.saveBatch(productSkuStockList, 2000);
            }
            // 刷新库存
            warehouseServiceV2Impl.pullInventoryAsync();
        }
    }

    public void addProductSkuStockData(String tenantId, String warehouseSystemCode, Long userId) {
        // 新增产品库存数据
        List<ProductSkuStock> productSkuStockList = new ArrayList<>();
        List<ProductSku> productSkus = iProductSkuService.listProductSkuByTenantId(tenantId);
        if (CollUtil.isNotEmpty(productSkus)) {
            for (ProductSku productSku : productSkus) {
                String stockCode = productCodeGenerator.codeGenerate(BusinessCodeEnum.StockCode);
                ProductSkuStock productSkuStock = new ProductSkuStock();
                productSkuStock.setStockCode(stockCode).setStockTotal(0).setStockReserved(0).setStockAvailable(0)
                               .setStockState(GlobalStateEnum.Valid)
                               .setErpSku(productSku.getErpSku()).setProductCode(productSku.getProductCode())
                               .setProductSkuCode(productSku.getProductSkuCode())
                               .setWarehouseSystemCode(warehouseSystemCode).setCreateTime(new Date());
                productSkuStock.setTenantId(tenantId);
                productSkuStock.setCreateBy(userId);
                productSkuStockList.add(productSkuStock);
            }
            // 新增数据
            if (CollUtil.isNotEmpty(productSkuStockList)) {
                iProductSkuStockService.saveBatch(productSkuStockList, 2000);
            }
        }
    }


    public void addProductSkuStockDataAndPullInventoryAsync(String tenantId, String warehouseSystemCode, Long userId) {
        // 产品库存信息软删
        LambdaUpdateWrapper<ProductSkuStock> lambdaUpdateProductSkuStockWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateProductSkuStockWrapper.set(ProductSkuStock::getDelFlag,"2").set(ProductSkuStock::getUpdateBy,userId).set(ProductSkuStock::getUpdateTime,new Date()).eq(ProductSkuStock::getTenantId,tenantId).eq(ProductSkuStock::getWarehouseSystemCode,warehouseSystemCode);
        iProductSkuStockService.update(lambdaUpdateProductSkuStockWrapper);
        // 新增产品库存数据
        List<ProductSkuStock> productSkuStockList = new ArrayList<>();
        List<ProductSku> productSkus = iProductSkuService.listProductSkuByTenantId(tenantId);
        if (CollUtil.isNotEmpty(productSkus)) {
            for (ProductSku productSku : productSkus) {
                String stockCode = productCodeGenerator.codeGenerate(BusinessCodeEnum.StockCode);
                ProductSkuStock productSkuStock = new ProductSkuStock();
                productSkuStock.setStockCode(stockCode).setStockTotal(0).setStockReserved(0).setStockAvailable(0)
                               .setStockState(GlobalStateEnum.Valid)
                               .setErpSku(productSku.getErpSku()).setProductCode(productSku.getProductCode())
                               .setProductSkuCode(productSku.getProductSkuCode())
                               .setWarehouseSystemCode(warehouseSystemCode).setCreateTime(new Date());
                productSkuStock.setTenantId(tenantId);
                productSkuStock.setCreateBy(userId);
                productSkuStockList.add(productSkuStock);
            }
            // 新增数据
            if (CollUtil.isNotEmpty(productSkuStockList)) {
                iProductSkuStockService.saveBatch(productSkuStockList, 2000);
                // 刷新库存
                warehouseServiceV2Impl.pullInventoryAsync();
            }
        }
    }


    /**
     * 删除产品库存数据（软删除）
     *
     * @param tenantId
     * @param warehouseSystemCode
     * @param userId
     */
    @Async
    public void deleteProductSkuStockData(String tenantId, String warehouseSystemCode, Long userId) {
        LambdaUpdateWrapper<ProductSkuStock> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.set(ProductSkuStock::getDelFlag,"2").set(ProductSkuStock::getUpdateBy,userId).set(ProductSkuStock::getUpdateTime,new Date()).eq(ProductSkuStock::getTenantId,tenantId).eq(ProductSkuStock::getWarehouseSystemCode,warehouseSystemCode);
        iProductSkuStockService.update(lambdaUpdateWrapper);
    }
}
