<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.extend.shop.amazon.job.entity.mapper.AmznSyncOrderMapper">

    <!--<resultMap id="BaseResultMap" type="com.zsmall.extend.shop.amazon.job.entity.domain.AmznSyncOrder">
            <id property="amazonOrderId" column="amazon_order_id" jdbcType="VARCHAR"/>
            <result property="sellerId" column="seller_id" jdbcType="VARCHAR"/>
            <result property="sellerOrderId" column="seller_order_id" jdbcType="VARCHAR"/>
            <result property="purchaseDate" column="purchase_date" jdbcType="VARCHAR"/>
            <result property="lastUpdateDate" column="last_update_date" jdbcType="VARCHAR"/>
            <result property="orderStatus" column="order_status" jdbcType="VARCHAR"/>
            <result property="fulfillmentChannel" column="fulfillment_channel" jdbcType="VARCHAR"/>
            <result property="salesChannel" column="sales_channel" jdbcType="VARCHAR"/>
            <result property="orderChannel" column="order_channel" jdbcType="VARCHAR"/>
            <result property="shipServiceLevel" column="ship_service_level" jdbcType="VARCHAR"/>
            <result property="orderTotal" column="order_total" jdbcType="OTHER"/>
            <result property="numberOfItemsShipped" column="number_of_items_shipped" jdbcType="INTEGER"/>
            <result property="numberOfItemsUnshipped" column="number_of_items_unshipped" jdbcType="INTEGER"/>
            <result property="paymentExecutionDetail" column="payment_execution_detail" jdbcType="OTHER"/>
            <result property="paymentMethod" column="payment_method" jdbcType="VARCHAR"/>
            <result property="paymentMethodDetails" column="payment_method_details" jdbcType="OTHER"/>
            <result property="marketplaceId" column="marketplace_id" jdbcType="VARCHAR"/>
            <result property="shipmentServiceLevelCategory" column="shipment_service_level_category" jdbcType="VARCHAR"/>
            <result property="easyShipShipmentStatus" column="easy_ship_shipment_status" jdbcType="VARCHAR"/>
            <result property="cbaDisplayableShippingLabel" column="cba_displayable_shipping_label" jdbcType="VARCHAR"/>
            <result property="orderType" column="order_type" jdbcType="VARCHAR"/>
            <result property="earliestShipDate" column="earliest_ship_date" jdbcType="VARCHAR"/>
            <result property="latestShipDate" column="latest_ship_date" jdbcType="VARCHAR"/>
            <result property="earliestDeliveryDate" column="earliest_delivery_date" jdbcType="VARCHAR"/>
            <result property="latestDeliveryDate" column="latest_delivery_date" jdbcType="VARCHAR"/>
            <result property="isBusinessOrder" column="is_business_order" jdbcType="BIT"/>
            <result property="isGlobalExpressEnabled" column="is_global_express_enabled" jdbcType="BIT"/>
            <result property="isPremiumOrder" column="is_premium_order" jdbcType="BIT"/>
            <result property="isReplacementOrder" column="is_replacement_order" jdbcType="BIT"/>
            <result property="replacedOrderId" column="replaced_order_id" jdbcType="VARCHAR"/>
            <result property="promiseResponseDueDate" column="promise_response_due_date" jdbcType="VARCHAR"/>
            <result property="isEstimatedShipDateSet" column="is_estimated_ship_date_set" jdbcType="BIT"/>
            <result property="isSoldByAB" column="is_sold_by_a_b" jdbcType="BIT"/>
            <result property="isIBA" column="is_i_b_a" jdbcType="BIT"/>
            <result property="defaultShipFromLocationAddress" column="default_ship_from_location_address" jdbcType="OTHER"/>
            <result property="buyerInvoicePreference" column="buyer_invoice_preference" jdbcType="VARCHAR"/>
            <result property="buyerTaxInformation" column="buyer_tax_information" jdbcType="OTHER"/>
            <result property="fulfillmentInstruction" column="fulfillment_instruction" jdbcType="OTHER"/>
            <result property="isISPU" column="is_i_s_p_u" jdbcType="BIT"/>
            <result property="isAccessPointOrder" column="is_access_point_order" jdbcType="BIT"/>
            <result property="marketplaceTaxInfo" column="marketplace_tax_info" jdbcType="OTHER"/>
            <result property="sellerDisplayName" column="seller_display_name" jdbcType="VARCHAR"/>
            <result property="shippingAddress" column="shipping_address" jdbcType="OTHER"/>
            <result property="buyerInfo" column="buyer_info" jdbcType="OTHER"/>
            <result property="automatedShippingSettings" column="automated_shipping_settings" jdbcType="OTHER"/>
            <result property="hasRegulatedItems" column="has_regulated_items" jdbcType="BIT"/>
            <result property="electronicInvoiceStatus" column="electronic_invoice_status" jdbcType="OTHER"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        amazon_order_id,seller_id,seller_order_id,
        purchase_date,last_update_date,order_status,
        fulfillment_channel,sales_channel,order_channel,
        ship_service_level,order_total,number_of_items_shipped,
        number_of_items_unshipped,payment_execution_detail,payment_method,
        payment_method_details,marketplace_id,shipment_service_level_category,
        easy_ship_shipment_status,cba_displayable_shipping_label,order_type,
        earliest_ship_date,latest_ship_date,earliest_delivery_date,
        latest_delivery_date,is_business_order,is_global_express_enabled,
        is_premium_order,is_replacement_order,replaced_order_id,
        promise_response_due_date,is_estimated_ship_date_set,is_sold_by_a_b,
        is_i_b_a,default_ship_from_location_address,buyer_invoice_preference,
        buyer_tax_information,fulfillment_instruction,is_i_s_p_u,
        is_access_point_order,marketplace_tax_info,seller_display_name,
        shipping_address,buyer_info,automated_shipping_settings,
        has_regulated_items,electronic_invoice_status,create_time,
        update_time
    </sql>-->
</mapper>
