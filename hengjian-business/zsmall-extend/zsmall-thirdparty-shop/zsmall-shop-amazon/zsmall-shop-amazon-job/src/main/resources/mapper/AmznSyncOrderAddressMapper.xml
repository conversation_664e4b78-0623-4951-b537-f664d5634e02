<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.extend.shop.amazon.job.entity.mapper.AmznSyncOrderAddressMapper">

<!--    <resultMap id="BaseResultMap" type="com.zsmall.extend.shop.amazon.job.entity.domain.AmznSyncOrderAddress">-->
<!--            <id property="id" column="id" jdbcType="BIGINT"/>-->
<!--            <result property="amazonOrderId" column="amazon_order_id" jdbcType="VARCHAR"/>-->
<!--            <result property="buyerCompanyName" column="buyer_company_name" jdbcType="VARCHAR"/>-->
<!--            <result property="shippingAddress" column="shipping_address" jdbcType="OTHER"/>-->
<!--            <result property="deliveryPreferences" column="delivery_preferences" jdbcType="OTHER"/>-->
<!--            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>-->
<!--            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>-->
<!--    </resultMap>-->

<!--    <sql id="Base_Column_List">-->
<!--        id,amazon_order_id,buyer_company_name,-->
<!--        shipping_address,delivery_preferences,create_time,-->
<!--        update_time-->
<!--    </sql>-->
</mapper>
