<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.extend.shop.amazon.job.entity.mapper.AmznSyncFeedFulfillmentMapper">

    <resultMap id="BaseResultMap" type="com.zsmall.extend.shop.amazon.job.entity.domain.AmznSyncFeedFulfillment">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="feedId" column="feed_id" jdbcType="VARCHAR"/>
            <result property="amazonOrderId" column="amazon_order_id" jdbcType="VARCHAR"/>
            <result property="codCollectionMethod" column="cod_collection_method" jdbcType="VARCHAR"/>
            <result property="fulfillmentDate" column="fulfillment_date" jdbcType="TIMESTAMP"/>
            <result property="merchantFulfillmentId" column="merchant_fulfillment_id" jdbcType="BIGINT"/>
            <result property="merchantOrderId" column="merchant_order_id" jdbcType="VARCHAR"/>
            <result property="orderFulfillmentShippingId" column="order_fulfillment_shipping_id" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,feed_id,amazon_order_id,
        cod_collection_method,fulfillment_date,merchant_fulfillment_id,
        merchant_order_id,order_fulfillment_shipping_id,create_time,
        update_time
    </sql>
</mapper>
