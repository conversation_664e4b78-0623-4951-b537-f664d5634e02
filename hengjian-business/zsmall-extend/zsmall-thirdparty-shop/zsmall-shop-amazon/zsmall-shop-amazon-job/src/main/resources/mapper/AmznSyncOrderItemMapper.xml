<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.extend.shop.amazon.job.entity.mapper.AmznSyncOrderItemMapper">

<!--    <resultMap id="BaseResultMap" type="com.zsmall.extend.shop.amazon.job.entity.domain.AmznSyncOrderItem">-->
<!--            <id property="id" column="id" jdbcType="BIGINT"/>-->
<!--            <result property="amazonOrderId" column="amazon_order_id" jdbcType="VARCHAR"/>-->
<!--            <result property="asin" column="asin" jdbcType="VARCHAR"/>-->
<!--            <result property="sellerSku" column="seller_sku" jdbcType="VARCHAR"/>-->
<!--            <result property="orderItemId" column="order_item_id" jdbcType="VARCHAR"/>-->
<!--            <result property="associatedItems" column="associated_items" jdbcType="OTHER"/>-->
<!--            <result property="title" column="title" jdbcType="VARCHAR"/>-->
<!--            <result property="quantityOrdered" column="quantity_ordered" jdbcType="INTEGER"/>-->
<!--            <result property="quantityShipped" column="quantity_shipped" jdbcType="INTEGER"/>-->
<!--            <result property="productInfo" column="product_info" jdbcType="OTHER"/>-->
<!--            <result property="pointsGranted" column="points_granted" jdbcType="OTHER"/>-->
<!--            <result property="itemPrice" column="item_price" jdbcType="OTHER"/>-->
<!--            <result property="shippingPrice" column="shipping_price" jdbcType="OTHER"/>-->
<!--            <result property="itemTax" column="item_tax" jdbcType="OTHER"/>-->
<!--            <result property="shippingTax" column="shipping_tax" jdbcType="OTHER"/>-->
<!--            <result property="shippingDiscount" column="shipping_discount" jdbcType="OTHER"/>-->
<!--            <result property="shippingDiscountTax" column="shipping_discount_tax" jdbcType="OTHER"/>-->
<!--            <result property="promotionDiscount" column="promotion_discount" jdbcType="OTHER"/>-->
<!--            <result property="promotionDiscountTax" column="promotion_discount_tax" jdbcType="OTHER"/>-->
<!--            <result property="promotionIds" column="promotion_ids" jdbcType="OTHER"/>-->
<!--            <result property="cODFee" column="c_o_d_fee" jdbcType="OTHER"/>-->
<!--            <result property="cODFeeDiscount" column="c_o_d_fee_discount" jdbcType="OTHER"/>-->
<!--            <result property="isGift" column="is_gift" jdbcType="BIT"/>-->
<!--            <result property="conditionNote" column="condition_note" jdbcType="VARCHAR"/>-->
<!--            <result property="conditionId" column="condition_id" jdbcType="VARCHAR"/>-->
<!--            <result property="conditionSubtypeId" column="condition_subtype_id" jdbcType="VARCHAR"/>-->
<!--            <result property="scheduledDeliveryStartDate" column="scheduled_delivery_start_date" jdbcType="VARCHAR"/>-->
<!--            <result property="scheduledDeliveryEndDate" column="scheduled_delivery_end_date" jdbcType="VARCHAR"/>-->
<!--            <result property="priceDesignation" column="price_designation" jdbcType="VARCHAR"/>-->
<!--            <result property="taxCollection" column="tax_collection" jdbcType="VARCHAR"/>-->
<!--            <result property="serialNumberRequired" column="serial_number_required" jdbcType="BIT"/>-->
<!--            <result property="isTransparency" column="is_transparency" jdbcType="BIT"/>-->
<!--            <result property="iossNumber" column="ioss_number" jdbcType="VARCHAR"/>-->
<!--            <result property="storeChainStoreId" column="store_chain_store_id" jdbcType="VARCHAR"/>-->
<!--            <result property="deemedResellerCategory" column="deemed_reseller_category" jdbcType="VARCHAR"/>-->
<!--            <result property="buyerInfo" column="buyer_info" jdbcType="OTHER"/>-->
<!--            <result property="buyerRequestedCancel" column="buyer_requested_cancel" jdbcType="OTHER"/>-->
<!--            <result property="serialNumbers" column="serial_numbers" jdbcType="OTHER"/>-->
<!--            <result property="substitutionPreferences" column="substitution_preferences" jdbcType="OTHER"/>-->
<!--            <result property="measurement" column="measurement" jdbcType="OTHER"/>-->
<!--            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>-->
<!--            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>-->
<!--    </resultMap>-->

<!--    <sql id="Base_Column_List">-->
<!--        id,amazon_order_id,asin,-->
<!--        seller_sku,order_item_id,associated_items,-->
<!--        title,quantity_ordered,quantity_shipped,-->
<!--        product_info,points_granted,item_price,-->
<!--        shipping_price,item_tax,shipping_tax,-->
<!--        shipping_discount,shipping_discount_tax,promotion_discount,-->
<!--        promotion_discount_tax,promotion_ids,c_o_d_fee,-->
<!--        c_o_d_fee_discount,is_gift,condition_note,-->
<!--        condition_id,condition_subtype_id,scheduled_delivery_start_date,-->
<!--        scheduled_delivery_end_date,price_designation,tax_collection,-->
<!--        serial_number_required,is_transparency,ioss_number,-->
<!--        store_chain_store_id,deemed_reseller_category,buyer_info,-->
<!--        buyer_requested_cancel,serial_numbers,substitution_preferences,-->
<!--        measurement,create_time,update_time-->
<!--    </sql>-->
</mapper>
