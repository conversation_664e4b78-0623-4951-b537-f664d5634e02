package com.zsmall.extend.shop.amazon.job.entity.iservice;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zsmall.extend.shop.amazon.job.entity.domain.AmznSyncOrder;
import com.zsmall.extend.shop.amazon.job.entity.mapper.AmznSyncOrderMapper;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【amzn_sync_order】的数据库操作Service实现
* @createDate 2023-11-01 10:18:56
*/
@Service
public class IAmznSyncOrderService extends ServiceImpl<AmznSyncOrderMapper, AmznSyncOrder> {

    public Page<AmznSyncOrder> queryPage(Page<AmznSyncOrder> page, String sellerId, Date startDate, Date endDate, List<String> orderStatus) {
        LambdaQueryWrapper<AmznSyncOrder> lqw = Wrappers.lambdaQuery();
        lqw.eq(AmznSyncOrder::getSellerId, sellerId);
        lqw.ge(AmznSyncOrder::getUpdateTime, startDate);
        lqw.le(AmznSyncOrder::getCreateTime, endDate);
        lqw.notIn(AmznSyncOrder::getOrderStatus, orderStatus);
        return baseMapper.selectPage(page, lqw);
    }

    public AmznSyncOrder queryByAmazonOrderId(String amazonOrderId) {
        return lambdaQuery().eq(AmznSyncOrder::getAmazonOrderId, amazonOrderId).one();
    }

    public List<AmznSyncOrder> queryByJobSyncId(Long jobSyncId) {
        return lambdaQuery().eq(AmznSyncOrder::getJobSyncId, jobSyncId).list();
    }

}




