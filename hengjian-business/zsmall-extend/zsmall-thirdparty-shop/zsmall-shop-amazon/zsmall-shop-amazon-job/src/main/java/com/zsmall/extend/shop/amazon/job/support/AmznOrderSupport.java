package com.zsmall.extend.shop.amazon.job.support;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zsmall.extend.shop.amazon.job.entity.domain.AmznSyncOrder;
import com.zsmall.extend.shop.amazon.job.entity.domain.AmznSyncOrderAddress;
import com.zsmall.extend.shop.amazon.job.entity.domain.AmznSyncOrderItem;
import com.zsmall.extend.shop.amazon.job.entity.iservice.IAmznSyncOrderAddressService;
import com.zsmall.extend.shop.amazon.job.entity.iservice.IAmznSyncOrderItemService;
import com.zsmall.extend.shop.amazon.job.entity.iservice.IAmznSyncOrderService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 亚马逊订单相关支持
 *
 * <AUTHOR>
 * @date 2023/11/1
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AmznOrderSupport {

    private final IAmznSyncOrderService iAmznSyncOrderService;
    private final IAmznSyncOrderItemService iAmznSyncOrderItemService;
    private final IAmznSyncOrderAddressService iAmznSyncOrderAddressService;

    public Page<AmznSyncOrder> queryAmznOrderByDateTime(Page<AmznSyncOrder> inPage, String sellerId, Date startDate, Date endDate) {
        // 排除某些状态不查询
        List<String> orderStatus = new ArrayList<>();
        orderStatus.add("Pending");
        orderStatus.add("Canceled");
        return iAmznSyncOrderService.queryPage(inPage, sellerId, startDate, endDate, orderStatus);
    }

    public List<AmznSyncOrderItem> queryOrderItemByAmazonOrderId(String amazonOrderId) {
        return iAmznSyncOrderItemService.queryByAmazonOrderId(amazonOrderId);
    }

    public List<AmznSyncOrderAddress> queryOrderAddressByAmazonOrderId(String amazonOrderId) {
        return iAmznSyncOrderAddressService.queryByAmazonOrderId(amazonOrderId);
    }

}
