package com.zsmall.extend.shop.amazon.job.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 亚马逊同步订单履约-运送单
 * @TableName amzn_sync_feed_fulfillment_shipping
 */
@TableName(value ="amzn_sync_feed_fulfillment_shipping")
@Data
public class AmznSyncFeedFulfillmentShipping implements Serializable {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 承运商编码
     */
    private String carrierCode;

    /**
     * 承运商名词
     */
    private String carrierName;

    /**
     * 单号
     */
    private String shipperTrackingNumber;

    /**
     * 运送方式
     */
    private String shippingMethod;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        AmznSyncFeedFulfillmentShipping other = (AmznSyncFeedFulfillmentShipping) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getCarrierCode() == null ? other.getCarrierCode() == null : this.getCarrierCode().equals(other.getCarrierCode()))
            && (this.getCarrierName() == null ? other.getCarrierName() == null : this.getCarrierName().equals(other.getCarrierName()))
            && (this.getShipperTrackingNumber() == null ? other.getShipperTrackingNumber() == null : this.getShipperTrackingNumber().equals(other.getShipperTrackingNumber()))
            && (this.getShippingMethod() == null ? other.getShippingMethod() == null : this.getShippingMethod().equals(other.getShippingMethod()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getCarrierCode() == null) ? 0 : getCarrierCode().hashCode());
        result = prime * result + ((getCarrierName() == null) ? 0 : getCarrierName().hashCode());
        result = prime * result + ((getShipperTrackingNumber() == null) ? 0 : getShipperTrackingNumber().hashCode());
        result = prime * result + ((getShippingMethod() == null) ? 0 : getShippingMethod().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", carrierCode=").append(carrierCode);
        sb.append(", carrierName=").append(carrierName);
        sb.append(", shipperTrackingNumber=").append(shipperTrackingNumber);
        sb.append(", shippingMethod=").append(shippingMethod);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}
