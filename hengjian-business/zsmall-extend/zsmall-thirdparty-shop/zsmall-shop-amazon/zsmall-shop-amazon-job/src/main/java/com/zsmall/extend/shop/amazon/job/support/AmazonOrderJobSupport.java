package com.zsmall.extend.shop.amazon.job.support;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.amazon.SellingPartnerAPIAA.LWAAccessTokenCache;
import com.amazon.client.api.OrdersV0Api;
import com.amazon.client.invoker.ApiResponse;
import com.amazon.client.model.*;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zsmall.extend.shop.amazon.bean.AmazonAppBean;
import com.zsmall.extend.shop.amazon.job.constants.JobSyncStatus;
import com.zsmall.extend.shop.amazon.job.entity.bean.ErrorMessage;
import com.zsmall.extend.shop.amazon.job.entity.domain.AmznSyncOrder;
import com.zsmall.extend.shop.amazon.job.entity.domain.AmznSyncOrderAddress;
import com.zsmall.extend.shop.amazon.job.entity.domain.AmznSyncOrderItem;
import com.zsmall.extend.shop.amazon.job.entity.domain.JobAmznOrderSync;
import com.zsmall.extend.shop.amazon.job.entity.domain.bo.AmznOrderSyncAddBo;
import com.zsmall.extend.shop.amazon.job.entity.iservice.IAmznSyncOrderAddressService;
import com.zsmall.extend.shop.amazon.job.entity.iservice.IAmznSyncOrderItemService;
import com.zsmall.extend.shop.amazon.job.entity.iservice.IAmznSyncOrderService;
import com.zsmall.extend.shop.amazon.job.entity.iservice.IJobAmznOrderSyncService;
import com.zsmall.extend.shop.amazon.job.mq.AmznJobRedisProducer;
import com.zsmall.extend.shop.amazon.kit.AmazonSpApiKit;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Slf4j
@Component
@RequiredArgsConstructor
public class AmazonOrderJobSupport extends JobBaseSupport {

    private final IJobAmznOrderSyncService jobAmznOrderSyncService;
    private final AmznJobRedisProducer amznJobRedisProducer;
    private final LWAAccessTokenCache lwaAccessTokenCache;
    private final IAmznSyncOrderService iAmznSyncOrderService;
    private final IAmznSyncOrderItemService iAmznSyncOrderItemService;
    private final IAmznSyncOrderAddressService iAmznSyncOrderAddressService;


    public void addOrderSyncs(AmznOrderSyncAddBo orderSyncAdd) {
        log.info("AmazonOrderJobSupport addOrderSyncs starting ... params = {}", JSONUtil.toJsonStr(orderSyncAdd));
        LocalDateTime endDateTime = orderSyncAdd.getEndDateTime();
        List<AmznOrderSyncAddBo.OrderSync> orderSyncs = orderSyncAdd.getOrderSyncs();

        List<JobAmznOrderSync> jobAmznOrderSyncs = new ArrayList<>();
        if (CollUtil.isNotEmpty(orderSyncs)) {
            orderSyncs.forEach(orderSync -> {
                String appId = orderSync.getAppId();
                String sellerId = orderSync.getSellerId();
                String refreshToken = orderSync.getRefreshToken();
                LocalDateTime startDateTime = orderSync.getDefaultStartDateTime();
                log.info("appId = {}, sellerId = {}, refreshToken = {}, defaultStartDateTime = {}", appId, sellerId, refreshToken, startDateTime);

                LambdaQueryWrapper<JobAmznOrderSync> lqw = new LambdaQueryWrapper<>();
                lqw.eq(JobAmznOrderSync::getAmznAppId, appId);
                lqw.orderByDesc(JobAmznOrderSync::getSyncEndTime).orderByDesc(JobAmznOrderSync::getId);
                List<JobAmznOrderSync> list = jobAmznOrderSyncService.list(lqw);
                if (CollUtil.isNotEmpty(list)) {
                    JobAmznOrderSync amznOrderSync = list.get(0);

                    String syncStatus = amznOrderSync.getSyncStatus();
                    if (Objects.equals(syncStatus, JobSyncStatus.Todo.name())) {
                        log.info("sellerId = {}, syncStatus = {}, last register status is Todo.", sellerId, syncStatus);
                        return;
                    }

                    // 如果业务中出现异常 情况，需手动增加一条待处理数据以便后续正常运行
                    if (Objects.equals(syncStatus, JobSyncStatus.Error.name())) {
                        log.info("sellerId = {}, syncStatus = {}, last register status is Error.", sellerId, syncStatus);

                        // 继续查询错误订单
                        amznOrderSync.setSyncStatus(JobSyncStatus.Todo.name());
                        amznOrderSync.setAmznAppId(appId);
                        amznOrderSync.setSellerId(sellerId);
                        amznOrderSync.setRefreshToken(refreshToken);
                        jobAmznOrderSyncs.add(amznOrderSync);
                        return;
                    }

                    // 非第一次，则替换startDateTime
                    startDateTime = endDateTime;
                }

                log.info("startDateTime = {}, endDateTime = {}", startDateTime, endDateTime);
                JobAmznOrderSync amznOrderSync = new JobAmznOrderSync();
                amznOrderSync.setSellerId(sellerId);
                amznOrderSync.setRefreshToken(refreshToken);
                amznOrderSync.setAmznAppId(appId);
                amznOrderSync.setSyncStartTime(startDateTime);
                amznOrderSync.setSyncEndTime(endDateTime);
                jobAmznOrderSyncs.add(amznOrderSync);
            });

            if (CollUtil.isNotEmpty(jobAmznOrderSyncs)) {
                boolean saved = jobAmznOrderSyncService.saveBatch(jobAmznOrderSyncs);
                log.info("Amazon order sync job save sussess ?　｛｝", saved);
                if (!saved) {
                    log.error("Amazon order sync job save error!!!");
                    return;
                }
                amznJobRedisProducer.orderPublishProducer(jobAmznOrderSyncs);
            }
        }
        log.info("AmazonOrderJobSupport addOrderSyncs ending!");
    }

    /**
     * 调用亚马逊接口获取订单
     *
     * @param jobSyncId
     */
    public void getOrdersFromAmzn(Long jobSyncId) {
        JobAmznOrderSync amznOrderSync = jobAmznOrderSyncService.getById(jobSyncId);
        log.info("jobSyncId = {}, {}", jobSyncId, JSONUtil.toJsonStr(amznOrderSync));
        String sellerId = amznOrderSync.getSellerId();
        String amznAppId = amznOrderSync.getAmznAppId();
        String syncStatus = amznOrderSync.getSyncStatus();
        // 暂时不用
        // String marketplaceId = amznOrderSync.getMarketplaceId();

        if (!StrUtil.equals(syncStatus, JobSyncStatus.Todo.name())) {
            log.error("sellerId = {}, amznAppId = {} current state is not Todo", sellerId, amznAppId);
            return;
        }

        AmazonAppBean amazonAppBean = buildAppBean(amznAppId);
        if (amazonAppBean == null) {
            log.error("sellerId = {}, amznAppId = {} is invalid...", sellerId, amznAppId);
            return;
        }

        amznOrderSync.setSyncStatus(JobSyncStatus.Running.name());
        jobAmznOrderSyncService.updateById(amznOrderSync);

        LocalDateTime syncStartTime = amznOrderSync.getSyncStartTime();
        LocalDateTime syncEndTime = amznOrderSync.getSyncEndTime();

        OrdersV0Api ordersV0Api = AmazonSpApiKit.createOrdersV0Api(amazonAppBean, lwaAccessTokenCache, amznOrderSync.getRefreshToken());

        ZoneId utc = ZoneId.of("UTC");
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ISO_OFFSET_DATE_TIME.withZone(utc);
        String updateBefore = syncStartTime.atZone(ZoneId.systemDefault()).withZoneSameInstant(utc)
                                           .format(dateTimeFormatter);
        String updateAfter = syncEndTime.atZone(ZoneId.systemDefault()).withZoneSameInstant(utc)
                                        .format(dateTimeFormatter);
        String nextToken = null;

        // 订单状态列表
        List<String> orderStatuses = CollUtil.newArrayList("Unshipped");
        List<String> fulfillmentChannels = CollUtil.newArrayList("MFN");
        String marketplaceId = amazonAppBean.getEndpoint().getMarketplaceId();

        try {
            boolean hasNext;
            AmznSyncOrder amazonSyncOrder;
            List<AmznSyncOrder> syncOrders = new ArrayList<>();

            scan:
            {
                List<String> orderIds = new ArrayList<>();
                do {
                    // 循环判断
                    GetOrdersResponse getOrdersResponse = ordersV0Api
                        .getOrders(CollUtil.newArrayList(marketplaceId), null, null, updateAfter, updateBefore, orderStatuses,
                            fulfillmentChannels, null, null, null, null, null, null,
                            nextToken, null, null, null, null, null, null,
                            null, null);

                    // TODO 判断是否429错误代码，超过请求速率要求
                    ErrorList resError = getOrdersResponse.getErrors();
                    if (Objects.nonNull(resError) && CollUtil.isNotEmpty(resError)) {
                        List<ErrorMessage> errorMessages = BeanUtil.copyToList(resError, ErrorMessage.class);
                        amznOrderSync.setSyncStatus(JobSyncStatus.Error.name());
                        amznOrderSync.setErrorMessage(errorMessages);
                        break scan;
                    }

                    OrdersList orderList = getOrdersResponse.getPayload();
                    OrderList orders = orderList.getOrders();
                    nextToken = orderList.getNextToken();

                    // 如果存在且不为空，则在下一个请求中传递此字符串标记以返回下一个响应页面。
                    hasNext = StrUtil.isNotBlank(nextToken);
                    // 判断订单数量是否为空，并进行解析
                    if (CollUtil.isNotEmpty(orders)) {
                        for (Order order : orders) {
                            String amazonOrderId = order.getAmazonOrderId();
                            amazonSyncOrder = iAmznSyncOrderService.queryByAmazonOrderId(amazonOrderId);
                            if (amazonSyncOrder == null) {
                                amazonSyncOrder = new AmznSyncOrder();
                            }

                            BeanUtil.copyProperties(order, amazonSyncOrder,
                                CopyOptions.create().setIgnoreNullValue(true).setIgnoreError(true)
                                           .setIgnoreProperties(new String[]{"id", "shippingAddress"}));

                            amazonSyncOrder.setSellerId(sellerId);
                            amazonSyncOrder.setJobSyncId(jobSyncId);
                            syncOrders.add(amazonSyncOrder);
                            orderIds.add(amazonSyncOrder.getAmazonOrderId());
                        }
                    }
                } while (hasNext);

                amznOrderSync.setSyncStatus(JobSyncStatus.Success.name());
                if (CollUtil.isNotEmpty(syncOrders)) {
                    iAmznSyncOrderService.saveOrUpdateBatch(syncOrders);

                    // 推送订单详情获取消息队列
                    amznJobRedisProducer.orderItemsPublishProducer(jobSyncId);
                    // 推送订单地址获取消息队列
                    amznJobRedisProducer.orderAddressPublishProducer(jobSyncId);
                }
            }
        } catch (Exception e) {
            log.error("error message = {}", e.getMessage(), e);
            amznOrderSync.setSyncStatus(JobSyncStatus.Error.name());
            amznOrderSync.setErrorMessage(CollUtil.newArrayList(new ErrorMessage("-1", e.getMessage())));
        } finally {
            jobAmznOrderSyncService.updateById(amznOrderSync);
        }
    }

    /**
     * 调用亚马逊接口获取订单详情
     */
    public void getOrderItemsFromAmzn(Long jobSyncId) {
        JobAmznOrderSync amznOrderSync = jobAmznOrderSyncService.getById(jobSyncId);
        log.info("jobSyncId = {}, {}", jobSyncId, JSONUtil.toJsonStr(amznOrderSync));
        String sellerId = amznOrderSync.getSellerId();
        String amznAppId = amznOrderSync.getAmznAppId();

        AmazonAppBean amazonAppBean = buildAppBean(amznAppId);
        if (amazonAppBean == null) {
            log.error("亚马逊任务【调用亚马逊接口获取订单详情】sellerId = {}, amznAppId = {}不可用", sellerId, amznAppId);
            return;
        }

        OrdersV0Api ordersV0Api = AmazonSpApiKit.createOrdersV0Api(amazonAppBean, lwaAccessTokenCache, amznOrderSync.getRefreshToken());

        List<AmznSyncOrder> amznSyncOrders = iAmznSyncOrderService.queryByJobSyncId(jobSyncId);
        if (CollUtil.isNotEmpty(amznSyncOrders)) {
            List<AmznSyncOrderItem> syncOrderItems = new ArrayList<>();

            for (AmznSyncOrder amznSyncOrder : amznSyncOrders) {
                String amazonOrderId = amznSyncOrder.getAmazonOrderId();

                try {

                    boolean hasNext;
                    String nextToken = null;

                    do {

                        GetOrderItemsResponse getOrderItemsResponse = ordersV0Api.getOrderItems(amazonOrderId, nextToken);

                        // TODO 判断是否429错误代码，超过请求速率要求
                        ErrorList resError = getOrderItemsResponse.getErrors();
                        if (Objects.nonNull(resError) && CollUtil.isNotEmpty(resError)) {
                            // TODO 错误要保存
                        }

                        OrderItemsList orderItemList = getOrderItemsResponse.getPayload();
                        OrderItemList orderItems = orderItemList.getOrderItems();
                        nextToken = orderItemList.getNextToken();

                        // 如果存在且不为空，则在下一个请求中传递此字符串标记以返回下一个响应页面。
                        hasNext = StrUtil.isNotBlank(nextToken);

                        // 判断订单数量是否为空，并进行解析
                        if (CollUtil.isNotEmpty(orderItems)) {
                            for (OrderItem orderItem : orderItems) {
                                String orderItemId = orderItem.getOrderItemId();

                                AmznSyncOrderItem amazonSyncOrderItem = iAmznSyncOrderItemService.queryByOrderItemId(orderItemId);
                                if (amazonSyncOrderItem == null) {
                                    amazonSyncOrderItem = new AmznSyncOrderItem();
                                }

                                BeanUtil.copyProperties(orderItem, amazonSyncOrderItem,
                                    CopyOptions.create().setIgnoreNullValue(true).setIgnoreError(true)
                                               .setIgnoreProperties(new String[]{"productInfo", "taxCollection", "pointsGranted"}));

                                syncOrderItems.add(amazonSyncOrderItem);
                            }
                        }

                    } while (hasNext);

                    if (CollUtil.isNotEmpty(syncOrderItems)) {
                        iAmznSyncOrderItemService.saveOrUpdateBatch(syncOrderItems);
                    }
                } catch (Exception e){
                    log.error("亚马逊任务【调用亚马逊接口获取订单详情】出现异常，amazonOrderId = {}， 原因：{}", amazonOrderId, e.getMessage(), e);
                }
            }
        }
    }

    /**
     * 调用亚马逊接口获取对订单地址信息
     * @param jobSyncId
     */
    public void orderAddressConsumer(Long jobSyncId) {
        JobAmznOrderSync amznOrderSync = jobAmznOrderSyncService.getById(jobSyncId);
        log.info("jobSyncId = {}, {}", jobSyncId, JSONUtil.toJsonStr(amznOrderSync));
        String sellerId = amznOrderSync.getSellerId();
        String amznAppId = amznOrderSync.getAmznAppId();

        AmazonAppBean amazonAppBean = buildAppBean(amznAppId);
        if (amazonAppBean == null) {
            log.error("亚马逊任务【调用亚马逊接口获取订单详情】sellerId = {}, amznAppId = {}不可用", sellerId, amznAppId);
            return;
        }

        OrdersV0Api ordersV0Api = AmazonSpApiKit.createOrdersV0Api(amazonAppBean, lwaAccessTokenCache, amznOrderSync.getRefreshToken());

        List<AmznSyncOrder> amznSyncOrders = iAmznSyncOrderService.queryByJobSyncId(jobSyncId);
        if (CollUtil.isNotEmpty(amznSyncOrders)) {
            List<AmznSyncOrderAddress> syncOrderAddresses = new ArrayList<>();

            for (AmznSyncOrder amznSyncOrder : amznSyncOrders) {
                String amazonOrderId = amznSyncOrder.getAmazonOrderId();

                try {

                    ApiResponse<GetOrderAddressResponse> orderAddressWithHttpInfo = ordersV0Api.getOrderAddressWithHttpInfo(amazonOrderId);
                    OrderAddress orderAddress = Optional.ofNullable(orderAddressWithHttpInfo)
                                                         .flatMap(resp -> Optional.ofNullable(resp.getData()))
                                                         .map(GetOrderAddressResponse::getPayload)
                                                         .orElse(null);

                    // 判断订单数量是否为空，并进行解析
                    if (orderAddress != null) {
                        List<AmznSyncOrderAddress> amznSyncOrderAddresses = iAmznSyncOrderAddressService.queryByAmazonOrderId(amazonOrderId);
                        AmznSyncOrderAddress amznSyncOrderAddress = CollUtil.get(amznSyncOrderAddresses, 0);
                        if (amznSyncOrderAddress == null) {
                            amznSyncOrderAddress = new AmznSyncOrderAddress();
                        }

                        BeanUtil.copyProperties(orderAddress, amznSyncOrderAddress,
                            CopyOptions.create().setIgnoreNullValue(true).setIgnoreError(true)
                                       .setIgnoreProperties(new String[]{"id"}));

                        syncOrderAddresses.add(amznSyncOrderAddress);
                    }

                    if (CollUtil.isNotEmpty(syncOrderAddresses)) {
                        iAmznSyncOrderAddressService.saveOrUpdateBatch(syncOrderAddresses);
                    }
                } catch (Exception e){
                    log.error("亚马逊任务【调用亚马逊接口获取订单详情】出现异常，amazonOrderId = {}， 原因：{}", amazonOrderId, e.getMessage(), e);
                }
            }
        }
    }

}
