package com.zsmall.extend.shop.amazon.job.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 亚马逊同步订单履约-子订单信息
 * @TableName amzn_sync_feed_fulfillment_item
 */
@TableName(value ="amzn_sync_feed_fulfillment_item")
@Data
public class AmznSyncFeedFulfillmentItem implements Serializable {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 亚马逊子订单编码
     */
    private String amazonOrderItemCode;

    /**
     * 用户子订单履约Id
     */
    private Long merchantFulfillmentItemId;

    /**
     * 用户子订单Id
     */
    private String merchantOrderItemId;

    /**
     * 亚马逊同步订单履约信息Id
     */
    private String orderFulfillmentId;

    /**
     * 用户子订单履约数量
     */
    private Integer quantity;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        AmznSyncFeedFulfillmentItem other = (AmznSyncFeedFulfillmentItem) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getAmazonOrderItemCode() == null ? other.getAmazonOrderItemCode() == null : this.getAmazonOrderItemCode().equals(other.getAmazonOrderItemCode()))
            && (this.getMerchantFulfillmentItemId() == null ? other.getMerchantFulfillmentItemId() == null : this.getMerchantFulfillmentItemId().equals(other.getMerchantFulfillmentItemId()))
            && (this.getMerchantOrderItemId() == null ? other.getMerchantOrderItemId() == null : this.getMerchantOrderItemId().equals(other.getMerchantOrderItemId()))
            && (this.getOrderFulfillmentId() == null ? other.getOrderFulfillmentId() == null : this.getOrderFulfillmentId().equals(other.getOrderFulfillmentId()))
            && (this.getQuantity() == null ? other.getQuantity() == null : this.getQuantity().equals(other.getQuantity()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getAmazonOrderItemCode() == null) ? 0 : getAmazonOrderItemCode().hashCode());
        result = prime * result + ((getMerchantFulfillmentItemId() == null) ? 0 : getMerchantFulfillmentItemId().hashCode());
        result = prime * result + ((getMerchantOrderItemId() == null) ? 0 : getMerchantOrderItemId().hashCode());
        result = prime * result + ((getOrderFulfillmentId() == null) ? 0 : getOrderFulfillmentId().hashCode());
        result = prime * result + ((getQuantity() == null) ? 0 : getQuantity().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", amazonOrderItemCode=").append(amazonOrderItemCode);
        sb.append(", merchantFulfillmentItemId=").append(merchantFulfillmentItemId);
        sb.append(", merchantOrderItemId=").append(merchantOrderItemId);
        sb.append(", orderFulfillmentId=").append(orderFulfillmentId);
        sb.append(", quantity=").append(quantity);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}
