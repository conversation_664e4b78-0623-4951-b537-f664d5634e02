package com.zsmall.extend.shop.amazon.job.entity.iservice;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zsmall.extend.shop.amazon.job.entity.domain.AmznSyncFeedFulfillmentShipping;
import com.zsmall.extend.shop.amazon.job.entity.mapper.AmznSyncFeedFulfillmentShippingMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【amzn_sync_feed_fulfillment_shipping(亚马逊同步订单履约-运送单)】的数据库操作Service实现
* @createDate 2023-11-03 11:52:41
*/
@Service
public class IAmznSyncFeedFulfillmentShippingServiceImpl extends ServiceImpl<AmznSyncFeedFulfillmentShippingMapper, AmznSyncFeedFulfillmentShipping> {

}




