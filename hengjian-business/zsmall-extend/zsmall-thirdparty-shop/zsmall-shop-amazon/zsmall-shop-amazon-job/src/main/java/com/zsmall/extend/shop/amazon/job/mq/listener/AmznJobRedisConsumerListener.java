package com.zsmall.extend.shop.amazon.job.mq.listener;

import com.hengjian.common.redis.utils.QueueUtils;
import com.zsmall.extend.shop.amazon.job.constants.AmznQueueConstants;
import com.zsmall.extend.shop.amazon.job.mq.AmznJobRedisProducer;
import com.zsmall.extend.shop.amazon.job.support.AmazonFeedJobSupport;
import com.zsmall.extend.shop.amazon.job.support.AmazonOrderJobSupport;
import com.zsmall.extend.shop.amazon.job.utils.ThreadKit;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.Ordered;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 亚马逊定时任务-redis消费者监听
 */
@Slf4j
public class AmznJobRedisConsumerListener implements ApplicationRunner, Ordered {

    @Autowired
    private AmznJobRedisProducer amznJobRedisProducer;
    @Autowired
    private AmazonOrderJobSupport amazonOrderJobSupport;
    @Autowired
    private AmazonFeedJobSupport amazonFeedJobSupport;

    /**
     * @param args
     * @throws Exception
     */
    @Override
    public void run(ApplicationArguments args) throws Exception {
        orderConsumer();
        orderItemConsumer();
        feedTodoConsumer();
        feedFFDCreatedConsumer();
        feedUploadedConsumer();
        feed2FeedCreatedConsumer();
        feedRFDCreatedConsumer();
        log.info("亚马逊定时任务-redis消费者主题订阅监听器成功");
    }

    /**
     * @return
     */
    @Override
    public int getOrder() {
        return -1;
    }

    /**
     * 消费者-订单
     */
    private void orderConsumer() {
        log.info("亚马逊定时任务-初始化订阅orderConsumer");
        // 构造线程
        ExecutorService executorService = ThreadKit.buildExecutorService("orders-", 2, 5);
        int queueCapacity = ThreadKit.getDefaultQueueCapacity();
        // 项目初始化设置一次即可
        QueueUtils.subscribeBlockingQueue(AmznQueueConstants.OrderQueueName.ORDER, (Long jobSyncId) -> {
            // 观察接收时间
            log.info("【消费者 - orderConsumer】通道: {}, 收到数据: {}, starting... ", AmznQueueConstants.OrderQueueName.ORDER, jobSyncId);
            // 开始推送商品数据
            try {
                ThreadPoolExecutor poolExecutor = (ThreadPoolExecutor) executorService;
                int usedSize = poolExecutor.getQueue().size();
                // 判断已使用的数量是否小于默认的队列总数，避免数据丢失
                if(usedSize < queueCapacity) {
                    executorService.execute(() -> amazonOrderJobSupport.getOrdersFromAmzn(jobSyncId));
                } else {
                    // 如果队列已满，则重新加入到延迟队列中
                    amznJobRedisProducer.orderPublishProducer(jobSyncId);
                }

            } catch (Exception e) {
                log.error("【消费者 - orderConsumer】发生异常：{}", e.getMessage(), e);
            } finally {
                log.info("【消费者 - orderConsumer】通道: {}, 收到数据: {}, end!!! ", AmznQueueConstants.OrderQueueName.ORDER, jobSyncId);
            }
        });
    }

    /**
     * 消费者-订单明细
     */
    private void orderItemConsumer() {
        log.info("亚马逊定时任务-初始化订阅orderItemConsumer");
        // 构造线程
        ExecutorService executorService = ThreadKit.buildExecutorService("orderItem-", 2, 5);
        int queueCapacity = ThreadKit.getDefaultQueueCapacity();
        // 项目初始化设置一次即可
        QueueUtils.subscribeBlockingQueue(AmznQueueConstants.OrderQueueName.ORDER_ITEM, (Long jobSyncId) -> {
            // 观察接收时间
            log.info("【消费者 - orderItemConsumer】通道: {}, 收到数据: {}, starting... ", AmznQueueConstants.OrderQueueName.ORDER_ITEM, jobSyncId);
            // 开始推送商品数据
            try {
                ThreadPoolExecutor poolExecutor = (ThreadPoolExecutor) executorService;
                int usedSize = poolExecutor.getQueue().size();
                // 判断已使用的数量是否小于默认的队列总数，避免数据丢失
                if(usedSize < queueCapacity) {
                    executorService.execute(() -> amazonOrderJobSupport.getOrderItemsFromAmzn(jobSyncId));
                } else {
                    // 如果队列已满，则重新加入到延迟队列中
                    amznJobRedisProducer.orderPublishProducer(jobSyncId);
                }
            } catch (Exception e) {
                log.error("【消费者 - orderItemConsumer】发生异常：{}", e.getMessage(), e);
            } finally {
                log.info("【消费者 - orderItemConsumer】通道: {}, 收到数据: {}, end!!! ", AmznQueueConstants.OrderQueueName.ORDER_ITEM, jobSyncId);
            }
        });
    }

    /**
     * 消费者-订单地址
     */
    private void orderAddressConsumer() {
        log.info("亚马逊定时任务-初始化订阅orderAddressConsumer");
        // 构造线程
        ExecutorService executorService = ThreadKit.buildExecutorService("orderItem-", 2, 5);
        int queueCapacity = ThreadKit.getDefaultQueueCapacity();
        // 项目初始化设置一次即可
        QueueUtils.subscribeBlockingQueue(AmznQueueConstants.OrderQueueName.ORDER_ADDRESS, (Long jobSyncId) -> {
            // 观察接收时间
            log.info("【消费者 - orderAddressConsumer】通道: {}, 收到数据: {}, starting... ", AmznQueueConstants.OrderQueueName.ORDER_ADDRESS, jobSyncId);
            // 开始推送商品数据
            try {
                ThreadPoolExecutor poolExecutor = (ThreadPoolExecutor) executorService;
                int usedSize = poolExecutor.getQueue().size();
                // 判断已使用的数量是否小于默认的队列总数，避免数据丢失
                if(usedSize < queueCapacity) {
                    executorService.execute(() -> amazonOrderJobSupport.orderAddressConsumer(jobSyncId));
                } else {
                    // 如果队列已满，则重新加入到延迟队列中
                    amznJobRedisProducer.orderPublishProducer(jobSyncId);
                }
            } catch (Exception e) {
                log.error("【消费者 - orderAddressConsumer】发生异常：{}", e.getMessage(), e);
            } finally {
                log.info("【消费者 - orderAddressConsumer】通道: {}, 收到数据: {}, end!!! ", AmznQueueConstants.OrderQueueName.ORDER_ADDRESS, jobSyncId);
            }
        });
    }

    /**
     * 消费者-Feed未处理
     */
    private void feedTodoConsumer() {
        log.info("亚马逊定时任务-初始化订阅feedTodoConsumer");
        // 构造线程
        ExecutorService executorService = ThreadKit.buildExecutorService("feed-todo-", 1, 3);
        int queueCapacity = ThreadKit.getDefaultQueueCapacity();
        // 项目初始化设置一次即可
        QueueUtils.subscribeBlockingQueue(AmznQueueConstants.FeedQueueName.TODO, (Long feedSyncId) -> {
            // 观察接收时间
            log.info("【消费者 - feedTodoConsumer】通道: {}, 收到数据: {}, starting... ", AmznQueueConstants.FeedQueueName.TODO, feedSyncId);
            // 开始推送商品数据
            try {
                ThreadPoolExecutor poolExecutor = (ThreadPoolExecutor) executorService;
                int usedSize = poolExecutor.getQueue().size();
                // 判断已使用的数量是否小于默认的队列总数，避免数据丢失
                if(usedSize < queueCapacity) {
                    executorService.execute(() -> {
                        amazonFeedJobSupport.createFeedDocument(feedSyncId);
                    });
                } else {
                    // 如果队列已满，则重新加入到延迟队列中
                    amznJobRedisProducer.feedRePublishProducer(AmznQueueConstants.FeedQueueName.TODO, feedSyncId);
                }

            } catch (Exception e) {
                log.error("【消费者 - feedTodoConsumer】发生异常：{}", e.getMessage(), e);
            } finally {
                log.info("【消费者 - feedTodoConsumer】通道: {}, 收到数据: {}, end!!! ", AmznQueueConstants.FeedQueueName.TODO, feedSyncId);
            }
        });
    }
    /**
     * 消费者-Feed FFDCreate
     */
    private void feedFFDCreatedConsumer() {
        log.info("亚马逊定时任务-初始化订阅feedFFDCreatedConsumer");
        // 构造线程
        ExecutorService executorService = ThreadKit.buildExecutorService("feed-ffdcreated-", 1, 3);
        int queueCapacity = ThreadKit.getDefaultQueueCapacity();
        // 项目初始化设置一次即可
        QueueUtils.subscribeBlockingQueue(AmznQueueConstants.FeedQueueName.FFDCREATED, (Long feedSyncId) -> {
            // 观察接收时间
            log.info("【消费者 - feedFFDCreatedConsumer】通道: {}, 收到数据: {}, starting... ", AmznQueueConstants.FeedQueueName.FFDCREATED, feedSyncId);
            // 开始推送商品数据
            try {
                ThreadPoolExecutor poolExecutor = (ThreadPoolExecutor) executorService;
                int usedSize = poolExecutor.getQueue().size();
                // 判断已使用的数量是否小于默认的队列总数，避免数据丢失
                if(usedSize < queueCapacity) {
                    executorService.execute(() -> {
                        amazonFeedJobSupport.uploadFeed(feedSyncId);
                    });
                } else {
                    // 如果队列已满，则重新加入到延迟队列中
                    amznJobRedisProducer.feedRePublishProducer(AmznQueueConstants.FeedQueueName.FFDCREATED, feedSyncId);
                }

            } catch (Exception e) {
                log.error("【消费者 - feedFFDCreatedConsumer】发生异常：{}", e.getMessage(), e);
            } finally {
                log.info("【消费者 - feedFFDCreatedConsumer】通道: {}, 收到数据: {}, end!!! ", AmznQueueConstants.FeedQueueName.FFDCREATED, feedSyncId);
            }
        });
    }
    /**
     * 消费者-Feed Uploaded
     */
    private void feedUploadedConsumer() {
        log.info("亚马逊定时任务-初始化订阅feedUploadedConsumer");
        // 构造线程
        ExecutorService executorService = ThreadKit.buildExecutorService("feed-uploaded-", 1, 3);
        int queueCapacity = ThreadKit.getDefaultQueueCapacity();
        // 项目初始化设置一次即可
        QueueUtils.subscribeBlockingQueue(AmznQueueConstants.FeedQueueName.UPLOADED, (Long feedSyncId) -> {
            // 观察接收时间
            log.info("【消费者 - feedUploadedConsumer】通道: {}, 收到数据: {}, starting... ", AmznQueueConstants.FeedQueueName.UPLOADED, feedSyncId);
            // 开始推送商品数据
            try {
                ThreadPoolExecutor poolExecutor = (ThreadPoolExecutor) executorService;
                int usedSize = poolExecutor.getQueue().size();
                // 判断已使用的数量是否小于默认的队列总数，避免数据丢失
                if(usedSize < queueCapacity) {
                    executorService.execute(() -> {
                        amazonFeedJobSupport.createFeed(feedSyncId);
                    });
                } else {
                    // 如果队列已满，则重新加入到延迟队列中
                    amznJobRedisProducer.feedRePublishProducer(AmznQueueConstants.FeedQueueName.UPLOADED, feedSyncId);
                }

            } catch (Exception e) {
                log.error("【消费者 - feedUploadedConsumer】发生异常：{}", e.getMessage(), e);
            } finally {
                log.info("【消费者 - feedUploadedConsumer】通道: {}, 收到数据: {}, end!!! ", AmznQueueConstants.FeedQueueName.UPLOADED, feedSyncId);
            }
        });
    }
    /**
     * 消费者-Feed FeedCreated
     */
    private void feed2FeedCreatedConsumer() {
        log.info("亚马逊定时任务-初始化订阅feed2FeedCreatedConsumer");
        // 构造线程
        ExecutorService executorService = ThreadKit.buildExecutorService("feed-feedcreated-", 1, 3);
        int queueCapacity = ThreadKit.getDefaultQueueCapacity();
        // 项目初始化设置一次即可
        QueueUtils.subscribeBlockingQueue(AmznQueueConstants.FeedQueueName.FEEDCREATED, (Long feedSyncId) -> {
            // 观察接收时间
            log.info("【消费者 - feed2FeedCreatedConsumer】通道: {}, 收到数据: {}, starting... ", AmznQueueConstants.FeedQueueName.FEEDCREATED, feedSyncId);
            // 开始推送商品数据
            try {
                ThreadPoolExecutor poolExecutor = (ThreadPoolExecutor) executorService;
                int usedSize = poolExecutor.getQueue().size();
                // 判断已使用的数量是否小于默认的队列总数，避免数据丢失
                if(usedSize < queueCapacity) {
                    executorService.execute(() -> {
                        amazonFeedJobSupport.getFeedDocument(feedSyncId);
                    });
                } else {
                    // 如果队列已满，则重新加入到延迟队列中
                    amznJobRedisProducer.feedRePublishProducer(AmznQueueConstants.FeedQueueName.FEEDCREATED, feedSyncId);
                }

            } catch (Exception e) {
                log.error("【消费者 - feed2FeedCreatedConsumer】发生异常：{}", e.getMessage(), e);
            } finally {
                log.info("【消费者 - feed2FeedCreatedConsumer】通道: {}, 收到数据: {}, end!!! ", AmznQueueConstants.FeedQueueName.FEEDCREATED, feedSyncId);
            }
        });
    }
    /**
     * 消费者-Feed RFDCreated
     */
    private void feedRFDCreatedConsumer() {
        log.info("亚马逊定时任务-初始化订阅feedRFDCreatedConsumer");
        // 构造线程
        ExecutorService executorService = ThreadKit.buildExecutorService("feed-rfdcreated-", 1, 3);
        int queueCapacity = ThreadKit.getDefaultQueueCapacity();
        // 项目初始化设置一次即可
        QueueUtils.subscribeBlockingQueue(AmznQueueConstants.FeedQueueName.RFDCREATED, (Long feedSyncId) -> {
            // 观察接收时间
            log.info("【消费者 - feedRFDCreatedConsumer】通道: {}, 收到数据: {}, starting... ", AmznQueueConstants.FeedQueueName.RFDCREATED, feedSyncId);
            // 开始推送商品数据
            try {
                ThreadPoolExecutor poolExecutor = (ThreadPoolExecutor) executorService;
                int usedSize = poolExecutor.getQueue().size();
                // 判断已使用的数量是否小于默认的队列总数，避免数据丢失
                if(usedSize < queueCapacity) {
                    executorService.execute(() -> {
                        amazonFeedJobSupport.updateFeedDocument(feedSyncId);
                    });
                } else {
                    // 如果队列已满，则重新加入到延迟队列中
                    amznJobRedisProducer.feedRePublishProducer(AmznQueueConstants.FeedQueueName.RFDCREATED, feedSyncId);
                }

            } catch (Exception e) {
                log.error("【消费者 - feedRFDCreatedConsumer】发生异常：{}", e.getMessage(), e);
            } finally {
                log.info("【消费者 - feedRFDCreatedConsumer】通道: {}, 收到数据: {}, end!!! ", AmznQueueConstants.FeedQueueName.RFDCREATED, feedSyncId);
            }
        });
    }

}
