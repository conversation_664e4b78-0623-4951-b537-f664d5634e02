package com.zsmall.extend.shop.amazon.job.entity.iservice;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zsmall.extend.shop.amazon.job.entity.domain.AmznSyncOrderItem;
import com.zsmall.extend.shop.amazon.job.entity.mapper.AmznSyncOrderItemMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【amzn_sync_order_item】的数据库操作Service实现
* @createDate 2023-11-01 10:18:56
*/
@Service
public class IAmznSyncOrderItemService extends ServiceImpl<AmznSyncOrderItemMapper, AmznSyncOrderItem> {

    public List<AmznSyncOrderItem> queryByAmazonOrderId(String amazonOrderId) {
        return lambdaQuery().eq(AmznSyncOrderItem::getAmazonOrderId, amazonOrderId).list();
    }

    public AmznSyncOrderItem queryByOrderItemId(String orderItemId) {
        return lambdaQuery().eq(AmznSyncOrderItem::getOrderItemId, orderItemId).one();
    }

}




