package com.zsmall.extend.shop.amazon.job.constants;

import com.hengjian.common.core.constant.GlobalConstants;

public interface AmznQueueConstants {

    /**
     * redis 队列前缀
     */
    String PREFIX_QUEUE_REDIS = GlobalConstants.GLOBAL_REDIS_KEY + "job:queue:amazon:";

    interface OrderQueueName {
        /**
         * 订单同步队列
         */
        String ORDER = PREFIX_QUEUE_REDIS + "order";

        /**
         * 订单明细队列
         */
        String ORDER_ITEM = PREFIX_QUEUE_REDIS + "orderItem";

        String ORDER_ADDRESS = PREFIX_QUEUE_REDIS + "orderAddress";
    }

    interface FeedQueueName {
        /**
         * 待处理
         */
        String TODO = PREFIX_QUEUE_REDIS + "feed:Todo";
        /**
         * FeedDocument已创建
         */
        String FFDCREATED = PREFIX_QUEUE_REDIS + "feed:FFDCreated";
        /**
         * Feed已上传
         */
        String UPLOADED = PREFIX_QUEUE_REDIS + "feed:Uploaded";
        /**
         * 已创建
         */
        String FEEDCREATED = PREFIX_QUEUE_REDIS + "feed:FeedCreated";
        /**
         * ResultFeedDocument已创建
         */
        String RFDCREATED = PREFIX_QUEUE_REDIS + "feed:RFDCreated";
    }

}
