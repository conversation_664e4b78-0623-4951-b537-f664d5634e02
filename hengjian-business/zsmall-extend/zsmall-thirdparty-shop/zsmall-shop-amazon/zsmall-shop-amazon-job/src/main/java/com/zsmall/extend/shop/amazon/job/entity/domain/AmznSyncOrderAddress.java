package com.zsmall.extend.shop.amazon.job.entity.domain;

import com.amazon.client.model.Address;
import com.amazon.client.model.DeliveryPreferences;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * @TableName amzn_sync_order_address
 */
@TableName(value ="amzn_sync_order_address", autoResultMap = true)
@Data
@EqualsAndHashCode
@ToString
public class AmznSyncOrderAddress implements Serializable {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * An Amazon-defined order item identifier.
     */
    private String amazonOrderId;

    /**
     * Company Name of the Buyer.
     */
    private String buyerCompanyName;

    /**
     * The shipping address for the order.
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Address shippingAddress;

    /**
     * Contains all of the delivery instructions provided by the customer for the shipping address.
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private DeliveryPreferences deliveryPreferences;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

}
