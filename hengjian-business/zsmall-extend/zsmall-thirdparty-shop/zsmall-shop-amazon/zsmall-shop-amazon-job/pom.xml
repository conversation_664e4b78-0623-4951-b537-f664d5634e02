<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.zsmall.extend.shop</groupId>
        <artifactId>zsmall-shop-amazon</artifactId>
        <version>${zsmall.version}</version>
    </parent>

    <artifactId>zsmall-shop-amazon-job</artifactId>
    <name>ZS-Mall第三方店铺模块 amazon 任务相关</name>

    <dependencies>

        <dependency>
            <groupId>com.hengjian</groupId>
            <artifactId>hengjian-common-web</artifactId>
        </dependency>

        <dependency>
            <groupId>com.hengjian</groupId>
            <artifactId>hengjian-common-mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <optional>true</optional>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.zsmall.extend.shop</groupId>
            <artifactId>zsmall-shop-amazon-kit</artifactId>
        </dependency>

        <dependency>
            <groupId>com.zsmall.extend.shop</groupId>
            <artifactId>zsmall-shop-amazon-business</artifactId>
        </dependency>
    </dependencies>

</project>
