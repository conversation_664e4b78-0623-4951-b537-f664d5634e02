package com.zsmall.extend.wayfair.model.shipping;

import com.zsmall.extend.wayfair.model.common.Part;
import lombok.Data;

/**
 * A unit that is being shipped out. In this sense, a unit is a single quantity of a single product part in a single box
 */
@Data
public class ShippingUnit {

    /**
     * This is an identifier representing which instance of this particular part this unit belongs to. For instance, if a purchase order has a single part with a quantity of 2, the group will either be 1 or 2. If the group is 1 this shipping unit refers to the first instance of the product.
     */
    private Long groupIdentifier;


    /**
     * This is an identifier representing the box number in the group that this shipping unit belongs to. For instance, if a specific part only has one group, but the group is shipped in 5 boxes the sequences for the group will range from 1 to 5. Note that there can only be one of each.
     */
    private Long sequenceIdentifier;


    /**
     * The part that this shipping unit is associated with.
     */
    private Part part;


}
