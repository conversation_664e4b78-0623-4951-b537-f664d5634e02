package com.zsmall.extend.shop.enums;

import com.hengjian.common.core.domain.RStatusCodeBase;

public enum ZSMallExtendShopStatusCodeEnum implements RStatusCodeBase {

    AUTHORIZE_INFO_LOST("12134", "zsmall.extend.shop.auth.lost"),
    SHOPIFY_HAS_CONNECTED("12135", "zsmall.extend.shop.shopify.connected"),
    SHOPIFY_AUTHENTICATION_EXPIRED("12136", "zsmall.extend.shop.shopify.auth.expire"),
    SALES_CHANNEL_ONLY_BULK("12137", "zsmall.extend.shop.shopify.distributor.only"),

    ;

    /**
     * 响应子编号
     */
    private String subCode;

    /**
     * 信息编号，用于在messages.properties等文件中获取国际化信息
     */
    private String messageCode;

    private Object[] args;

    ZSMallExtendShopStatusCodeEnum(String subCode, String messageCode) {
        this.subCode = subCode;
        this.messageCode = messageCode;
    }

    @Override
    public String getSubCode() {
        return this.subCode;
    }

    @Override
    public String getMessageCode() {
        return this.messageCode;
    }

    public ZSMallExtendShopStatusCodeEnum args(Object... args) {
        this.args = args;
        return this;
    }

    @Override
    public Object[] getArgs() {
        return this.args;
    }
}
