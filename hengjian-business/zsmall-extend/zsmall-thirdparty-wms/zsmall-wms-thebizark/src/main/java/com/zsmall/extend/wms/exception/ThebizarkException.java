package com.zsmall.extend.wms.exception;

/**
 * 恒健仓库异常类
 */
public class ThebizarkException extends RuntimeException {

    private int errorCode;

    private String message;

    public ThebizarkException(final int errorCode, final String message) {
        super("TheBizArk Exception Code: " + errorCode + ", message: " + message);
        this.message = message;
        this.errorCode = errorCode;
    }

    public ThebizarkException(final int errorCode, final String message, Throwable e) {
        super(e);
        this.message = message;
        this.errorCode = errorCode;
    }

    public int getErrorCode() {
        return errorCode;
    }

    @Override
    public String getMessage() {
        return message;
    }
}
