package com.zsmall.order.biz.service;


import cn.hutool.json.JSONObject;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.zsmall.common.domain.dto.*;
import com.zsmall.common.exception.OrderPayException;
import com.zsmall.common.exception.ProductException;
import com.zsmall.common.exception.StockException;
import com.zsmall.order.entity.domain.OrderItemShippingRecord;
import com.zsmall.order.entity.domain.OrderRefund;
import com.zsmall.order.entity.domain.Orders;
import com.zsmall.order.entity.domain.bo.order.*;
import com.zsmall.order.entity.domain.dto.AmazonVCOrderLogisticsAttachmentCallBackDTO;
import com.zsmall.order.entity.domain.dto.ErpSaleOrderDTO;
import com.zsmall.order.entity.domain.mq.EsOrdersDTO;
import com.zsmall.order.entity.domain.openapi.OpenApiOrdersDeliveryDTO;
import com.zsmall.order.entity.domain.vo.OrderListVo;
import com.zsmall.order.entity.domain.vo.order.OrderDetailVo;
import com.zsmall.order.entity.domain.vo.order.OrderPageVo;
import org.dromara.easyes.core.conditions.select.LambdaEsQueryWrapper;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.ParseException;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Set;

/**
 * 主订单Service接口
 *
 * <AUTHOR>
 * @date 2023-06-07
 */
public interface OrdersService {

    /**
     * 查询主订单
     */
    OrderDetailVo queryByOrderNo(String orderNo);

    /**
     * 查询主订单列表
     */
    TableDataInfo<OrderListVo> queryPageList(OrdersPageBo dto, PageQuery pageQuery);

    /**
     * 支付订单
     */
    R<Void> payOrder(OrderPayBo bo) throws Exception;

    /**
     * 支付订单，走队列
     * @param bo
     * @return
     * @throws Exception
     */
    R<Void> payOrderQueue(OrderPayBo bo) throws Exception;
    R<Void> payOrderForTest(OrderPayBo bo) throws Exception;
    R<Void> payOrderForV3(OrderPayBo bo) throws Exception;

    /**
     * 重新创建WMS订单
     * @param bo
     * @return
     */
    R<Void> recreateWMSOrder(OrderNoBo bo);


    /**
     * 查询订单集合
     * @param bo
     * @return
     */
    List<OrderPageVo> getOrders(OrderNosBo bo);

    /**
     * 功能描述：设置订单业务字段
     *
     * @param orderReceiveFromThirdDTO 从ERP DTO接收订单
     * @param orders                 订单
     * @return {@link Orders }
     * <AUTHOR>
     * @date 2024/01/11
     */
    Orders setOrderBusinessField(OrderReceiveFromThirdDTO orderReceiveFromThirdDTO, Orders orders);

    Orders setOrderBusinessFieldForOpen(OrderReceiveFromThirdDTO orderReceiveFromThirdDTO, Orders orders);

    Orders setOrderBusinessFieldForTemu(TemuOrderDTO temuOrderDTO, Orders orders) throws ParseException;
    Orders setOrderBusinessFieldForAmazonVc(AmazonVCOrderMessageDTO amazonVCOrderMessageDTO, Orders orders) throws ParseException;
    Orders setOrderBusinessFieldForEc(EcOrderMessageDTO ecOrderMessageDTO, Orders orders) throws ParseException;

    Orders setOrderBusinessFieldForAmazonSc(AmazonScOrderDTO amazonScOrderDTO, Orders orders) throws ParseException;


    /**
     * 功能描述：设置订单业务字段
     *
     * @param erpSaleOrderDTO ERP销售订单DTO
     * @param orders          订单
     * @return {@link Orders }
     * <AUTHOR>
     * @date 2024/01/12
     */
    Orders setOrderBusinessField(ErpSaleOrderDTO erpSaleOrderDTO, Orders orders);

    /**
     * 功能描述：设置订单标签系统
     *
     * @param orderReceiveFromThirdDTO 从ERP DTO接收订单
     * @param orders                 订单
     * @return {@link Orders }
     * <AUTHOR>
     * @date 2024/01/11
     */
    Orders setOrderTagSystem(OrderReceiveFromThirdDTO orderReceiveFromThirdDTO, Orders orders);

    /**
     *
     * @param temuOrderDTO
     * @param orders
     * @return
     */
    Orders setOrderTagSystemByTemu(TemuOrderDTO temuOrderDTO, Orders orders);

    Orders setOrderTagSystemByAmazonVc(AmazonVCOrderMessageDTO amazonVCOrderMessageDTO, Orders orders);
    Orders setOrderTagSystemByEc(EcOrderMessageDTO ecOrderMessageDTO, Orders orders);

    Orders setOrderTagSystemByAmazonSc(AmazonScOrderDTO amazonScOrderDTO, Orders orders);

    /**
     * 功能描述：设置订单标签系统
     *
     * @param erpSaleOrderDTO ERP销售订单DTO
     * @param orders          订单
     * @return {@link Orders }
     * <AUTHOR>
     * @date 2024/01/12
     */
    Orders setOrderTagSystem(ErpSaleOrderDTO erpSaleOrderDTO, Orders orders);
    /**
     * 功能描述：设置频道标签
     *
     * @param orderReceiveFromThirdDTO 从ERP DTO接收订单
     * @param orders                 订单
     * @return {@link Orders }
     * <AUTHOR>
     * @date 2024/01/11
     */
    Orders setChannelTag(OrderReceiveFromThirdDTO orderReceiveFromThirdDTO, Orders orders);

    Orders setChannelTagForOpen(OrderReceiveFromThirdDTO orderReceiveFromThirdDTO, Orders orders);

    Orders setChannelTagForTemu(TemuOrderDTO temuOrderDTO, Orders orders) throws ParseException;

    Orders setChannelTagForAmazonVc(AmazonVCOrderMessageDTO amazonVCOrderMessageDTO, Orders orders) throws ParseException;
    Orders setChannelTagForEc(EcOrderMessageDTO ecOrderMessageDTO, Orders orders) throws ParseException;

    Orders setChannelTagForAmazonSc(AmazonScOrderDTO amazonScOrderDTO, Orders orders) throws ParseException;

    /**
     * 功能描述：设置频道标签
     *
     * @param erpSaleOrderDTO 分销销售订单DTO
     * @param orders          订单
     * @return {@link Orders }
     * <AUTHOR>
     * @date 2024/01/12
     */
    Orders setChannelTag(ErpSaleOrderDTO erpSaleOrderDTO, Orders orders);

    boolean payOrderForErp(OrderPayBo bo, String tenantId, boolean isGenuinePay, boolean isNeedCreate) throws OrderPayException, Exception;

    boolean payOrderOnlyErp(OrderPayBo bo, String tenantId, boolean isGenuinePay, boolean isNeedCreate) throws OrderPayException, Exception;

    boolean payOrderForDistribution(OrderPayBo bo, String tenantId, boolean isGenuinePay, boolean isNeedCreate) throws OrderPayException, Exception;

    R<Void> export(OrdersPageBo dto, HttpServletResponse response, PageQuery pageQuery) ;

    List<OrderPageVo>  getOrdersByOrderNosAndTenantId(Collection<String> orderNos, String tenantId);

    R<Void> exportNew(OrdersPageBo ordersPageBo, HttpServletResponse response);

    /**
     * 标记订单
     * @param orderNos 订单编号
     */
    void batchTaggingOrders(List<String> orderNos);

    void restoreOrders(List<String> orderNo);

    R<Void> payOrderForV4(OrderPayBo bo) throws OrderPayException, StockException, ProductException;

    void updateOrSetNull(Orders orders);

    /**
     * 功能描述：需要先更新业务操作的订单,再二次更新null的业务,多用于初始化的订单
     *
     * @param updateOrderList 更新订单列表
     * @param codesMap
     * <AUTHOR>
     * @date 2024/07/31
     */
    void updateBatchOrSetNull(List<Orders> updateOrderList, HashMap<String, Integer> codesMap);

    /**
     * 功能描述：只进行一次更新的业务,用于库存业务流程的场景
     *
     * @param orders             订单
     * @param orderExceptionCode 订单例外代码
     * <AUTHOR>
     * @date 2024/08/05
     */
    void updateOrSetNull(List<Orders> orders, Integer orderExceptionCode);


    /**
     * 功能描述：更新批量补偿
     *
     * @param needCompensateOrders 需要补偿订单
     * @return {@link List }<{@link String }>
     * <AUTHOR>
     * @date 2024/08/23
     */
    List<String> updateBatchCompensate(List<Orders> needCompensateOrders);
    /**
     * @description: 支付失败且是自提的订单修改仓库编码
     * @author: Len
     * @date: 2024/10/24 10:43
     * @param: orderNo
     * @param: warehouseSystemCode
     **/
    void updateOrderWarehouseCode(String orderNo, String warehouseSystemCode);

    /**
     * 功能描述：根据订单号获取物流面单附件
     * @param orderNoList
     */
    void getOrderLogisticsAttachment(List<String> orderNoList);

    /**
     * 功能描述：处理物流面单附件
     * @param amazonVCOrderLogisticsAttachmentCallBackDTO
     */
    void orderLogisticsAttachmentDeal(AmazonVCOrderLogisticsAttachmentCallBackDTO amazonVCOrderLogisticsAttachmentCallBackDTO);

    /**
     * 功能描述：设置订单物流信息
     * @param bo
     */
    void setOrderTrackingInfo(SetOrderTrackingInfoBo bo);

    Orders setSiteField(OrderReceiveFromThirdDTO orderReceiveFromThirdDTO, Orders orders);

    /**
     *  订单附件导出
     * @param dto
     * @param response
     */
    void exportOrderAttachment(OrdersPageBo dto, HttpServletResponse response);
    Orders setOrderBusinessFieldOnlyErp(OrderReceiveFromThirdDTO orderReceiveFromThirdDTO, Orders orders);

    Orders setOrderTagSystemOnlyErp(OrderReceiveFromThirdDTO orderReceiveFromThirdDTO, Orders orders);

    Orders setChannelTagOnlyErp(OrderReceiveFromThirdDTO orderReceiveFromThirdDTO, Orders orders);
    JSONObject getAbnormalServiceMessage(Orders orders, OrderReceiveFromThirdDTO orderReceiveFromThirdDTO);

    /**
     * 待发货订单取消
     * @param orderNo
     */
    void cancelOrder(String orderNo);

    /**
     * 更新订单取消状态
     * @param orderNo
     * @param cancelStatus
     */
    void updateOrderCancelStatus(String orderNo, Integer cancelStatus) throws Exception;

    List<OrderRefund> getOrderRefundsByOrderExtendId(String orderExtendId);

    void refuseCancel(List<String> orderRefundNo, String orderExtendId, Boolean supplierCall) throws Exception;

    void agreeSuccess(List<String> orderRefundNo, String orderExtendId, Boolean supplierCall);

    /**
     * 功能描述：canceling2取消流程
     *
     * <AUTHOR>
     * @date 2025/03/20
     */
    void canceling2CancelFlow();
    void testSysConfig(String key);
    void batchSetTracking(MultipartFile dto) throws IOException;

    /**
     * OpenApi供应商订单发货
     * @param shippingRecordList
     * @param ordersDeliveryDTO
     */
    void openApiOrderDelivery(List<OrderItemShippingRecord> shippingRecordList, OpenApiOrdersDeliveryDTO ordersDeliveryDTO);


    OrdersPageBo getOrdersPageBo(OrdersPageBo dto);


    LambdaEsQueryWrapper<EsOrdersDTO> getEsOrdersQueryWrapper(OrdersPageBo dto);
    /**
     *  订单附件导出ByES
     * @param dto
     * @param response
     */
    void exportOrderAttachmentByEs(OrdersPageBo dto, HttpServletResponse response);
    /**
     * 使用ES导出订单
     * @param ordersPageBo
     * @param response
     * @return
     */
    R<Void> exportNewByEs(OrdersPageBo ordersPageBo, HttpServletResponse response);
    /**
     * 数据库数据清洗进ES
     */
    void dealOrderDataByInsertEs();

    /**
     * 数据库数据清洗进ES（不使用多线程，避免内存溢出）
     */
    void dealOrderDataByInsertEsWithoutThreads();

    /**
     * 处理ES查询结果
     * @param list
     * @return
     */
    List<OrderListVo> dealOrderEsSearchListData(List<EsOrdersDTO> list);

    List<EsOrdersDTO>  getEsOrderDateByOrderNo(Set<String> orderNos);

    void checkEsOrderDate();

    void insertOrderDateToEsByOrderNos(String orderNos);
}
