package com.zsmall.order.biz.job.order;

import com.xxl.job.core.handler.annotation.XxlJob;
import com.zsmall.common.enums.order.OrderExceptionEnum;
import com.zsmall.order.biz.support.OrderExceptionSupport;
import com.zsmall.order.entity.domain.Orders;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2024年11月26日  11:44
 * @description: 订单异常补偿任务
 */
@Slf4j
@Service
public class OrderExceptionHandlerJob {

    @Resource
    OrderExceptionSupport orderExceptionSupport;

    @XxlJob("orderLogisticsAttachmentExceptionHandler")
    public void orderLogisticsAttachmentExceptionHandler() throws Exception{
        orderExceptionSupport.orderLogisticsAttachmentException();
    }

}
