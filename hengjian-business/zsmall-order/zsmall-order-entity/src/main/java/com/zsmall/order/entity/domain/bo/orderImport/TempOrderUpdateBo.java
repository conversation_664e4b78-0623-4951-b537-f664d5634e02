package com.zsmall.order.entity.domain.bo.orderImport;

import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 请求体-临时订单更新
 *
 * <AUTHOR>
 * @date 2023/6/12
 */
@Data
public class TempOrderUpdateBo {

    /**
     * 临时订单号（都需要）
     */
    private String tempOrderNo;

    private Long attachmentOssId;

    /**
     * 地址信息（更新地址信息需要）
     */
    private AddressInfo addressInfo;

    /**
     * 物流信息（更新物流信息需要）
     */
    private LogisticsInfo logisticsInfo;

    /**
     * 渠道信息（更新渠道信息需要）
     */
    private ChannelInfo channelInfo;

    /**
     * 文件（上传快递标签、上传订单附件需要）
     */
    private MultipartFile multipartFile;

    /**
     * 功能描述：临时订单更新bo
     *
     * @param tempOrderNo 温度订单号
     * @param ossId       oss id
     * <AUTHOR>
     * @date 2025/05/22
     */
    public TempOrderUpdateBo(String tempOrderNo, Long ossId) {
        this.tempOrderNo = tempOrderNo;
        this.attachmentOssId = ossId;
    }

    @Data
    public static class AddressInfo {
        /**
         * 临时订单号
         */
        private String tempOrderNo;
        /**
         * 收件人名称
         */
        private String recipientName;

        /**
         * 地址1
         */
        private String address1;
        /**
         * 地址2
         */
        private String address2;
        /**
         * 地址3
         */
        private String address3;
        /**
         * 城市
         */
        private String city;
        /**
         * 手机号
         */
        private String phoneNumber;
        /**
         * 邮编
         */
        private String zipCode;
        /**
         * 国家
         */
        private Long countryId;
        /**
         * 国家
         */
        private String country;
        /**
         * 州/省/地区
         */
        private Long stateId;
        /**
         * 州/省/地区
         */
        private String state;
    }

    @Data
    public static class LogisticsInfo {
        /**
         * 临时订单号
         */
        private String tempOrderNo;
        /**
         * 是否选择第三方物流
         */
        private Boolean logisticsThirdBilling;
        /**
         * 第三方物流商（UPS，FedEx）
         */
        private String logisticsCarrier;
        /**
         * 物流服务名称
         */
        private String logisticsServiceName;
        /**
         * 第三方物流发货商账号
         */
        private String logisticsAccount;
        /**
         * 第三方发货商账号邮编
         */
        private String logisticsAccountZipCode;
        /**
         * 物流类型（PickUp-自提，DropShipping-代发商品）
         */
        private String logisticsType;
        /**
         * 仓库编号
         */
        private String warehouseCode;
        /**
         * 仓库系统唯一编号
         */
        private String warehouseSystemCode;
        /**
         * 物流跟踪单号集合
         */
        private List<String> logisticsTrackingNo;
        /**
         * ItemNo
         */
        private String productSkuCode;
        /**
         * 购物车订单
         */
        private Boolean isMarketplace;
    }

    @Data
    public static class ChannelInfo {
        /**
         * 临时订单号
         */
        private String tempOrderNo;
        /**
         * 渠道订单号
         */
        private String storeOrderId;
        /**
         * 渠道商店名称
         */
        private String storeName;

        private String channelType;
    }

    public TempOrderUpdateBo(String tempOrderNo) {
        this.tempOrderNo = tempOrderNo;
    }

    public TempOrderUpdateBo(String tempOrderNo, AddressInfo addressInfo) {
        this.tempOrderNo = tempOrderNo;
        this.addressInfo = addressInfo;
    }

    public TempOrderUpdateBo(String tempOrderNo, LogisticsInfo logisticsInfo) {
        this.tempOrderNo = tempOrderNo;
        this.logisticsInfo = logisticsInfo;
    }

    public TempOrderUpdateBo(String tempOrderNo,TempOrderUpdateBo bo) {
        this.tempOrderNo = tempOrderNo;
        this.logisticsInfo = bo.logisticsInfo;
        this.channelInfo = bo.channelInfo;
        this.addressInfo = bo.addressInfo;
    }


    public TempOrderUpdateBo(String tempOrderNo, ChannelInfo channelInfo) {
        this.tempOrderNo = tempOrderNo;
        this.channelInfo = channelInfo;
    }

    public TempOrderUpdateBo(String tempOrderNo, MultipartFile multipartFile) {
        this.tempOrderNo = tempOrderNo;
        this.multipartFile = multipartFile;
    }
}
