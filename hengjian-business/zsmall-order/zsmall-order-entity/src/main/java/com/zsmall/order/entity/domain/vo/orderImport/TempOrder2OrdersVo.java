package com.zsmall.order.entity.domain.vo.orderImport;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * 响应体-临时订单转正式订单
 *
 * <AUTHOR>
 * @date 2022-08-09
 **/
@Data
@NoArgsConstructor
public class TempOrder2OrdersVo {

    /**
     * 订单号集合
     */
    private List<String> orderNoList;

    /**
     * 所有商品费用
     */
    private BigDecimal allProductPrice;

    /**
     * 所有运费
     */
    private BigDecimal allShippingFee;

    /**
     * 所有订单费用
     */
    private BigDecimal allTotalPrice;

}
