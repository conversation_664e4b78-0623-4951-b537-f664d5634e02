package com.zsmall.order.entity.domain.vo;

import cn.hutool.json.JSONObject;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.hengjian.common.excel.annotation.ExcelDictFormat;
import com.hengjian.common.excel.convert.ExcelDictConvert;
import com.zsmall.order.entity.domain.OrderLogisticsInfo;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serializable;


/**
 * 主订单物流信息视图对象 order_logistics_info
 *
 * <AUTHOR>
 * @date 2023-06-07
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = OrderLogisticsInfo.class)
public class OrderLogisticsInfoVo implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 订单表主键
     */
    @ExcelProperty(value = "订单表主键")
    private Long orderId;

    /**
     * 主订单编号
     */
    @ExcelProperty(value = "主订单编号")
    private String orderNo;

    /**
     * 物流类型：PickUp-自提，DropShipping-代发
     */
    @ExcelProperty(value = "物流类型：PickUp-自提，DropShipping-代发")
    private String logisticsType;

    /**
     * 物流错误信息
     */
    @ExcelProperty(value = "物流错误信息")
    private JSONObject logisticsErrorMessage;

    /**
     * 物流公司名称
     */
    @ExcelProperty(value = "物流公司名称")
    private String logisticsCompanyName;

    /**
     * 物流服务名称
     */
    @ExcelProperty(value = "物流服务名称")
    private String logisticsServiceName;

    /**
     * 物流账户
     */
    @ExcelProperty(value = "物流账户")
    private String logisticsAccount;

    /**
     * 物流账户邮政编码
     */
    @ExcelProperty(value = "物流账户邮政编码")
    private String logisticsAccountZipCode;

    /**
     * 是否存在运输标签（0-否，1-是）
     */
    @ExcelProperty(value = "是否存在运输标签", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "0=-否，1-是")
    private Boolean shippingLabelExist;

    /**
     * 运输标签源文件名
     */
    @ExcelProperty(value = "运输标签源文件名")
    private String shippingLabelFileName;

    /**
     * 运输标签存储对象主键
     */
    @ExcelProperty(value = "运输标签存储对象主键")
    private Long shippingLabelOssId;

    /**
     * 运输标签存储地址
     */
    @ExcelProperty(value = "运输标签存储地址")
    private String shippingLabelSavePath;

    /**
     * 运输标签展示地址
     */
    @ExcelProperty(value = "运输标签展示地址")
    private String shippingLabelShowUrl;

    /**
     * 物流用邮编（匹配物流模板只需要用到五位数的邮编，但是美国有可能会出现xxxxx-xxxx的格式，此处存放的就是切割后前半部分的五位数邮编）
     */
    @ExcelProperty(value = "物流用邮编", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "匹=配物流模板只需要用到五位数的邮编，但是美国有可能会出现xxxxx-xxxx的格式，此处存放的就是切割后前半部分的五位数邮编")
    private String logisticsZipCode;

    /**
     * 物流用国家代号（两位英文大写）
     */
    @ExcelProperty(value = "物流用国家代号", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "物流用国家代号（两位英文大写）")
    private String logisticsCountryCode;

    /**
     * 完整邮编
     */
    @ExcelProperty(value = "完整邮编")
    private String zipCode;


}
