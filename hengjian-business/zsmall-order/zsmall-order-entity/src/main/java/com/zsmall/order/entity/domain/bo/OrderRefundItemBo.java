package com.zsmall.order.entity.domain.bo;

import com.hengjian.common.core.validate.AddGroup;
import com.hengjian.common.core.validate.EditGroup;
import com.hengjian.common.mybatis.core.domain.BaseEntity;
import com.zsmall.order.entity.domain.OrderRefundItem;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 售后申请子单业务对象 order_refund_item
 *
 * <AUTHOR>
 * @date 2023-06-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = OrderRefundItem.class, reverseConvertGenerate = false)
public class OrderRefundItemBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 主订单表主键
     */
    @NotNull(message = "主订单表主键不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long orderId;

    /**
     * 子订单表主键
     */
    @NotNull(message = "子订单表主键不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long orderItemId;

    /**
     * 主订单编号
     */
    @NotBlank(message = "主订单编号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String orderNo;

    /**
     * 子订单编号
     */
    @NotBlank(message = "子订单编号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String orderItemNo;

    /**
     * 售后申请主单表主键
     */
    @NotNull(message = "售后申请主单表主键不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long orderRefundId;

    /**
     * 售后申请主单编号
     */
    @NotBlank(message = "售后申请主单编号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String orderRefundNo;

    /**
     * 售后申请子单编号
     */
    @NotBlank(message = "售后申请子单编号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String orderRefundItemNo;

    /**
     * 售后商品数量
     */
    @NotNull(message = "售后商品数量不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer refundQuantity;

    /**
     * Sku唯一编号（ItemNo.）
     */
    @NotBlank(message = "Sku唯一编号（ItemNo.）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String productSkuCode;

    /**
     * 参与活动类型（为空代表未参与活动）
     */
    @NotBlank(message = "参与活动类型（为空代表未参与活动）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String activityType;

    /**
     * 参与活动编号（为空代表未参与活动）
     */
    @NotBlank(message = "参与活动编号（为空代表未参与活动）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String activityCode;

    /**
     * 原始应付总金额（供货商）
     */
    @NotNull(message = "原始应付总金额（供货商）不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal originalPayableTotalAmount;

    /**
     * 原始已预付总金额（供货商）
     */
    @NotNull(message = "原始已预付总金额（供货商）不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal originalPrepaidTotalAmount;

    /**
     * 原始实际支付总金额（供应商）
     */
    @NotNull(message = "原始实际支付总金额（供应商）不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal originalActualTotalAmount;

    /**
     * 平台应付总金额（平台、分销商）
     */
    @NotNull(message = "平台应付总金额（平台、分销商）不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal platformPayableTotalAmount;

    /**
     * 平台已预付总金额（平台、分销商）
     */
    @NotNull(message = "平台已预付总金额（平台、分销商）不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal platformPrepaidTotalAmount;

    /**
     * 平台实际支付总金额（平台、分销商）
     */
    @NotNull(message = "平台实际支付总金额（平台、分销商）不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal platformActualTotalAmount;


}
