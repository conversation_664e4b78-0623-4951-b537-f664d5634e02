package com.zsmall.order.entity.domain.vo.orderItem;

import lombok.Getter;
import lombok.Setter;

/**
 * 子订单信息（提供给第三方仓库事件使用）
 *
 * <AUTHOR>
 * @date 2023/6/17
 */
@Setter
@Getter
public class OrderItem4ThirdWarehouseVo {

    private String erpSku;
    private Integer totalQuantity;
    private String countryCode;
    private String stateCode;
    private String city;
    private String recipient;
    private String phoneNumber;
    private String address1;
    private String address2;
    private String logisticsAccount;
    private String logisticsZipCode;

}
