package com.zsmall.order.entity.domain.vo.wholesaleOrder;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.LinkedList;
import java.util.List;

/**
 * 响应体-批发订单送货地址信息
 *
 * <AUTHOR>
 * @date 2023/2/24
 */
@Data
@NoArgsConstructor
@Schema(name = "响应体-批发订单送货地址信息")
public class WholesaleOrderAddressVo {

    /**
     * 收件地址-国家
     */
    private String recipientCountry;
    /**
     * 收件地址-国家Code
     */
    private String recipientCountryCode;
    /**
     * 收件地址-州/省
     */
    private String recipientState;
    /**
     * 收件地址-州/省Code
     */
    private String recipientStateCode;
    /**
     * 收件地址-城市
     */
    private String recipientCity;
    /**
     * 收件地址-详细地址1
     */
    private String recipientAddress1;
    /**
     * 收件地址-详细地址2
     */
    private String recipientAddress2;
    /**
     * 收件地址-邮编
     */
    private String recipientZipCode;
    /**
     * 收件人姓名
     */
    private String recipientName;
    /**
     * 收件人电话
     */
    private String recipientPhone;
    /**
     * 完整地址
     */
    private String intactAddress;

    /**
     * 生成完整地址
     *
     * @return
     */
    public String generateIntactAddress() {
        List<String> addressList = new LinkedList<>();
        addressList.add(StrUtil.isNotBlank(recipientAddress2) ? recipientAddress2 : null);
        addressList.add(StrUtil.isNotBlank(recipientAddress1) ? recipientAddress1 : null);
        addressList.add(recipientCity);
        addressList.add(recipientState);
        addressList.add(recipientCountry);
        CollUtil.removeNull(addressList);
        this.intactAddress = CollUtil.join(addressList, ", ");
        return this.intactAddress;
    }

}
