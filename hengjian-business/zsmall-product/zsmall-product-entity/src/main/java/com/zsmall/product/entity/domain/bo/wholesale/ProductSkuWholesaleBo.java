package com.zsmall.product.entity.domain.bo.wholesale;

import com.zsmall.product.entity.domain.bo.product.ProductAttachmentBo;
import com.zsmall.product.entity.domain.bo.product.ProductIntactInfoUpdateBo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;


/**
 * 通用参数-国外现货批发商品sku信息
 */
@Data
@Schema(name = "通用参数-国外现货批发商品sku信息")
public class ProductSkuWholesaleBo {

    /**
     * productSkuId
     */
    private Long id;
    /**
     * skuCode
     */
    private String skuCode;
    /**
     * 商品统一代码
     */
    private String upc;
    /**
     * erpSku
     */
    private String erpSku;
    /**
     * 规格属性值集合
     */
    private List<ProductIntactInfoUpdateBo.SpecComposeList> specComposeList;
    /**
     * 属性
     */
    private String specId;
    /**
     * 规格值名称，示例：大/白色
     */
    private String specValName;
    /**
     * 规格组成名称，示例：尺寸-大;颜色-白色
     */
    private String specComposeName;
    /**
     * 数量
     */
    private Integer quantity;
    /**
     * 重量
     */
    private BigDecimal weight;
    /**
     * 长
     */
    private BigDecimal length;
    /**
     * 宽
     */
    private BigDecimal width;
    /**
     * 高
     */
    private BigDecimal height;
    /**
     * 重量单位
     */
    private String weightUnit;
    /**
     * 长度单位
     */
    private String lengthUnit;
    /**
     * 打包重量
     */
    private BigDecimal packWeight;
    /**
     * 打包长
     */
    private BigDecimal packLength;
    /**
     * 打包宽
     */
    private BigDecimal packWidth;
    /**
     * 打包高
     */
    private BigDecimal packHeight;
    /**
     * 打包重量单位
     */
    private String packWeightUnit;
    /**
     * 打包长度单位
     */
    private String packLengthUnit;
    /**
     * 商品尺寸与打包尺寸是否一致
     */
    private Boolean samePacking = false;
    /**
     * 图片数组
     */
    private List<ProductAttachmentBo> imageList;
    /**
     * 视频数组
     */
    private List<ProductAttachmentBo> videoList;


}
