package com.zsmall.product.entity.domain.bo.product;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 请求参数-商品SKU
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@Schema(name = "请求参数-商品SKU")
public class ReqProductSkuListBody {

    @Schema(title = "同步状态：Synced-已同步；NotSynced-未同步")
    private String syncStatus;

    @Schema(title = "商品编号")
    private String productCode;

    @Schema(title = "skuCode")
    private String skuCode;


    @Schema(title = "ItemNo集合")
    private List<String> itemNoList;

}
