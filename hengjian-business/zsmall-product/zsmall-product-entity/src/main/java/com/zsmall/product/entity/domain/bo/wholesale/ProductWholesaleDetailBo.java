package com.zsmall.product.entity.domain.bo.wholesale;

import cn.hutool.json.JSONObject;
import com.hengjian.common.core.validate.AddGroup;
import com.hengjian.common.core.validate.EditGroup;
import com.hengjian.common.mybatis.core.domain.BaseEntity;
import com.zsmall.product.entity.domain.ProductWholesaleDetail;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 国外现货批发商品详情业务对象 product_wholesale_detail
 *
 * <AUTHOR> Li
 * @date 2023-05-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ProductWholesaleDetail.class, reverseConvertGenerate = false)
public class ProductWholesaleDetailBo extends BaseEntity {

    /**
     * 主键
     */
    private Long id;

    /**
     * 商品主键
     */
    private Long productId;

    /**
     * 最小起订数量
     */
    @NotNull(message = "最小起订数量不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer minimumQuantity;

    /**
     * 订金比例
     */
    @NotNull(message = "订金比例不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal depositRatio;

    /**
     * 预留时间（天）
     */
    @NotNull(message = "预留时间（天）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer reservedTime;

    /**
     * 发货方式
     */
    @NotBlank(message = "发货方式不能为空", groups = { AddGroup.class, EditGroup.class })
    private JSONObject deliveryType;

    /**
     * 仓库编码
     */
    @NotBlank(message = "仓库编码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String warehouseSystemCode;

    /**
     * 物流模板编号
     */
    @NotBlank(message = "物流模板编号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String logisticsTemplateNo;


}
