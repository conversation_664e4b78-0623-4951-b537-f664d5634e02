package com.zsmall.product.entity.domain.bo.product;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 请求参数-分页查询sku映射
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@Schema(name = "请求参数-分页查询sku映射")
public class ReqSkuMappingPageBody {

    @Schema(title = "渠道类型")
    private String channelType;

    @Schema(title = "渠道id集合")
    private List<Long> channelIds;

    @Schema(title = "查询正文（sku、商品名等）")
    private String queryContent;

}
