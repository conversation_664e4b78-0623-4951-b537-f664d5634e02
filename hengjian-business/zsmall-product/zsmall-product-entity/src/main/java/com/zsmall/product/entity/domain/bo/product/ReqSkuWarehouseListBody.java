package com.zsmall.product.entity.domain.bo.product;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 请求参数-查询SKU仓库配置
 *
 * <AUTHOR>
 * @create 2022/3/14 19:02
 */
@Data
@NoArgsConstructor
@Schema(name = "请求参数-查询SKU仓库配置")
public class ReqSkuWarehouseListBody {

    @Schema(title = "商品编号")
    private String productCode;

    @Schema(title = "商品sku")
    private String sku;

}
