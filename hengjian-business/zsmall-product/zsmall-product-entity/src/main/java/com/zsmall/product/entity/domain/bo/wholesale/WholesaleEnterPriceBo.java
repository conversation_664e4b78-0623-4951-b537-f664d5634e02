package com.zsmall.product.entity.domain.bo.wholesale;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 请求体-国外现货批发订单录入价格
 *
 * <AUTHOR>
 * @date 2023/2/22
 */
@Data
@NoArgsConstructor
public class WholesaleEnterPriceBo {

    /**
     * 国外现货批发订单编号
     */
    private String wiOrderNo;

    /**
     *操作费
     */
    private BigDecimal operationFee;

    /**
     *运费
     */
    private BigDecimal shippingFee;

    /**
     *处理时间
     */
    private Integer handleTime;

}
