package com.zsmall.product.controller;

import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.validate.AddGroup;
import com.hengjian.common.core.validate.EditGroup;
import com.hengjian.common.excel.utils.ExcelUtil;
import com.hengjian.common.idempotent.annotation.RepeatSubmit;
import com.hengjian.common.log.annotation.Log;
import com.hengjian.common.log.enums.BusinessType;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.common.web.core.BaseController;
import com.zsmall.product.biz.service.IProductLabelService;
import com.zsmall.product.entity.domain.bo.ProductLabelBo;
import com.zsmall.product.entity.domain.bo.label.ReqLabelBindingBatchBody;
import com.zsmall.product.entity.domain.bo.label.ReqLabelBindingBody;
import com.zsmall.product.entity.domain.bo.product.ReqProductBody;
import com.zsmall.product.entity.domain.vo.ProductLabelVo;
import com.zsmall.product.entity.domain.vo.product.ProductToLabelBody;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 标签
 *
 * <AUTHOR> Li
 * @date 2023-05-31
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/product/label")
public class LabelController extends BaseController {

    private final IProductLabelService productLabelService;

    /**
     * 翻页查询标签列表
     */
    @GetMapping("/page")
    public TableDataInfo<ProductLabelVo> page(ProductLabelBo bo, PageQuery pageQuery) {
        return productLabelService.queryPageList(bo, pageQuery);
    }

    /**
     * 不翻页查询标签列表
     */
    @GetMapping("/list")
    public List<ProductLabelVo> list(ProductLabelBo bo) {
        return productLabelService.queryList(bo);
    }

    /**
     * 导出标签列表
     */
    @Log(title = "标签", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(ProductLabelBo bo, HttpServletResponse response) {
        List<ProductLabelVo> list = productLabelService.queryList(bo);
        ExcelUtil.exportExcel(list, "标签", ProductLabelVo.class, response, false);
    }

    /**
     * 获取标签详细信息
     *
     * @param id 主键
     */
    @GetMapping("/{id}")
    public R<ProductLabelVo> getInfo(@NotNull(message = "主键不能为空")
    @PathVariable Long id) {
        return R.ok(productLabelService.queryById(id));
    }

    /**
     * 新增标签
     */
    @Log(title = "标签", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody ProductLabelBo bo) {
        try {
            return toAjax(productLabelService.insertByBo(bo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 修改标签
     */
    @Log(title = "标签", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody ProductLabelBo bo) {
        try {
            return toAjax(productLabelService.updateByBo(bo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除标签
     *
     * @param ids 主键串
     */
    @Log(title = "标签", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids) {
        return toAjax(productLabelService.deleteWithValidByIds(List.of(ids), true));
    }

    /**
     * 更新标签排序
     *
     * @param bo
     * @return
     */
    @PostMapping("/updateSort")
    public R<Void> updateSort(@Validated(EditGroup.class) @RequestBody ProductLabelBo bo) {
        return toAjax(productLabelService.updateSort(bo));
    }

    /**
     * 商品绑定标签
     *
     * @param requestBody
     * @return
     */
    @PostMapping("/binding")
    public R<Void> binding(@RequestBody ReqLabelBindingBody requestBody) throws Exception {
        return productLabelService.binding(requestBody);
    }

    /**
     * 商品批量绑定标签
     *
     * @param requestBody
     * @return
     */
    @PostMapping("/bindingBatch")
    public R<Void> bindingBatch(@RequestBody ReqLabelBindingBatchBody requestBody) throws Exception {
        return productLabelService.bindingBatch(requestBody);
    }

    /**
     * 商品批量解绑标签
     *
     * @param requestBody
     * @return
     */
    @PostMapping("/unbindBatch")
    public R<Void> unbindBatch(@RequestBody ReqLabelBindingBatchBody requestBody) {
        return productLabelService.unbindBatch(requestBody);
    }

    /**
     * 翻页查询商品列表（标签绑定）
     *
     * @param bo
     * @param pageQuery
     * @return
     */
    @PostMapping("/getProductPageToLabel")
    public TableDataInfo<ProductToLabelBody> getProductPageToLabel(@RequestBody ReqProductBody bo, @RequestBody PageQuery pageQuery) {
        return productLabelService.getProductPageToLabel(bo, pageQuery);
    }


}
