package com.zsmall.product.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.exception.RStatusCodeException;
import com.hengjian.common.core.utils.ServletUtils;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.excel.utils.ExcelUtil;
import com.hengjian.common.log.annotation.InMethodLog;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.hengjian.openapi.domain.vo.SysInfVo;
import com.hengjian.openapi.service.ISysInfService;
import com.hengjian.stream.mq.constant.RabbitMqConstant;
import com.hengjian.system.service.ISysConfigService;
import com.rabbitmq.client.Channel;
import com.zsmall.common.constant.FileNameConstants;
import com.zsmall.common.enums.downloadRecord.DownloadTypePlusEnum;
import com.zsmall.common.enums.productMapping.SyncStateEnum;
import com.zsmall.common.exception.OrderPayException;
import com.zsmall.common.exception.ProductException;
import com.zsmall.common.exception.StockException;
import com.zsmall.order.biz.support.OrderSupport;
import com.zsmall.product.biz.service.ProductMappingService;
import com.zsmall.product.entity.domain.*;
import com.zsmall.product.entity.domain.bo.distributorProduct.BatchImportToStoreBo;
import com.zsmall.product.entity.domain.bo.distributorProduct.ImportToStoreBo;
import com.zsmall.product.entity.domain.bo.productMapping.*;
import com.zsmall.product.entity.domain.vo.productMapping.*;
import com.zsmall.product.entity.iservice.IProductMappingService;
import com.zsmall.system.entity.domain.TenantSalesChannel;
import com.zsmall.system.entity.iservice.ITenantSalesChannelService;
import com.zsmall.system.entity.util.DownloadRecordUtil;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * 商品映射相关接口
 *
 * <AUTHOR>
 * @date 2023/6/21
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/business/productMapping")
public class ProductMappingController {

    private final ProductMappingService productMappingService;
    private final IProductMappingService iProductMappingService;
    private final ITenantSalesChannelService iTenantSalesChannelService;
    private final OrderSupport orderSupport;
    private final ISysConfigService sysConfigService;
    private final ISysInfService sysInfService;
    @Autowired
    private RabbitTemplate rabbitTemplate;

    /**
     * 分页查询商品映射信息列表
     * @param bo
     * @param pageQuery
     * @return
     */
    @GetMapping("/queryProductMappingPage")
    public ProductMappingListVo queryProductMappingPage(ProductMappingTableQueryBo bo, PageQuery pageQuery) {
        bo.setTenantId(LoginHelper.getTenantId());
        return productMappingService.queryProductMappingPage(bo, pageQuery);
    }



    /**
     * 查询商品映射信息详情
     * @param bo
     * @return
     */
    @PostMapping(value = "/queryProductMappingDetail")
    public R<ProductMappingDetailVo> queryProductMappingDetail(@RequestBody ProductMappingDetailBo bo) {
        return productMappingService.queryProductMappingDetail(bo);
    }

    /**
     * 更新Sku映射商品编码
     */
    @PostMapping(value = "/saveSkuMapping")
    public R<Void> saveSkuMapping(@RequestBody SaveSkuMappingBo bo) throws RStatusCodeException {
        try {
            R<List<ProductMapping>> listR = productMappingService.updateProductMappingChannelSkuSite(bo);
            Set<String> collectSkuList = listR.getData().stream().map(ProductMapping::getChannelSku)
                                        .collect(Collectors.toSet());
          //异步调用订单
          ThreadUtil.execAsync(() -> {
                try {
                  orderSupport.orderCompletionProductMappingException(new ArrayList<>(collectSkuList));
                } catch (OrderPayException | StockException | ProductException e) {
                    log.error("映射模块异步调用订单模块，执行失败" + e);
                }
            });
            //异步调用发送MQ消息
            try {
                ThreadUtil.execAsync(() -> {
                    listR.getData().forEach(this::sendMqMessageByProductMapping);
                });
            } catch (Exception e) {
                log.error(e.getMessage());
            }
            return R.ok("更新成功！");
        }catch (Exception e){
            return  R.fail("更新失败"+ e.getMessage());
        }
    }

    /**
     * 单独设置分销商商品MarkUp
     */
    @PostMapping(value = "/setMarkUp")
    public R<Void> setMarkUp(@RequestBody SetMarkUpBo bo) {
        return productMappingService.setMarkUp(bo);
    }

    /**
     * 更新商品映射名称
     * @param payload id/name
     * @return R
     */
    @PostMapping(value = "/setProductMappingName")
    public R<Void> setProductMappingName(@RequestBody @Valid Map<String, String> payload) {
        String id = payload.get("id");
        String name = payload.get("name");
        if (Objects.isNull(id)){
            return  R.fail("商品映射ID不能为空");
        }
        if (name.length()>150){
            return  R.fail("商品名称过长，不能超过150个字符");
        }
        return productMappingService.setProductMappingName(id, name);
    }

    /**
     * 批量设置分销商商品MarkUp
     */
    @PostMapping(value = "/batchSetMarkUp")
    public R<Void> batchSetMarkUp(@RequestBody BatchSetMarkUpBo bo) {
        return productMappingService.batchSetMarkUp(bo);
    }

    /**
     * 上传SKU映射
     */
    @PostMapping(value = "/uploadSkuMapping", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public R<Void> uploadSkuMapping(@RequestPart("file") MultipartFile file) {
        try {
            R<List<ProductMapping>> listR = productMappingService.uploadSkuMapping(file);
            List<ProductMapping> data = listR.getData();
            Set<String> channelSku = data.stream().map(ProductMapping::getChannelSku).collect(Collectors.toSet());
            //异步调用订单模块
            try {
                log.info("异步调用订单模块");
                ThreadUtil.execAsync(() -> {
                    try {
                        orderSupport.orderCompletionProductMappingException(new ArrayList<>(channelSku));
                    } catch (OrderPayException | ProductException | StockException e) {
                        log.error("映射通知订单异步处理异常", e);
                    }
                });
            } catch (Exception e) {
                log.error("订单处理异常", e);
            }
            //异步调用发送MQ消息
            //20241220暂时不发送MQ
//            try {
//                ThreadUtil.execAsync(() -> {
//                    data.forEach(this::sendMqMessageByProductMapping);
//                });
//            } catch (Exception e) {
//                log.error("映射通知发送MQ异步处理异常", e);
//            }
            log.info("上传成功");
            return R.ok("上传成功！");
        }catch (Exception e){
            return R.fail("上传失败！"+e.getMessage());
        }

    }

    /**
     * 推送商品映射至销售渠道
     */
    @PostMapping(value = "/pushProductMapping")
    public R<Void> pushProductMapping(@RequestBody PushProductBo bo) {
        return productMappingService.pushProductMapping(bo);
    }

    /**
     * 删除商品映射信息
     * @return
     */
    @PostMapping(value = "/deleteProductMapping")
    public R<Void> deleteProductMapping(@RequestBody ProductMappingDeleteBo bo) throws OrderPayException, StockException, ProductException {
        LambdaQueryWrapper<ProductMapping> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(ProductMapping::getId, bo.getIds());
        List<ProductMapping> productMappingList = iProductMappingService.getBaseMapper().selectList(wrapper);
        if (productMappingList.size()!=bo.getIds().size()){
            return  R.fail("部分商品映射信息不存在"+bo.getIds());
        }
        productMappingService.deleteProductMapping(bo);
        try {
            //异步调用订单模块
            ThreadUtil.execAsync(()->{
                try {
                    orderSupport.orderCompletionProductMappingException(productMappingList.stream().map(ProductMapping::getChannelSku)
                            .collect(Collectors.toList()));
                } catch (OrderPayException | StockException | ProductException e) {
                    log.error("映射通知订单异步处理异常"+e);
                }
            });
        }catch (Exception e){
            log.error(e.getMessage());
        }
        return R.ok("删除成功");
    }

    /**
     * 商品批量铺货至渠道
     */
    @PostMapping(value = "/productBatchImportToChannel")
    public R<Void> productBatchImportToChannel(@RequestBody BatchImportToStoreBo bo) {
        return productMappingService.productBatchImportToChannel(bo);
    }

    /**
     * 商品铺货至渠道
     */
    @PostMapping(value = "/productImportToChannel")
    public R<Void> productImportToChannel(@RequestBody ImportToStoreBo bo) {
        return productMappingService.productImportToChannel(bo);
    }

    /**
     * 分页查询履约仓库映射
     */
    @GetMapping(value = "/queryWarehouseMappingPage")
    public TableDataInfo<WarehouseMappingVo> queryWarehouseMappingPage(WarehouseMappingBo bo, PageQuery pageQuery) {
        return productMappingService.queryWarehouseMappingPage(bo, pageQuery);
    }

    /**
     * 设置履约仓库映射
     */
    @PostMapping(value = "/setWarehouseMapping")
    public R<Void> setWarehouseMapping(@RequestBody FulfillWarehouseBo bo) {
        return productMappingService.setWarehouseMapping(bo);
    }

    /**
     * 查询铺货准备信息
     */
    @GetMapping(value = "/queryImportReadyInfo")
    public R<ImportReadyVo> queryImportReadyInfo(String productCode, String channelType) {
        return productMappingService.queryImportReadyInfo(productCode, channelType);
    }


    /**
     * 新增产品映射
     * @param addMappingMapper   channelId/itemNo/channelSku 渠道id，商品编码，渠道商品编码
     * @return
     */
    @PostMapping(value = "/addProductMapping")
    public R<Void> addProductMapping(@RequestBody Map<String, String> addMappingMapper) {
        String channelId = addMappingMapper.get("channelId");
        boolean match = ReUtil.isMatch("^\\d+$", channelId);
        if (!match){
           return R.fail("渠道ID格式错误,应该为纯数字: " + channelId);
        }
        String itemNo = addMappingMapper.get("itemNo");
        String channelSku =  addMappingMapper.get("channelSku");
        String orderNo =  addMappingMapper.get("orderId");
        try {
            R<ProductMapping> productMapping = productMappingService.addProductMappingSite(Long.valueOf(channelId), itemNo, channelSku,orderNo);
            if (ObjectUtil.isNotEmpty(productMapping.getData())){
                //异步通知订单模块
                ThreadUtil.execAsync(() -> {
                    try {
                        log.info(StrUtil.format("异步处理订单模块,渠道SKU:{}",productMapping.getData().getChannelSku()));
                        orderSupport.orderCompletionProductMappingException(Collections.singletonList(productMapping.getData().getChannelSku()));
                    } catch (OrderPayException | StockException | ProductException e) {
                        log.error("映射通知订单异步处理异常" + e);
                    }
                });
                //异步发送mq消息
//                try {
//                    ThreadUtil.execAsync(() -> {
//                        sendMqMessageByProductMapping(productMapping.getData());
//                    });
//                } catch (Exception e) {
//                    log.error("映射通知发送MQ异步处理异常"+e);
//                }
            }
            return R.ok("添加映射成功！");
        }catch (Exception e){
            log.error(e.getMessage());
            return  R.fail("添加映射失败：" +e.getMessage());
        }
    }
    @PostMapping(value = "/mq")
    public  void  test1(){
        String s="{\n" +
            "    \"accountFlag\": \"FurniBliss-US\",\n" +
            "    \"extCode\": \"FB-GZ-1659-BK5\",\n" +
            "    \"image\": \"https://img.cdnfe.com/product/fancy/ab2f1716-53cc-42ca-bf1d-8dd56a0ffb5a.jpg\",\n" +
            "    \"productName\": \"Arched Full Length Mirror with Stand, 59\\\"x16\\\" Floor Mirror with Aluminum Alloy Frame,   Bedroom, Dressing Mirror, Black/Gold\",\n" +
            "    \"sellerSku\": \"**********\",\n" +
            "    \"skuId\": \"**********\",\n" +
            "    \"type\": \"Temu\"\n" +
            "}";
        rabbitTemplate.convertAndSend(RabbitMqConstant.MULTICHANNEL_SEND_EXCHANGE,RabbitMqConstant.PRODUCT_MAPPING_RESPONSE, JSON.toJSONString(s));
    }

    /**
     * 发送MQ消息
     * @param productMapping
     */
    @InMethodLog("进入到[商品映射发送MQ模块]")
    public  void  sendMqMessageByProductMapping(ProductMapping productMapping){
        log.info("[商品映射发送MQ模块],发送消息"+JSONUtil.toJsonStr(productMapping));
        //查询店铺
        TenantSalesChannel tenantSalesChannel = iTenantSalesChannelService.getBaseMapper().selectById(productMapping.getChannelId());
        if(ObjectUtil.isNull(tenantSalesChannel.getConnectStr())){
            return;
        }
        //控制那些渠道的商品去处理映射信息
        String productMappingMqChannelType = sysConfigService.selectConfigByKey("ProductMapping_Mq_ChannelType");
        List<String>  productMappingChannelList= JSONUtil.toList(productMappingMqChannelType, String.class);
        if (!productMappingChannelList.contains(tenantSalesChannel.getChannelType())){
            return;
        }
        //去TK那边获取名称/价格/图片信息
        HashMap<String,String> map=new HashMap<>();
        //渠道SKU
        map.put("sellerSku",productMapping.getChannelSku());
        //渠道类型
        map.put("type",tenantSalesChannel.getChannelType());
        //店铺
        map.put("accountFlag",tenantSalesChannel.getChannelName());
        rabbitTemplate.convertAndSend(RabbitMqConstant.MULTICHANNEL_SEND_EXCHANGE,RabbitMqConstant.PRODUCT_MAPPING_REQUEST, JSON.toJSONString(map));
        log.info("[商品映射发送MQ模块],发送商品映射MQ消息成功" + JSON.toJSONString(map));
    }



    @RabbitHandler
    @RabbitListener(queues = RabbitMqConstant.PRODUCT_MAPPING_RESPONSE)
    public void receive(Message message,  @Header(AmqpHeaders.CHANNEL) Channel channel) throws IOException {
        // 处理消息
        try {
            //处理消息
            String messageContext = new String(message.getBody());
            if (StringUtils.isEmpty(messageContext)) {
                log.warn("[商品映射接收MQ模块],接收到消息队列: {}, 信息为空",RabbitMqConstant.TRACKING_NOTIFY_ERROR_QUEUE);
                return;
            }
            log.info("商品映射接收MQ模块,收到消息队列: {}", messageContext);
            JSONObject entries=new JSONObject(messageContext);
            String sellerSku = entries.getStr("sellerSku");
            String accountFlag = entries.getStr("accountFlag");
            String price = entries.getStr("price");
            String image = entries.getStr("image");
            String productName = entries.getStr("productName");
            String type = entries.getStr("type");
            String extCode = entries.getStr("extCode");
            //判断店铺是否存在
            LambdaQueryWrapper<TenantSalesChannel> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(TenantSalesChannel::getChannelName,accountFlag);
            wrapper.eq(TenantSalesChannel::getChannelType,type);
            wrapper.eq(TenantSalesChannel::getDelFlag,0);
            TenantSalesChannel tenantSalesChannel = iTenantSalesChannelService.getBaseMapper().selectOne(wrapper);
            if (ObjectUtil.isNull(tenantSalesChannel)){
                throw new RuntimeException(StrUtil.format("[商品映射接收MQ模块]消息处理异常,未找到店铺信息，店铺名称："+accountFlag+"渠道类型："+type));
            }
            //判断channelSKU是否存在
            LambdaQueryWrapper<ProductMapping> wrapper1 = new LambdaQueryWrapper<>();
            wrapper1.eq(ProductMapping::getChannelSku,sellerSku);
            wrapper1.eq(ProductMapping::getChannelId,tenantSalesChannel.getId());
            wrapper1.eq(ProductMapping::getSyncState, SyncStateEnum.Mapped);
            ProductMapping mapping = iProductMappingService.getBaseMapper().selectOne(wrapper1);
            if (ObjectUtil.isNull(mapping)){
                throw new RuntimeException("[商品映射接收MQ模块],未找到商品映射信息，channelSku："+sellerSku+"店铺ID："+tenantSalesChannel.getId());
            }
            //图片信息转换
            String hjOss = convertToHjOss(image);
            //更新商品映射信息
            UpdateWrapper<ProductMapping> updateWrapper=new UpdateWrapper<>();
            updateWrapper.eq("id",mapping.getId());
            updateWrapper.set("product_name",productName);
            updateWrapper.set("final_price",price);
            updateWrapper.set("image_show_url",hjOss);
            updateWrapper.set("channel_sku_item_number",extCode);
            updateWrapper.set("update_time",new Date());
            iProductMappingService.update(null,updateWrapper);
            log.info("[商品映射接收MQ模块],处理MQ消息成功："+message);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("[商品映射接收MQ模块],处理MQ消息失败，失败原因:{}",e.getMessage());
        } finally {
            channel.basicAck(message.getMessageProperties().getDeliveryTag(),true);
        }
    }

    /**
     * 将外部OSS文件转为恒健OSS
     * @param externalFileUrl 外部OSS
     * @return
     */
    public String convertToHjOss(String externalFileUrl){
        //转换shipmentPackages 里面的packageUrl 图片信息
        if (ObjectUtil.isNotEmpty(externalFileUrl)) {
            try {
                log.info(StrUtil.format("[商品映射接收MQ模块]转换OSS,外部地址：{}",externalFileUrl));
                //调用图片接口转换
                SysInfVo imageToHj = sysInfService.queryByInfNote("CONVERT_IMAGE_TO_HJ");
                if (ObjectUtil.isNull(imageToHj)){
                    throw  new RuntimeException("外部OSS转恒健OSS接口未配置，请检查！");
                }
                HashMap<String, String> map = new HashMap<>();
                map.put("url",externalFileUrl);
                String url=imageToHj.getIsTest()==1?imageToHj.getInfTestUrl():imageToHj.getInfUrl();
                String body = HttpRequest.post(url).body(JSON.toJSONString(map)).timeout(2000)
                                         .execute().body();
                com.alibaba.fastjson.JSONObject jsonObject = JSON.parseObject(body);
                String status = jsonObject.getString("status");
                if ("success".equals(status)){
                    return  jsonObject.getString("url");
                }else {
                    return externalFileUrl;
                }
            }catch (Exception e ){
                log.error(StrUtil.format("[商品映射接收MQ模块]转换OSS异常,错误原因：{}"+e));
                return externalFileUrl;
            }
        }
        return null;
    }
//    @InMethodLog("商品映射发送MQ监听")
//    @EventListener
//    public void listener(ProductMappingEvent productMappingEvent) {
//        sendMqMessageByProductMapping(productMappingEvent.getProductMapping());
//    }

    /**
     * 导出商品映射
     *
     * @param response
     * @param productMappingTableQueryBo
     * @throws IOException
     */
    @GetMapping("export")
    public R download(HttpServletResponse response,ProductMappingTableQueryBo productMappingTableQueryBo) throws IOException {
        productMappingTableQueryBo.setTenantId(LoginHelper.getTenantId());
        Locale headerLocale = ServletUtils.getHeaderLocale();
        String fileName = StrUtil.format(FileNameConstants.PRODUCT_MAPPING_EXPORT, DateUtil.format(new Date(), "yyMMdd-HHmmssSS"));
        DownloadRecordUtil.generate(fileName, DownloadTypePlusEnum.ProductMappingExport, tempFileSavePath -> {
            PageQuery pageQuery = new PageQuery();
            pageQuery.setPageNum(0);
            pageQuery.setPageSize(Integer.MAX_VALUE);

            ProductMappingListVo productMappingListVo = productMappingService.queryProductMappingPage(productMappingTableQueryBo, pageQuery);
            List<ProductMappingExportVo> list = new ArrayList<>();
            productMappingListVo.getRows().forEach(s->{
                ProductMappingExportVo exportVo = BeanUtil.copyProperties(s, ProductMappingExportVo.class);
                exportVo.setSiteCurrency(s.getSite()+"/"+s.getCurrency());
                list.add(exportVo);
            });

            File tempFile = new File(tempFileSavePath);
            BufferedOutputStream outputStream = FileUtil.getOutputStream(tempFile);
            ExcelUtil.exportExcelWithLocale(list, "ProductMapping", ProductMappingExportVo.class, false, outputStream, headerLocale);
            IoUtil.close(outputStream);
            return tempFile;
        });
        return R.ok();
    }

    /**
     * 清洗映射数据
     * @return
     */
    @PostMapping(value = "/dealProductMappingDate")
    public void dealProductMappingDate() {
        productMappingService.dealProductMappingDate();
    }
}
