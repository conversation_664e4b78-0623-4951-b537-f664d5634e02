package com.zsmall.product.controller;

import com.hengjian.common.core.domain.R;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.zsmall.product.biz.service.TenantFavoritesService;
import com.zsmall.product.entity.domain.bo.tenantFavorites.TenantFavoritesDeleteBo;
import com.zsmall.product.entity.domain.bo.tenantFavorites.TenantFavoritesExportBo;
import com.zsmall.product.entity.domain.bo.tenantFavorites.TenantFavoritesListBo;
import com.zsmall.product.entity.domain.vo.tenantFavorites.TenantFavoritesListVo;
import javax.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 租户收藏夹相关接口
 *
 * <AUTHOR>
 * @date 2023/8/23
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/business/favorites")
public class TenantFavoritesController {

    private final TenantFavoritesService tenantFavoritesService;

    /**
     * 分页查询租户收藏夹
     */
    @GetMapping
    public TableDataInfo<TenantFavoritesListVo> queryPage(TenantFavoritesListBo bo, PageQuery pageQuery) {
        bo.setTenantId(LoginHelper.getTenantId());
        return tenantFavoritesService.queryPage(bo, pageQuery);
      //  return TableDataInfo.build(tenantFavoritesListVos);
    }

    /**
     * 查询收藏夹数量
     * @return
     */
    @GetMapping("/getTenantFavoritesCount")
    public R getTenantFavoritesCount() {
        return R.ok(tenantFavoritesService.getTenantFavoritesCount());
    }

    /**
     * 删除收藏夹项目
     */
    @DeleteMapping
    public R<Void> deleteFavorites(@RequestBody TenantFavoritesDeleteBo bo) {
        return tenantFavoritesService.deleteFavorites(bo);
    }

//    /**
//     * 导出收藏夹商品数据
//     */
//    @GetMapping("/export")
//    public R<Void> exportFavorites(TenantFavoritesExportBo bo) {
//        return tenantFavoritesService.exportFavorites(bo);
//    }

    /**
     * 功能描述：收藏夹商品数据 v2
     *
     * @param bo        bo
     * @param response  回答
     * <AUTHOR>
     * @date 2024/04/26
     */
    @GetMapping("/exportV2")
    public R<Void> exportV2(TenantFavoritesListBo bo, HttpServletResponse response) {
        return tenantFavoritesService.exportV2(bo, response);
    }

    /**
     * 导出收藏夹商品资料压缩包
     */
    @GetMapping("/exportZip")
    public R<Void> exportFavoritesZip(TenantFavoritesExportBo bo) {
        return tenantFavoritesService.exportFavoritesZip(bo);
    }


}
