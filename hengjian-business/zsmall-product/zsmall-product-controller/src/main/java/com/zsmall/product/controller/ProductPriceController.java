package com.zsmall.product.controller;

import cn.hutool.json.JSONObject;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.exception.RStatusCodeException;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.zsmall.product.biz.service.ProductSkuPriceService;
import com.zsmall.product.entity.domain.bo.product.ProductPriceBo;
import com.zsmall.product.entity.domain.bo.product.ProductSitePriceBo;
import com.zsmall.product.entity.domain.bo.siteBo.SiteBo;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/** 商品价格相关
 * <AUTHOR>
 * @date 2023/7/12 19:07
 */

@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/business/productPrice")
public class ProductPriceController {

    private final ProductSkuPriceService productSkuPriceService;


    /**
     * 上传商品价格更新Excel
     */
    @PostMapping("/uploadProductPriceUpdate")
    public R<JSONObject> uploadProductPriceUpdate(@RequestPart("file") MultipartFile file) {
        return productSkuPriceService.uploadProductPriceUpdate(file);
    }

    /**
     * 功能描述：商品修改页面导出
     *
     * @param bo        bo
     * @param pageQuery 页面查询
     * @param response  响应
     * @return {@link R }<{@link Void }>
     * <AUTHOR>
     * @date 2025/01/26
     */
    @GetMapping(value = "/export")
    public R<Void> export(ProductPriceBo bo, PageQuery pageQuery, HttpServletResponse response) throws RStatusCodeException {
        productSkuPriceService.exportAsync(bo, pageQuery, response);
//        productSkuPriceService.exportNotAsync(bo, pageQuery, response);
        return R.ok();
    }

    /**
     * 功能描述：新增时获取站点列表
     *
     * @return {@link R }<{@link ProductSitePriceBo }>
     * <AUTHOR>
     * @date 2024/12/20
     */
    @PostMapping("/getSitePrice")
    public R<List<SiteBo>> getSitePrice() {
        return R.ok(productSkuPriceService.getSitePrice());
    }

//    @PostMapping("/initializePricesAndSites")
//    public R initializePricesAndSites(@RequestBody List<SitePriceCleanBo> sitePriceCleanBos) {
//        return R.ok(productSkuPriceService.initializePricesAndSites(sitePriceCleanBos));
//    }













}
