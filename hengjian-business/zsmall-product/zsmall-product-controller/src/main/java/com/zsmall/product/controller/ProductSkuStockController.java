package com.zsmall.product.controller;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.log.annotation.Log;
import com.hengjian.common.log.enums.BusinessType;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.zsmall.extend.shop.rakuten.constant.OrderConstant;
import com.zsmall.extend.shop.rakuten.kit.RakutenDelegate;
import com.zsmall.extend.shop.rakuten.kit.RakutenKit;
import com.zsmall.extend.shop.rakuten.model.order.InSearchOrder;
import com.zsmall.extend.shop.rakuten.model.order.OutSearchOrder;
import com.zsmall.product.biz.service.ProductSkuStockService;
import com.zsmall.product.entity.domain.bo.productSkuStock.StockEditBo;
import com.zsmall.product.entity.domain.bo.productSkuStock.StockListBo;
import com.zsmall.product.entity.domain.bo.productSkuStock.StockStateSwitchBo;
import com.zsmall.product.entity.domain.vo.productSkuStock.SkuStockInfoVo;
import com.zsmall.product.entity.domain.vo.productSkuStock.StockValidWarehouseSelectVo;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * 商品SKU库存-控制层
 *
 * <AUTHOR>
 * @date 2023/6/15
 */
@Validated
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/business/productSkuStock")
public class ProductSkuStockController {

    private final ProductSkuStockService productSkuStockService;




    @PostMapping(value = "/testStock")
    public R<Void> testStock(@RequestBody JSONObject params) {

        String serviceSecret = "SP414318_uKlmDwQFlUm0VyQi";
        String licenseKey = "SL414318_KhvlUrZye303EfaX";
        RakutenDelegate rakutenDelegate = RakutenKit.create(serviceSecret, licenseKey);

        InSearchOrder inSearchOrder = new InSearchOrder();
        inSearchOrder.setDateType(OrderConstant.DateType.OrderDate);
        inSearchOrder.setDateBetweenByOffset(3);
        inSearchOrder.pageDesc(1, 20);

        OutSearchOrder outSearchOrder = rakutenDelegate.orderApi().searchOrder(inSearchOrder);
        System.out.println(JSONUtil.toJsonStr(outSearchOrder));

        return R.ok(JSONUtil.toJsonStr(outSearchOrder));
    }

    /**
     * 查询Sku库存列表
     */
    @GetMapping(value = "/list")
    public TableDataInfo<SkuStockInfoVo> queryProductSkuStockList(StockListBo bo, PageQuery pageQuery) {
        bo.setTenantId(LoginHelper.getTenantId());
        return productSkuStockService.queryProductSkuStockList(bo, pageQuery);
    }

    /**
     * 编辑库存数量
     */
    @PostMapping(value = "/editStockQuantity")
    public R<Void> editStockQuantity(@RequestBody @Validated StockEditBo bo) {
        return productSkuStockService.editStockQuantity(bo);
    }

    /**
     * 切换库存状态
     */
    @PostMapping(value = "/switchStockState")
    public R<Void> switchStockState(@RequestBody @Validated StockStateSwitchBo bo) {
        return productSkuStockService.switchStockState(bo);
    }

    /**
     * 查询库存可用仓库
     */
    @GetMapping(value = "/queryValidWarehouseList")
    public R<List<StockValidWarehouseSelectVo>> queryValidWarehouseList(String stockCode) {
        return productSkuStockService.queryValidWarehouseList(stockCode);
    }

    /**
     * 上传库存Excel
     */
    @PostMapping(value = "/uploadStockExcel", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public R<Void> uploadStockExcel(@RequestPart("file") MultipartFile file) throws Exception {
        return productSkuStockService.uploadStockExcel(file);
    }

    /**
     * 管理库存数据导出
     */
    @Log(title = "管理库存数据导出", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public R<Void> export(StockListBo bo, HttpServletResponse response) {
//        PageQuery pageQuery = new PageQuery();
//        pageQuery.setPageNum(1);
//        pageQuery.setPageSize(500000);
//        TableDataInfo<SkuStockInfoVo> skuStockInfoVoTableDataInfo = productSkuStockService.queryProductSkuStockList(bo, pageQuery);
//        List<SkuStockInfoVo> rows = skuStockInfoVoTableDataInfo.getRows();
//        List<SkuStockInfoExportDTO> skuStockInfoExportDTO= BeanUtil.copyToList(rows, SkuStockInfoExportDTO.class);
//        ExcelUtil.exportExcel(skuStockInfoExportDTO, "库存数据导出", SkuStockInfoExportDTO.class, response, false);
        bo.setTenantId(LoginHelper.getTenantId());
        productSkuStockService.export(bo);
        return R.ok();
    }

}
