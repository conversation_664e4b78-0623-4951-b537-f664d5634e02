package com.zsmall.product.controller;

import com.hengjian.common.core.domain.R;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.zsmall.bma.open.member.service.MemberLevelV2Service;
import com.zsmall.product.entity.domain.bo.member.MemberLevelQueryBo;
import com.zsmall.product.entity.domain.vo.member.MemberLevelTableInfoVo;
import com.zsmall.product.entity.domain.vo.member.MemberLevelVO;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * 会员等级
 *
 * <AUTHOR> Theo
 * @create 2024/4/29 15:50
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/business/member/roleLevel")
public class MemberRoleLevelController {

    private final MemberLevelV2Service memberLevelService;

    /**
     * 功能描述：会员等级列表
     *
     * @param bo        bo
     * @param pageQuery 页面查询
     * @return {@link MemberLevelTableInfoVo }
     * <AUTHOR>
     * @date 2024/05/08
     */
    @GetMapping("/list")
    public MemberLevelTableInfoVo list(MemberLevelQueryBo bo, PageQuery pageQuery) {
        return memberLevelService.queryPageList(bo, pageQuery);
    }

    /**
     * 功能描述：会员等级添加
     *
     * @param vo vo
     * @return {@link R }<{@link Void }>
     * <AUTHOR>
     * @date 2024/05/08
     */
    @PostMapping()
    public R<Void> add(@RequestBody MemberLevelVO vo) {
        return memberLevelService.add(vo);
    }

    /**
     * 功能描述：会员等级编辑
     *
     * @param vo vo
     * @return {@link R }<{@link Void }>
     * <AUTHOR>
     * @date 2024/05/08
     */
    @PutMapping()
    public R<Void> edit(@RequestBody MemberLevelVO vo){
        return memberLevelService.edit(vo);
    }

    /**
     * 功能描述：会员等级编辑前校验
     *
     * @param vo vo
     * @return {@link R }<{@link Void }>
     * <AUTHOR>
     * @date 2024/05/08
     */
    @PostMapping("/editBefore")
    public R<Boolean> editBefore(@RequestBody MemberLevelVO vo){
        return memberLevelService.editBefore(vo);
    }

    /**
     * 供应商创建/编辑商品时判断时候有会员等级生效
     *
     * @return
     */
    @GetMapping("/saveProductCheck")
    public R<Boolean> newProductCheck(){
        return memberLevelService.newProductCheck();
    }

}
