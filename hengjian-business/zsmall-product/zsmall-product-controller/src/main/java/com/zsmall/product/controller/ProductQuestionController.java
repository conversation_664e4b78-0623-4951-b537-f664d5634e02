package com.zsmall.product.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaMode;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.core.validate.AddGroup;
import com.hengjian.common.core.validate.EditGroup;
import com.hengjian.common.excel.utils.ExcelUtil;
import com.hengjian.common.idempotent.annotation.RepeatSubmit;
import com.hengjian.common.log.annotation.Log;
import com.hengjian.common.log.enums.BusinessType;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.hengjian.common.web.core.BaseController;
import com.zsmall.product.entity.domain.bo.prodcutQuestion.*;
import com.zsmall.product.entity.domain.vo.prodcutQuestion.ProductAnswerLogVo;
import com.zsmall.product.entity.domain.vo.prodcutQuestion.ProductQuestionVo;
import com.zsmall.product.entity.iservice.IProductQuestionService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 商品问答
 *
 * <AUTHOR> Li
 * @date 2023-07-26
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/product/question")
public class ProductQuestionController extends BaseController {

    private final IProductQuestionService iProductQuestionService;

    /**
     * 查询商品问答列表
     */
    @SaCheckPermission("product:question:list")
    @GetMapping("/list")
    public TableDataInfo<ProductQuestionVo> list(ProductQuestionBo bo, PageQuery pageQuery) {
        return iProductQuestionService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出商品问答列表
     */
    @SaCheckPermission("product:question:export")
    @Log(title = "商品问答", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(ProductQuestionBo bo, HttpServletResponse response) {
        List<ProductQuestionVo> list = iProductQuestionService.queryList(bo);
        ExcelUtil.exportExcel(list, "商品问答", ProductQuestionVo.class, response, false);
    }

    /**
     * 获取商品问答详细信息
     *
     * @param questionCode 唯一编码
     */
    @SaCheckPermission("product:question:query")
    @GetMapping("/{questionCode}")
    public R<ProductQuestionVo> getInfo(@NotNull(message = "{zsmall.productQA.questionCodeIsBlank}")
                                     @PathVariable String questionCode) {
        return R.ok(iProductQuestionService.queryByCode(questionCode));
    }

    /**
     * 发布商品疑问
     */
    @SaCheckPermission("product:question:add")
    @Log(title = "发布商品疑问", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody ProductQuestionBo bo) {
        // 判断当前用户是否是分销商
        LoginHelper.getLoginUser(TenantType.Distributor);
        return toAjax(iProductQuestionService.insertByBo(bo));
    }

    /**
     * 追加发布商品疑问
     */
    @SaCheckPermission("product:question:add")
    @Log(title = "追加发布商品疑问", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/again")
    public R<Void> addAgain(@Validated(AddGroup.class) @RequestBody ProductQuestionAgainBo bo) {
        // 判断当前用户是否是分销商
        LoginHelper.getLoginUser(TenantType.Distributor);
        return toAjax(iProductQuestionService.addAgain(bo));
    }

    /**
     * 新增回复
     * @param bo
     * @return
     */
    @SaCheckPermission(value = { "product:question:answer:add", "product:question:answer:platformAdd" }, mode = SaMode.OR)
    @Log(title = "新增回复", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/answer")
    public R<Void> addAnswer(@Validated(AddGroup.class) @RequestBody ProductAnswerReplyBo bo) {
        return toAjax(iProductQuestionService.addAnswer(bo));
    }

    /**
     * 编辑回复
     * @param bo
     * @return
     */
    @SaCheckPermission(value = { "product:question:answer:add", "product:question:answer:platformAdd" }, mode = SaMode.OR)
    @Log(title = "编辑回复", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping("/answer")
    public R<Void> editAnswer(@Validated(EditGroup.class) @RequestBody ProductAnswerEditBo bo) {
        return toAjax(iProductQuestionService.editAnswer(bo));
    }

    /**
     * 删除回复或追问
     * @param bo
     * @return
     */
    @SaCheckPermission("product:question:answer:delete")
    @Log(title = "删除回复或追问", businessType = BusinessType.DELETE)
    @RepeatSubmit()
    @DeleteMapping(value = "/answer")
    public R<Void> deleteAnswers(@Validated @RequestBody ProductAnswerDeleteBo bo) {
        return toAjax(iProductQuestionService.deleteAnswer(bo));
    }

    /**
     * 删除商品问答
     *
     * @param questionCodes 问题编码集合
     */
    @SaCheckPermission("product:question:remove")
    @Log(title = "商品问答", businessType = BusinessType.DELETE)
    @DeleteMapping("/{questionCodes}")
    public R<Void> remove(@NotEmpty(message = "{zsmall.productQA.questionCodeIsBlank}")
                          @PathVariable String[] questionCodes) {
        // 判断当前用户是否是管理员
        LoginHelper.getLoginUser(TenantType.Manager);
        return toAjax(iProductQuestionService.deleteWithValidByCodes(List.of(questionCodes)));
    }

    /**
     * 查询日志
     * @param answerCode
     * @return
     */
    @SaCheckPermission("product:question:answer:log")
    @GetMapping(value = "/log/{answerCode}")
    public R<List<ProductAnswerLogVo>> queryLog(@NotEmpty(message = "{zsmall.productQA.answerCodeIsBlank}")
                                                       @PathVariable String answerCode) {
        return R.ok(iProductQuestionService.queryLog(answerCode));
    }

    /**
     * 举报提问
     * @param bo
     * @return
     */
    @SaCheckPermission("product:question:report")
    @Log(title = "商品问答举报提问", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping(value = "/reportQuestion")
    public R<Void> reportQuestion(@RequestBody ProductQuestionsReportBo bo) {
        // 判断当前用户是否是供应商
        LoginHelper.getLoginUser(TenantType.Supplier);
        return toAjax(iProductQuestionService.reportQuestion(bo));
    }
}
