package com.zsmall.product.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaIgnore;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.validate.AddGroup;
import com.hengjian.common.core.validate.EditGroup;
import com.hengjian.common.excel.utils.ExcelUtil;
import com.hengjian.common.idempotent.annotation.RepeatSubmit;
import com.hengjian.common.log.annotation.Log;
import com.hengjian.common.log.enums.BusinessType;
import com.hengjian.common.web.core.BaseController;
import com.zsmall.product.biz.service.ProductCategoryService;
import com.zsmall.product.entity.domain.bo.category.ProductCategoryBo;
import com.zsmall.product.entity.domain.bo.category.ProductCategoryParamBo;
import com.zsmall.product.entity.domain.vo.category.ProductCategorySearchVo;
import com.zsmall.product.entity.domain.vo.category.ProductCategorySelectVo;
import com.zsmall.product.entity.domain.vo.category.ProductCategoryVo;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 商品分类
 *
 * <AUTHOR>
 * @date 2023-05-19
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/business/productCategory")
public class ProductCategoryController extends BaseController {

    private final ProductCategoryService productCategoryService;

    /**
     * 查询商品分类列表
     */
    @SaCheckPermission("business:productCategory:list")
    @GetMapping("/list")
    public R<List<ProductCategoryVo>> list(ProductCategoryParamBo bo) {
        List<ProductCategoryVo> list = productCategoryService.queryList(bo);
        return R.ok(list);
    }

    /**
     * 查询商品分类列表（提供给下拉选使用）
     */
    // @SaCheckPermission("business:productCategory:list")
    @SaIgnore
    @GetMapping("/listForSelect")
    public R<List<ProductCategorySelectVo>> listForSelect(Long parentId) {
        return R.ok(productCategoryService.queryListForSelect(parentId));
    }

    /**
     * 导出商品分类列表
     */
//    @SaCheckPermission("business:productCategory:export")
    @Log(title = "商品分类", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(ProductCategoryParamBo bo, HttpServletResponse response) {
        List<ProductCategoryVo> list = productCategoryService.queryList(bo);
        ExcelUtil.exportExcel(list, "商品分类", ProductCategoryVo.class, response, false);
    }

    /**
     * 获取商品分类详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("business:productCategory:query")
    @GetMapping("/{id}")
    public R<ProductCategoryVo> getInfo(@NotNull(message = "主键不能为空")
                                        @PathVariable Long id) {
        return R.ok(productCategoryService.queryById(id));
    }

    /**
     * 新增商品分类
     */
    @SaCheckPermission("business:productCategory:add")
    @Log(title = "商品分类", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody ProductCategoryBo bo) {
        return toAjax(productCategoryService.insertByBo(bo));
    }

    /**
     * 修改商品分类
     */
    @SaCheckPermission("business:productCategory:edit")
    @Log(title = "商品分类", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody ProductCategoryBo bo) {
        return toAjax(productCategoryService.updateByBo(bo));
    }

    /**
     * 删除商品分类
     *
     * @param ids 主键串
     */
    @SaCheckPermission("business:productCategory:remove")
    @Log(title = "商品分类", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return productCategoryService.deleteWithValidByIds(List.of(ids), true);
    }

    /**
     * 根据类目名称模糊搜索全部类目
     *
     * @param categoryName
     * @return
     */
    @GetMapping("listForSearch")
    public R<List<ProductCategorySearchVo>> listForSearch(String categoryName) {
//        productCategoryService.queryListForSelect(parentId)
//        return R.ok(productCategoryService.listForSearch(categoryName));
//        return R.ok(productCategoryService.listForSearchForMontage(categoryName));
        return R.ok(productCategoryService.listForSearchForAllMontage(categoryName));
    }
}
