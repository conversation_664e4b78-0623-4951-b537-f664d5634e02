package com.zsmall.system.entity.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import com.hengjian.common.log.annotation.InMethodLog;

import java.lang.reflect.Field;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 供应商入驻反射对比工具
 *
 * <AUTHOR>
 * @date 2023/8/30 12:03
 */
public class SettleInReflectUtil {

    @InMethodLog(value = "供应商入驻信息对比")
    public static <T> T compareTenantSupSettleInObj(T oldObj, T newObj, Class<T> beanClass) throws IllegalAccessException {
        List<Field> fields = CollUtil.toList(ReflectUtil.getFields(beanClass));
        /**
         * "countryId", "stateId", "stateText", "cityText" 字段值对比规则：
         *  1、全部相等则全部清空字段值
         *  2、任意一个不相等则全部保留
         */
        List<String> specifyFields1 = CollUtil.newArrayList("countryId", "stateId", "stateText", "cityText", "address", "warehouseAddress", "registeredAddress");
        List<String> specifyFields2 = CollUtil.newArrayList("areaCode", "phoneNumber");
        List<String> specifyFields3 = CollUtil.newArrayList("msgAppType", "msgAppAccount");
        List<List<String>> specifyFieldsList = CollUtil.newArrayList(specifyFields1, specifyFields2, specifyFields3);
        for (List<String> specifyFields : specifyFieldsList) {
            boolean allEquals = true;
            for (String specifyField : specifyFields) {
                if (CollUtil.contains(fields, specifyField)) {
                    continue;
                }
                Object oldVal = ReflectUtil.getFieldValue(oldObj, specifyField);
                Object newVal = ReflectUtil.getFieldValue(newObj, specifyField);

                if (ObjUtil.notEqual(oldVal, newVal)) {
                    allEquals = false;
                    break;
                }
            }
            if (allEquals) {
//                specifyFields.forEach(field -> ReflectUtil.setFieldValue(oldObj, field, null));
                specifyFields.forEach(field -> {
//                    List<Field> fields1 = Arrays.stream(fields).toList();
                    List<String> collect = fields.stream().map(field1 -> field1.getName()).collect(Collectors.toList());
                    CollUtil.toList(String.class);
                    if (CollUtil.contains(collect, field)) {
                        ReflectUtil.setFieldValue(oldObj, field, null);
                    }
                });
            }
        }
        for (Field field : fields) {
            String fieldName = field.getName();
            //排除比较字段
            if (specifyFields1.contains(fieldName)
                || specifyFields2.contains(fieldName)
                || specifyFields3.contains(fieldName)
                || StrUtil.equalsAny(fieldName, "contactType", "id", "serialVersionUID", "createBy", "createTime", "updateBy", "updateTime")) {
                continue;
            }
            field.setAccessible(true);
            //获取修改前的当前字段值
            Object oldValue = field.get(oldObj);
            //获取修改后的当前字段值
            Object newValue = field.get(newObj);
            //比较修改前与修改后的结果，true：清空当前字段数据， false：保留当前字段数据
            if (ObjUtil.equals(oldValue, newValue)) {
                field.set(oldObj, null);
            }
        }
        return oldObj;
    }
}
