package com.zsmall.system.entity.util;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.hengjian.common.core.domain.model.LoginUser;
import com.hengjian.common.core.exception.RStatusCodeException;
import com.hengjian.common.core.utils.SpringUtils;
import com.hengjian.common.satoken.holder.LoginContextLocalHolder;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.hengjian.extend.event.OSSUploadEvent;
import com.hengjian.system.domain.vo.SysOssVo;
import com.zsmall.common.enums.downloadRecord.DownloadTypePlusEnum;
import com.zsmall.common.enums.downloadRecord.RecordStateEnum;
import com.zsmall.common.enums.statuscode.ZSMallStatusCodeEnum;
import com.zsmall.common.properties.FileProperties;
import com.zsmall.system.entity.domain.DownloadRecord;
import com.zsmall.system.entity.iservice.IDownloadRecordService;
import lombok.Cleanup;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.io.File;
import java.io.InputStream;
import java.util.function.Function;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/12/3 11:13
 */
@Slf4j
@RequiredArgsConstructor
public class DownloadRecordV2Util {
    private static FileProperties fileProperties;
    private static IDownloadRecordService iDownloadRecordService;
    private static ThreadPoolTaskExecutor threadPoolTaskExecutor;

    public DownloadRecordV2Util(FileProperties fileProperties, IDownloadRecordService iDownloadRecordService, ThreadPoolTaskExecutor threadPoolTaskExecutor) {
        DownloadRecordV2Util.fileProperties = fileProperties;
        DownloadRecordV2Util.iDownloadRecordService = iDownloadRecordService;
        DownloadRecordV2Util.threadPoolTaskExecutor = threadPoolTaskExecutor;
    }

    /**
     * 功能描述：导出
     *
     * @param fileName     文件名
     * @param downloadType 下载类型
     * @param function     功能
     * <AUTHOR>
     * @date 2025/03/28
     */
    public static void generate(String fileName, DownloadTypePlusEnum downloadType, Function<String, File> function) {
        Boolean existsed = iDownloadRecordService.existsByRecordState(RecordStateEnum.Generating);
        if (existsed) {
            throw new RStatusCodeException(ZSMallStatusCodeEnum.DOWNLOAD_RECORD_GENERATING);
        } else {
            // 创建新的下载记录
            LoginUser loginUser = null;
            String tenantId = null;
            Long userId = null;
            try{
                tenantId = LoginHelper.getTenantId();
                userId = LoginHelper.getUserId();
                if(ObjectUtil.isNull(tenantId)||ObjectUtil.isNull(userId)){
                    loginUser = LoginContextLocalHolder.getLoginInfo();
                    tenantId = loginUser.getTenantId();
                    userId = loginUser.getUserId();
                }
            }catch (Exception e){
                log.error("【下载记录工具类】获取登录用户信息失败 {},使用线程变量", e.getMessage(), e);
                loginUser = LoginContextLocalHolder.getLoginInfo();
                if(ObjectUtil.isNotEmpty(loginUser)){
                    tenantId = loginUser.getTenantId();
                    userId = loginUser.getUserId();
                }
            }
            DownloadRecord newRecord = new DownloadRecord();
            newRecord.setRecordState(RecordStateEnum.Generating);
            newRecord.setTenantId(tenantId);
            newRecord.setCreateBy(userId);
            newRecord.setFileName(fileName);
            newRecord.setDownloadType(downloadType);
            iDownloadRecordService.save(newRecord);

            try {
                String tempSavePath = fileProperties.getTempSavePath();
                String tempFileSavePath = StrUtil.builder(tempSavePath, File.separator, downloadType.name(), File.separator, UUID.fastUUID().toString(true), ".", FileUtil.getSuffix(fileName)).toString();

                log.info("【下载记录工具类】临时文件保存路径 => {}", tempFileSavePath);
                File file = function.apply(tempFileSavePath);
                @Cleanup InputStream inputStream = null;
                if(ObjectUtil.isNotEmpty(file)){
                     inputStream = FileUtil.getInputStream(file);
                }
                OSSUploadEvent uploadEvent = new OSSUploadEvent(inputStream, fileName);
                SpringUtils.context().publishEvent(uploadEvent);
                SysOssVo sysOssVo = uploadEvent.getSysOssVo();

                newRecord.setOssId(sysOssVo.getOssId());
                newRecord.setFileSaveKey(sysOssVo.getFileName());
                newRecord.setFileUrl(sysOssVo.getUrl());
                newRecord.setFileSize(StrUtil.toString(file.length()));
                newRecord.setRecordState(RecordStateEnum.Ready);
                iDownloadRecordService.updateById(newRecord);

                FileUtil.del(file);
            } catch (Exception e) {
                log.error("【导出通用工具】出现未知错误 {}", e.getMessage(), e);
                newRecord.setRecordState(RecordStateEnum.Failed);
                iDownloadRecordService.updateById(newRecord);
            }

        }
    }


    /**
     * 功能描述：导入生成
     *
     * @param fileName     文件名
     * @param downloadType 下载类型
     * @param function     功能
     * <AUTHOR>
     * @date 2025/03/28
     */
    public static void leadIntoGenerate(String fileName, DownloadTypePlusEnum downloadType, Function<String, File> function) {
        Boolean existsed = iDownloadRecordService.existsByRecordState(RecordStateEnum.Generating);
        if (existsed) {
            throw new RStatusCodeException(ZSMallStatusCodeEnum.DOWNLOAD_RECORD_GENERATING);
        } else {
            // 创建新的下载记录
            LoginUser loginUser = null;
            String tenantId = null;
            Long userId = null;
            try{
                tenantId = LoginHelper.getTenantId();
                userId = LoginHelper.getUserId();
                if(ObjectUtil.isNull(tenantId)||ObjectUtil.isNull(userId)){
                    loginUser = LoginContextLocalHolder.getLoginInfo();
                    tenantId = loginUser.getTenantId();
                    userId = loginUser.getUserId();
                }
            }catch (Exception e){
                log.error("【下载记录工具类】获取登录用户信息失败 {},使用线程变量", e.getMessage(), e);
                loginUser = LoginContextLocalHolder.getLoginInfo();
                if(ObjectUtil.isNotEmpty(loginUser)){
                    tenantId = loginUser.getTenantId();
                    userId = loginUser.getUserId();
                }
            }
            DownloadRecord newRecord = new DownloadRecord();
            newRecord.setRecordState(RecordStateEnum.Generating);
            newRecord.setTenantId(tenantId);
            newRecord.setCreateBy(userId);
            newRecord.setFileName(fileName);
            newRecord.setDownloadType(downloadType);
            iDownloadRecordService.save(newRecord);

            try {
                String tempSavePath = fileProperties.getTempSavePath();
                String tempFileSavePath = StrUtil.builder(tempSavePath, File.separator, downloadType.name(), File.separator, UUID.fastUUID().toString(true), ".", FileUtil.getSuffix(fileName)).toString();

                log.info("【下载记录工具类】临时文件保存路径 => {}", tempFileSavePath);
                File file = function.apply(tempFileSavePath);
                @Cleanup InputStream inputStream = null;
                if(ObjectUtil.isNotEmpty(file)){
                    inputStream = FileUtil.getInputStream(file);
                }
                OSSUploadEvent uploadEvent = new OSSUploadEvent(inputStream, fileName);
                SpringUtils.context().publishEvent(uploadEvent);
                SysOssVo sysOssVo = uploadEvent.getSysOssVo();

                newRecord.setOssId(sysOssVo.getOssId());
                newRecord.setFileSaveKey(sysOssVo.getFileName());
                newRecord.setFileUrl(sysOssVo.getUrl());
                newRecord.setFileSize(StrUtil.toString(file.length()));
                newRecord.setRecordState(RecordStateEnum.Ready);
                iDownloadRecordService.updateById(newRecord);

                FileUtil.del(file);
            } catch (Exception e) {
                log.error("【导出通用工具】出现未知错误 {}", e.getMessage(), e);
                newRecord.setRecordState(RecordStateEnum.Failed);
                iDownloadRecordService.updateById(newRecord);
            }

        }
    }


}
