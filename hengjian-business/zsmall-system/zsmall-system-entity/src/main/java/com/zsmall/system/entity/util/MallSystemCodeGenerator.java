package com.zsmall.system.entity.util;

import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.hengjian.common.core.constant.AbstractCodeTypeBase;
import com.hengjian.common.core.exception.RStatusCodeException;
import com.zsmall.common.enums.BusinessCodeEnum;
import com.zsmall.common.enums.statuscode.ZSMallStatusCodeEnum;
import com.zsmall.common.service.RedisCodeGenerator;
import com.zsmall.system.entity.iservice.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalTime;

/**
 * 商城系统编码生成器
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class MallSystemCodeGenerator extends RedisCodeGenerator {

    private final ITenantWalletService iTenantWalletService;
    private final ITransactionRecordService iTransactionRecordService;
    private final ITransactionReceiptService iTransactionReceiptService;
    private final ITenantReceiptAccountService iTenantReceiptAccountService;
    private final IMarketplaceActivityConfigService iMarketplaceActivityConfigService;

    /**
     * 编号生成器
     *
     * @param type 主类型
     * @return
     */
    @Override
    public String codeGenerate(AbstractCodeTypeBase type) throws RStatusCodeException {
        log.info("编号生成器 type = {}, subType = {}", type);
        var code = "";
        var value = type.getValue();
        var repeat = true;
        while (repeat) {
            if (BusinessCodeEnum.Wallet.equals(type)) {
                code = UUID.randomUUID().toString(true);
                repeat = iTenantWalletService.existWalletNo(code);
            } else if(BusinessCodeEnum.TransactionReceiptNo.equals(type)) {
                code = value + super.getDateTimeNumber() + RandomUtil.randomNumbers(6);
                repeat = iTransactionReceiptService.existTransactionReceiptNo(code);
            } else if(BusinessCodeEnum.TransactionNo.equals(type)) {
                code = value + super.getDateTimeNumber() + RandomUtil.randomNumbers(6);
                repeat = iTransactionRecordService.existTransactionNo(code);
            } else if (BusinessCodeEnum.PayoneerOrderNo.equals(type)) {
                code = value + super.getDateTimeNumber() + RandomUtil.randomNumbers(6);
                repeat = iTransactionRecordService.existPayoneerOrderNo(code);
            } else if (BusinessCodeEnum.ReceiptAccountNo.equals(type)) {
                code = value + super.getDateTimeNumber() + RandomUtil.randomNumbers(6);
                repeat = iTenantReceiptAccountService.existReceiptAccountNo(code);
            } else if (BusinessCodeEnum.MarketplaceActivityConfig.equals(type)) {
                code = value + super.getDateTimeNumber() + RandomUtil.randomNumbers(6);
                repeat = iMarketplaceActivityConfigService.existMpActivityCode(code);
            }
        }
        if (StrUtil.isNotBlank(code)) {
            return code;
        } else {
            throw new RStatusCodeException(ZSMallStatusCodeEnum.BUSINESS_CODE_GENERATE_ERROR);
        }
    }

    /**
     * 编号生成器
     *
     * @param type    主类型
     * @param subType 子类型
     * @return
     */
    @Override
    public String codeGenerate(AbstractCodeTypeBase type, String subType) {
        var value = type.getValue();
        var code = "";
        var repeat = true;

        while (repeat) {
            LocalTime nowTime = LocalTime.now();
            long digitIncrease = super.getSecondOneDigitIncrease(nowTime, value);
            code = value + subType + digitIncrease + RandomUtil.randomNumbers(5);
            repeat = super.checkSecondDuplicate(nowTime, value, code);
        }

        return code;
    }
}
