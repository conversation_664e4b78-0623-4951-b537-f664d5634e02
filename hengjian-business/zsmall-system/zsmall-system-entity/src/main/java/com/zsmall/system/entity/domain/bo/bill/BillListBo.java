package com.zsmall.system.entity.domain.bo.bill;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 请求参数-账单列表信息
 *
 * <AUTHOR>
 * @date 2022/11/29
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class BillListBo {

    /**
     * 租户编号
     */
    private String tenantId;

    /**
     * 账单年月（yyyy-MM）
     */
    private String billDate;

    /**
     * 导出类型：Excel、PDF
     */
    private String exportType;

    /**
     * 账单状态：已结算，未结算
     */
    private String billStatus;

    /**
     * 提现类型：已提现，未提现
     */
    private String withdrawalStatus;

    /**
     * 员工用查询类型：BillNo-账单编号，UserCode-用户编号
     */
    private String queryType;

    /**
     * 员工用查询值
     */
    private String queryValue;

}
