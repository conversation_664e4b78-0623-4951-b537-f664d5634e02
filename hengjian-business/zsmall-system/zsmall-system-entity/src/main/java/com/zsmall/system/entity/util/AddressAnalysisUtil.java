package com.zsmall.system.entity.util;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zsmall.system.entity.domain.SiteCountryCurrency;
import com.zsmall.system.entity.iservice.IConfZipService;
import com.zsmall.system.entity.iservice.ISiteCountryCurrencyService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/12/17 15:13
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AddressAnalysisUtil {
    private final IConfZipService iConfZipService;
    private final ISiteCountryCurrencyService iSiteCountryCurrencyService;

    /**
     * 功能描述：获取地址,根据地址名称和级别返回国家
     *  该接口用于获取地址信息,查询不到的地址会返回null,业务自行处理.
     * @param addressName  地址名称
     * @param addressLevel 地址级别
     * @return {@link String }
     * <AUTHOR>
     * @date 2024/12/17
     */
    public Long getAddress(String addressName,String addressLevel){

        LambdaQueryWrapper<SiteCountryCurrency> wrapper = new LambdaQueryWrapper<SiteCountryCurrency>().last("limit 1");

        // 国家级别直接返回
        if("country".equals(addressLevel)){
            if(StrUtil.isNotEmpty(addressName)){
                wrapper.eq(SiteCountryCurrency::getCountryCode,addressName);
                SiteCountryCurrency one = iSiteCountryCurrencyService.getOne(wrapper);
                return one.getId();
            }else {
                return null;
            }
        }
        return null;
    }

    /**
     * 功能描述：tiktok地址信息与站点信息转换
     *
     * @param addressName  地址名称
     * @param addressLevel 地址级别
     * @return {@link Long }
     * <AUTHOR>
     * @date 2024/12/20
     */
    public Long getAddressFromSite(String addressName,String addressLevel){

        LambdaQueryWrapper<SiteCountryCurrency> wrapper = new LambdaQueryWrapper<SiteCountryCurrency>().last("limit 1");

        // 国家级别直接返回
        if("country".equals(addressLevel)){
            if(StrUtil.isNotEmpty(addressName)){
                wrapper.eq(SiteCountryCurrency::getCountryName,addressName);
            }else {
                return null;
            }
        }

        SiteCountryCurrency one = iSiteCountryCurrencyService.getOne(wrapper);
        if(ObjectUtil.isNull(one)){
            return null;
        }else {
            return one.getId();
        }
    }
}
