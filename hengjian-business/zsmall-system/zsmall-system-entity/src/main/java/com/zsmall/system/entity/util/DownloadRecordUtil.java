package com.zsmall.system.entity.util;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.StrUtil;
import com.hengjian.common.core.exception.RStatusCodeException;
import com.hengjian.common.core.utils.SpringUtils;
import com.hengjian.extend.event.OSSUploadEvent;
import com.hengjian.system.domain.vo.SysOssVo;
import com.zsmall.common.enums.downloadRecord.DownloadTypePlusEnum;
import com.zsmall.common.enums.downloadRecord.RecordStateEnum;
import com.zsmall.common.enums.statuscode.ZSMallStatusCodeEnum;
import com.zsmall.common.properties.FileProperties;
import com.zsmall.system.entity.domain.DownloadRecord;
import com.zsmall.system.entity.iservice.IDownloadRecordService;
import lombok.Cleanup;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.io.File;
import java.io.InputStream;
import java.util.function.Function;

/**
 * 下载记录工具类
 *
 * <AUTHOR>
 * @date 2023/8/25
 */
@Slf4j
@RequiredArgsConstructor
public class DownloadRecordUtil {

    private static FileProperties fileProperties;
    private static IDownloadRecordService iDownloadRecordService;
    private static ThreadPoolTaskExecutor threadPoolTaskExecutor;

    public DownloadRecordUtil(FileProperties fileProperties, IDownloadRecordService iDownloadRecordService, ThreadPoolTaskExecutor threadPoolTaskExecutor) {
        DownloadRecordUtil.fileProperties = fileProperties;
        DownloadRecordUtil.iDownloadRecordService = iDownloadRecordService;
        DownloadRecordUtil.threadPoolTaskExecutor = threadPoolTaskExecutor;
    }

    public static void generate(String fileName, DownloadTypePlusEnum downloadType, Function<String, File> function) {
        Boolean existsed = iDownloadRecordService.existsByRecordState(RecordStateEnum.Generating);
        if (existsed) {
            throw new RStatusCodeException(ZSMallStatusCodeEnum.DOWNLOAD_RECORD_GENERATING);
        } else {
            // 创建新的下载记录
            DownloadRecord newRecord = new DownloadRecord();
            newRecord.setRecordState(RecordStateEnum.Generating);
            newRecord.setFileName(fileName);
            newRecord.setDownloadType(downloadType);
            iDownloadRecordService.save(newRecord);

            threadPoolTaskExecutor.execute(() -> {
                try {
                    String tempSavePath = fileProperties.getTempSavePath();
                    String tempFileSavePath = StrUtil.builder(tempSavePath, File.separator, downloadType.name(), File.separator, UUID.fastUUID().toString(true), ".", FileUtil.getSuffix(fileName)).toString();

                    log.info("【下载记录工具类】临时文件保存路径 => {}", tempFileSavePath);
                    File file = function.apply(tempFileSavePath);

                    @Cleanup InputStream inputStream = FileUtil.getInputStream(file);
                    OSSUploadEvent uploadEvent = new OSSUploadEvent(inputStream, fileName);
                    SpringUtils.context().publishEvent(uploadEvent);
                    SysOssVo sysOssVo = uploadEvent.getSysOssVo();

                    newRecord.setOssId(sysOssVo.getOssId());
                    newRecord.setFileSaveKey(sysOssVo.getFileName());
                    newRecord.setFileUrl(sysOssVo.getUrl());
                    newRecord.setFileSize(StrUtil.toString(file.length()));
                    newRecord.setRecordState(RecordStateEnum.Ready);
                    iDownloadRecordService.updateById(newRecord);

                    FileUtil.del(file);
                } catch (Exception e) {
                    log.error("【导出通用工具】出现未知错误 {}", e.getMessage(), e);
                    newRecord.setRecordState(RecordStateEnum.Failed);
                    iDownloadRecordService.updateById(newRecord);
                }
            });

        }
    }

}
