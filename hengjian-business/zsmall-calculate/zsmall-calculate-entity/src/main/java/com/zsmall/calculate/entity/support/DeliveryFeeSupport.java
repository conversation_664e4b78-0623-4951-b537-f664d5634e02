package com.zsmall.calculate.entity.support;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.zsmall.common.domain.DeliveryFeeByErpRequest;
import com.zsmall.common.domain.DeliveryFeeByErpResponse;
import com.zsmall.product.entity.domain.ProductSku;
import com.zsmall.product.entity.domain.ProductSkuPrice;
import com.zsmall.product.entity.domain.RuleLevelProductPrice;
import com.zsmall.product.entity.iservice.IProductSkuPriceService;
import com.zsmall.product.entity.iservice.IProductSkuService;
import com.zsmall.bma.open.member.service.RuleLevelProductPriceV2Service;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/9/29 11:17
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DeliveryFeeSupport {
    private final IProductSkuService iProductSkuService;
    private final IProductSkuPriceService iProductSkuPriceService;
    private final RuleLevelProductPriceV2Service ruleLevelProductPriceService;

    /**
     * 询价失败，全局补偿策略类
     *
     * @param deliveryFeeByErpRequest
     * @param carrier
     * @return
     */
    @Deprecated
    public List<DeliveryFeeByErpResponse> getDeliveryFeeByErpByError(List<DeliveryFeeByErpRequest> deliveryFeeByErpRequest,
                                                                     String carrier){
        DeliveryFeeByErpRequest deliveryFeeByErpRequests = deliveryFeeByErpRequest.get(0);
        BigDecimal finalDeliveryFee=BigDecimal.ZERO;
        //转换sku
        ProductSku productSku = iProductSkuService.queryBySku(deliveryFeeByErpRequests.getSkuList().get(0).getErpSku());
        RuleLevelProductPrice memberPrice =new RuleLevelProductPrice();
        // 已废弃
//        RuleLevelProductPrice memberPrice = ruleLevelProductPriceService.getMemberPrice(
//            deliveryFeeByErpRequests.getSupplierTenantId(), deliveryFeeByErpRequests.getDistributorTenantId(), productSku.getId(),null );
        if(ObjectUtil.isNotNull(memberPrice)){
            finalDeliveryFee = memberPrice.getPlatformFinalDeliveryFee();
        }else {
            LambdaQueryWrapper<ProductSkuPrice> pp = new LambdaQueryWrapper<>();
            pp.eq(ProductSkuPrice::getProductSkuId,productSku.getId());
            ProductSkuPrice ignore = TenantHelper.ignore(() -> iProductSkuPriceService.getBaseMapper().selectOne(pp));
            if (ObjectUtil.isNotNull(ignore)){
                finalDeliveryFee=ignore.getPlatformFinalDeliveryFee();
            }
        }
        finalDeliveryFee=finalDeliveryFee==null?BigDecimal.ZERO:finalDeliveryFee;
        //封装返回的对象
        DeliveryFeeByErpResponse d=new DeliveryFeeByErpResponse();
        d.setChannelFlag(deliveryFeeByErpRequests.getChannelFlag());
        List<DeliveryFeeByErpRequest.ProductItem> skuList = deliveryFeeByErpRequests.getSkuList();
        DeliveryFeeByErpResponse.ProductItem productItem=new DeliveryFeeByErpResponse.ProductItem();
        productItem.setErpSku(skuList.get(0).getErpSku());
        productItem.setQuantity(skuList.get(0).getQuantity());
        d.setSkuList(List.of(productItem));
        d.setShippingFee(finalDeliveryFee);
        List<String> orgWarehouseCodeList = deliveryFeeByErpRequests.getOrgWarehouseCodeList();
        d.setOrgWarehouseCode(orgWarehouseCodeList.get(0));
        // 如果订单有给快递公司赋值，否则默认赋值
        if(StrUtil.isEmpty(carrier)){
            d.setCarrierCode("Fedex");
            d.setLogisticCode("FEDHD");
        }else {
            d.setCarrierCode(carrier);
        }
        d.setRequestId(deliveryFeeByErpRequests.getRequestId());
        d.setDistributionCalculateResult(true);
        return List.of(d);
    }
}
