package com.zsmall.bma.open.member.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hengjian.system.domain.SysUser;
import com.zsmall.product.entity.domain.bo.member.MemberLevelQueryBo;
import com.zsmall.product.entity.domain.member.MemberLevel;
import com.zsmall.product.entity.domain.vo.member.LevelPriceDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/5/7 10:34
 */
public interface MemberLevelMapper extends BaseMapper<MemberLevel> {
    IPage<MemberLevel> queryPageList(Page<MemberLevel> queryPage,@Param("queryBo") MemberLevelQueryBo queryBo);


    SysUser getUserName(@Param("userId")Long createBy);

    SysUser checkTenantId(String tenantId);

    String getLevelName(@Param("dictCode")Long dictCode);


    List<LevelPriceDTO> getLevelNames(@Param("dictCodes") List<Long> dictCodes);

    List<LevelPriceDTO> getDictCodesByType(@Param("dictType")String dictType);
}
