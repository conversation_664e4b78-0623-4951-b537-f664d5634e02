package com.zsmall.bma.open.member.service;

import com.hengjian.common.core.domain.R;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.zsmall.product.entity.domain.bo.member.MemberLevelQueryBo;
import com.zsmall.product.entity.domain.vo.member.MemberLevelTableInfoVo;
import com.zsmall.product.entity.domain.vo.member.MemberLevelVO;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/9/27 14:00
 */
public interface MemberLevelV2Service {
    /**
     * 功能描述：查询页面列表
     *
     * @param bo        bo
     * @param pageQuery 页面查询
     * @return {@link MemberLevelTableInfoVo }
     * <AUTHOR>
     * @date 2024/05/07
     */
    MemberLevelTableInfoVo queryPageList(MemberLevelQueryBo bo, PageQuery pageQuery);

    /**
     * 功能描述：添加
     *
     * @param vo vo
     * @return {@link R }<{@link Void }>
     * <AUTHOR>
     * @date 2024/05/07
     */
    R<Void> add(MemberLevelVO vo);

    /**
     * 功能描述：编辑
     *
     * @param vo vo
     * @return {@link R }<{@link Void }>
     * <AUTHOR>
     * @date 2024/05/07
     */
    R<Void> edit(MemberLevelVO vo);

    R<Boolean> editBefore(MemberLevelVO vo);

    /**
     * 供应商创建/编辑商品时判断时候有会员等级生效
     *
     * @return
     */
    R<Boolean> newProductCheck();
}
