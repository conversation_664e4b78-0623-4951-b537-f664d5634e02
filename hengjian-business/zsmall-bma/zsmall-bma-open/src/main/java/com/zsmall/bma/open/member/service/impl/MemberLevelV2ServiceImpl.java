package com.zsmall.bma.open.member.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.hengjian.system.domain.SysUser;
import com.zsmall.bma.open.member.iservice.IMemberLevelV2ServiceImpl;
import com.zsmall.bma.open.member.iservice.IMemberRuleRelationV2ServiceImpl;
import com.zsmall.bma.open.member.service.MemberLevelLogV2Service;
import com.zsmall.bma.open.member.service.MemberLevelV2Service;
import com.zsmall.product.entity.domain.bo.member.MemberLevelQueryBo;
import com.zsmall.product.entity.domain.member.MemberLevel;
import com.zsmall.product.entity.domain.member.MemberRuleRelation;
import com.zsmall.product.entity.domain.vo.member.MemberLevelListVo;
import com.zsmall.product.entity.domain.vo.member.MemberLevelTableInfoVo;
import com.zsmall.product.entity.domain.vo.member.MemberLevelVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/9/27 14:06
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class MemberLevelV2ServiceImpl implements MemberLevelV2Service {

    private final IMemberLevelV2ServiceImpl iMemberLevelService;
    private final MemberLevelLogV2Service memberLevelLogService;
    private final IMemberRuleRelationV2ServiceImpl memberRuleRelationService;

    @Override
    public MemberLevelTableInfoVo queryPageList(MemberLevelQueryBo bo, PageQuery pageQuery) {
        LoginHelper.getLoginUser(TenantType.Supplier);
        List<MemberLevelListVo> results = new ArrayList<>();
        IPage<MemberLevel> page = iMemberLevelService.queryPageList(pageQuery.build(), bo);
        List<MemberLevel> records = page.getRecords();
        for (MemberLevel record : records) {
            MemberLevelListVo memberLevelListVo = new MemberLevelListVo();
            BeanUtil.copyProperties(record, memberLevelListVo);
            Long createBy = memberLevelListVo.getCreateBy();
            if(ObjectUtil.isNotEmpty(createBy)){
                SysUser user = iMemberLevelService.getUserName(createBy);
                memberLevelListVo.setOperatorName(user.getNickName());
            }
            String levelName = iMemberLevelService.getLevelName(memberLevelListVo.getDictCode());
            memberLevelListVo.setLevelName(levelName);
            results.add(memberLevelListVo);
        }
        MemberLevelTableInfoVo vo = BeanUtil.toBean(TableDataInfo.build(results, page.getTotal()), MemberLevelTableInfoVo.class);
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<Void> add(MemberLevelVO vo) {
        LoginHelper.getLoginUser(TenantType.Supplier);
        String tenantId = LoginHelper.getTenantId();
        // 新增前校验 1.总数不大于3 2.同名称同tenantId是否已存在

//        String tenantId = "SJN1857";
        List<MemberLevel> list = iMemberLevelService.list(new LambdaQueryWrapper<MemberLevel>().eq(MemberLevel::getTenantId, tenantId)
                                                                                               .eq(MemberLevel::getDelFlag, 0));
        if (ObjectUtil.isNotEmpty(list)) {
//            if (list.size() >= 3) {
//                throw new RuntimeException("当前租户定制等级已达上限");
//            }
            List<MemberLevel> collect = list.stream().filter(a -> a.getDictCode().equals(vo.getDictCode()))
                                            .collect(Collectors.toList());
            if (ObjectUtil.isNotEmpty(collect)) {
                throw new RuntimeException("该等级已存在");
            }
        }
        MemberLevel memberLevel = new MemberLevel();
        memberLevel.setTenantId(tenantId);
        memberLevel.setStatus(vo.getStatus());
        memberLevel.setDictCode(vo.getDictCode());
        memberLevel.setCreateBy(LoginHelper.getUserId());
        memberLevel.setUpdateBy(LoginHelper.getUserId());
        iMemberLevelService.save(memberLevel);
        memberLevelLogService.createAddLog(memberLevel);
        return R.ok();
    }

    @Override
    public R<Void> edit(MemberLevelVO vo) {
        LoginHelper.getLoginUser(TenantType.Supplier);
        MemberLevel memberLevel = new MemberLevel();
        BeanUtil.copyProperties(vo, memberLevel);
        MemberLevel old = iMemberLevelService.getById(vo.getId());
        iMemberLevelService.updateById(memberLevel);
        memberLevelLogService.createUpdateLog(old,memberLevel);
        return R.ok();
    }

    @Override
    public R<Boolean> editBefore(MemberLevelVO vo) {
        String tenantId = LoginHelper.getTenantId();
        LambdaQueryWrapper<MemberRuleRelation> lqw = new LambdaQueryWrapper<>();
        lqw.eq(MemberRuleRelation::getRuleCustomizerTenantId,tenantId)
           .eq(MemberRuleRelation::getLevelId,vo.getId())
           .isNotNull(MemberRuleRelation::getRuleFollowerTenantId);
        List<MemberRuleRelation> list = memberRuleRelationService.list(lqw);
        if(!list.isEmpty()){
            return R.ok(false);
        }
        return R.ok(true);
    }

    @Override
    public R<Boolean> newProductCheck() {
        String tenantId = LoginHelper.getTenantId();
        LambdaQueryWrapper<MemberLevel> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(MemberLevel::getTenantId,tenantId)
                          .eq(MemberLevel::getStatus,0)
                          .eq(MemberLevel::getDelFlag, 0);
        List<MemberLevel> list = iMemberLevelService.list(lambdaQueryWrapper);
        if(CollectionUtil.isEmpty(list)){
            return R.ok(Boolean.FALSE);
        }else {
            return R.ok(Boolean.TRUE);
        }
    }
}
