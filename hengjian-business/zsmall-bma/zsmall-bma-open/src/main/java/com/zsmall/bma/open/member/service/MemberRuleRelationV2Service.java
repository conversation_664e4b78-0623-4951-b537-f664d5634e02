package com.zsmall.bma.open.member.service;

import com.hengjian.common.core.domain.R;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.zsmall.product.entity.domain.bo.member.MemberLevelQueryBo;
import com.zsmall.product.entity.domain.vo.member.MemberRuleRelationTableInfoVo;
import com.zsmall.product.entity.domain.vo.member.MemberRuleRelationVO;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/9/27 13:57
 */
public interface MemberRuleRelationV2Service {
    MemberRuleRelationTableInfoVo queryPageList(MemberLevelQueryBo bo, PageQuery pageQuery);

    R<Void> add(MemberRuleRelationVO vo);

    R<Void> del(MemberRuleRelationVO vo);

    R<Void> edit(MemberRuleRelationVO vo);

    R<Void> addBefore(String tenantId);

    R<Boolean> addProductAfter();
}
