package com.zsmall.bma.open.member.support;

import com.zsmall.bma.open.member.service.IMemberDiscountV2Service;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/9/29 14:33
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class MemberV2Support {

    private final IMemberDiscountV2Service memberDiscountService;
    public BigDecimal getDiscountFactor(Long dictCode) {
        // 1. 获取会员折扣
        return memberDiscountService.getMemberDiscountByDictCode(dictCode);
    }
}
