package com.zsmall.bma.open.member.manger;

import cn.hutool.core.util.ObjectUtil;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.zsmall.bma.open.member.service.RuleLevelProductPriceV2Service;
import com.zsmall.common.annotaion.Manager;
import com.zsmall.common.enums.order.LogisticsTypeEnum;
import com.zsmall.product.entity.domain.RuleLevelProductPrice;
import com.zsmall.product.entity.domain.vo.member.MemberLevelPriceVo;
import com.zsmall.product.entity.domain.vo.member.MemberSkuPriceVo;
import com.zsmall.product.entity.mapper.RuleLevelProductPriceMapper;
import com.zsmall.system.entity.domain.SiteCountryCurrency;
import com.zsmall.system.entity.iservice.ISiteCountryCurrencyService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2025/1/14 14:20
 */
@Manager
@Slf4j
@RequiredArgsConstructor
public class RuleLevelProductPriceV2Manger {
//    private final IProductService iProductService;
//    private final IProductMappingService iProductMappingService;
//    private final IProductCategoryService iProductCategoryService;
//    private final IProductReviewRecordService productReviewRecordService;
//    private final IProductReviewRecordService iProductReviewRecordService;
//
//    private final IProductGlobalAttributeService iProductGlobalAttributeService;
//
//    private final IProductSkuService iProductSkuService;
//
//    private final IProductSkuAttachmentService iProductSkuAttachmentService;
//    private final IProductSkuPriceService iProductSkuPriceService;
//    private final IProductSkuPriceLogService iProductSkuPriceLogService;
//    private final IProductSkuStockService iProductSkuStockService;
//    private final IProductSkuPriceRuleService iProductSkuPriceRuleService;
//
//    private final ILogisticsTemplateService iLogisticsTemplateService;
//    private final IProductImportRecordService iProductImportRecordService;
//    private final IWarehouseService iWarehouseService;
//    private final IProductSupport productSupport;
//    private final ProductCodeGenerator productCodeGenerator;
//    private final FileProperties fileProperties;
//    private final IRuleLevelProductPriceService iRuleLevelProductPriceService;
//    private final ISiteCountryCurrencyService iSiteCountryCurrencyService;
//    private final IMemberLevelV2ServiceImpl memberLevelService;
//
//    private final IMemberRuleRelationV2ServiceImpl memberRuleRelationService;
//
//    private final MemberLevelMapper memberLevelMapper;

    private final RuleLevelProductPriceMapper ruleLevelProductPriceMapper;

    private final RuleLevelProductPriceV2Service ruleLevelProductPriceService;

    private final ISiteCountryCurrencyService iSiteCountryCurrencyService;
    public R<Void> edit(MemberSkuPriceVo vo) {
        String tenantId = LoginHelper.getTenantId();
        Long userId = LoginHelper.getUserId();
        Long productId = vo.getProductId();
        Long productSkuId = vo.getProductSkuId();
        List<MemberLevelPriceVo> priceListVo = vo.getMemberLevelPriceListVo();
        List<RuleLevelProductPrice> ruleLevelProductPrices = new ArrayList<>();
        List<RuleLevelProductPrice> ruleLevelForSave = new ArrayList<>();
        Map<Long, SiteCountryCurrency> siteIdMap = iSiteCountryCurrencyService.getSiteIdMap();
        for (MemberLevelPriceVo priceVo : priceListVo) {
            //priceVo.setOriginalFinalDeliveryFee(BigDecimal.ZERO);
            Long siteId = priceVo.getSiteId();
            SiteCountryCurrency currency = siteIdMap.get(siteId);
            String currencyCode = currency.getCurrencyCode();
            String currencySymbol = currency.getCurrencySymbol();
            String countryCode = currency.getCountryCode();
            // todo 默认售价逻辑移除
            if (priceVo.getIsDefault()) {
                BigDecimal originalUnitPrice = priceVo.getOriginalUnitPrice();
                BigDecimal originalFinalDeliveryFee = priceVo.getOriginalFinalDeliveryFee();
                BigDecimal originalOperationFee = priceVo.getOriginalOperationFee();
                if (ObjectUtil.isNotEmpty(originalUnitPrice) && ObjectUtil.isNotEmpty(originalFinalDeliveryFee) && ObjectUtil.isNotEmpty(originalOperationFee)) {
                    ruleLevelProductPriceService.changeProductPrice(productSkuId, priceVo.getOriginalUnitPrice(), priceVo.getOriginalOperationFee(), priceVo.getOriginalFinalDeliveryFee(), LoginHelper.getTenantId(),siteId);
                    continue;
                }
            }

            RuleLevelProductPrice ruleLevelProductPrice = new RuleLevelProductPrice();
            ruleLevelProductPrice.setRuleCustomizerTenantId(tenantId);
            ruleLevelProductPrice.setProductSkuId(productSkuId);
            ruleLevelProductPrice.setProductId(productId);
            ruleLevelProductPrice.setDelFlag(0);
            ruleLevelProductPrice.setCreateBy(userId);
            ruleLevelProductPrice.setUpdateBy(userId);
            BigDecimal pickPrice = null;
            BigDecimal dropPrice = null;


            ruleLevelProductPrice.setId(priceVo.getRulePriceId());
            ruleLevelProductPrice.setLevelId(priceVo.getLevelId());
            if (!(ObjectUtil.isNull(priceVo.getOriginalUnitPrice()) && ObjectUtil.isNull(priceVo.getOriginalOperationFee()) && ObjectUtil.isNull(priceVo.getOriginalFinalDeliveryFee()))){
                if (ObjectUtil.isNull(priceVo.getOriginalUnitPrice())){
                    priceVo.setOriginalUnitPrice(BigDecimal.ZERO);
                }
                if (ObjectUtil.isNull(priceVo.getOriginalOperationFee())){
                    priceVo.setOriginalOperationFee(BigDecimal.ZERO);
                }
                if (ObjectUtil.isNull(priceVo.getOriginalFinalDeliveryFee())){
                    priceVo.setOriginalFinalDeliveryFee(BigDecimal.ZERO);
                }
            }
            ruleLevelProductPrice.setSiteId(siteId);
            ruleLevelProductPrice.setCurrency(currencyCode);
            ruleLevelProductPrice.setCurrencySymbol(currencySymbol);
            ruleLevelProductPrice.setCountryCode(countryCode);
            ruleLevelProductPrice.setOriginalUnitPrice(priceVo.getOriginalUnitPrice());
            ruleLevelProductPrice.setOriginalOperationFee(priceVo.getOriginalOperationFee());
            ruleLevelProductPrice.setOriginalFinalDeliveryFee(priceVo.getOriginalFinalDeliveryFee());

            ruleLevelProductPrice.setPlatformUnitPrice(priceVo.getOriginalUnitPrice());
            ruleLevelProductPrice.setPlatformOperationFee(priceVo.getOriginalOperationFee());
            ruleLevelProductPrice.setPlatformFinalDeliveryFee(priceVo.getOriginalFinalDeliveryFee());

            // 两个价格需要计算
            pickPrice = ruleLevelProductPriceService.computationPrice(priceVo.getOriginalUnitPrice(), priceVo.getOriginalFinalDeliveryFee(), priceVo.getOriginalOperationFee(), LogisticsTypeEnum.PickUp);
            dropPrice = ruleLevelProductPriceService.computationPrice(priceVo.getOriginalUnitPrice(), priceVo.getOriginalFinalDeliveryFee(), priceVo.getOriginalOperationFee(), LogisticsTypeEnum.DropShipping);

            ruleLevelProductPrice.setOriginalPickUpPrice(pickPrice);
            ruleLevelProductPrice.setOriginalDropShippingPrice(dropPrice);
            ruleLevelProductPrice.setPlatformDropShippingPrice(dropPrice);
            ruleLevelProductPrice.setPlatformPickUpPrice(pickPrice);
            // 实际为新增业务 产品糅合在一块了
            if (ObjectUtil.isEmpty(priceVo.getRulePriceId()) && (ObjectUtil.isNotEmpty(priceVo.getOriginalUnitPrice())
                || ObjectUtil.isNotEmpty(priceVo.getOriginalOperationFee())
                || ObjectUtil.isNotEmpty(priceVo.getOriginalFinalDeliveryFee()))) {
                if (ObjectUtil.isEmpty(ruleLevelProductPrice.getId())) {
                    ruleLevelForSave.add(ruleLevelProductPrice);
                    continue;
                }
            }
            ruleLevelProductPrices.add(ruleLevelProductPrice);
        }
        for (RuleLevelProductPrice ruleLevelProductPrice : ruleLevelProductPrices) {
            ruleLevelProductPriceMapper.updateRuleLevelProductPriceNullValue(ruleLevelProductPrice);
        }
        ruleLevelProductPriceService.saveBatch(ruleLevelForSave);
        return R.ok();
    }
}
