package com.zsmall.bma.open.member.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zsmall.product.entity.domain.bo.member.MemberLevelQueryBo;
import com.zsmall.product.entity.domain.member.MemberRuleRelation;
import org.apache.ibatis.annotations.Param;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/5/8 16:08
 */
public interface MemberRuleRelationMapper extends BaseMapper<MemberRuleRelation> {
    IPage<MemberRuleRelation> queryPageList(Page<MemberRuleRelation> queryPage,@Param("queryBo") MemberLevelQueryBo queryBo);

    void logicDel(@Param("id") Long id);

}
