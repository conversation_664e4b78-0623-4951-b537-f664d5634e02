package com.zsmall.bma.open.member.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.hengjian.system.domain.SysUser;
import com.zsmall.bma.open.member.iservice.IMemberLevelV2ServiceImpl;
import com.zsmall.bma.open.member.iservice.IMemberRuleRelationV2ServiceImpl;
import com.zsmall.bma.open.member.service.MemberRuleRelationV2Service;
import com.zsmall.product.entity.anno.IsSame;
import com.zsmall.product.entity.domain.bo.member.MemberLevelQueryBo;
import com.zsmall.product.entity.domain.member.MemberLevel;
import com.zsmall.product.entity.domain.member.MemberRuleRelation;
import com.zsmall.product.entity.domain.vo.member.MemberRuleRelationListVO;
import com.zsmall.product.entity.domain.vo.member.MemberRuleRelationTableInfoVo;
import com.zsmall.product.entity.domain.vo.member.MemberRuleRelationVO;
import com.zsmall.bma.open.member.mapper.MemberLevelMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/9/27 14:07
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class MemberRuleRelationV2ServiceImpl implements MemberRuleRelationV2Service {
    private final IMemberRuleRelationV2ServiceImpl memberRuleRelationService;
    private final IMemberLevelV2ServiceImpl iMemberLevelService;
    private final MemberRuleRelationLogV2ServiceImpl memberRuleRelationLogService;
    private final MemberLevelMapper memberLevelMapper;

    @Override
    public MemberRuleRelationTableInfoVo queryPageList(MemberLevelQueryBo bo, PageQuery pageQuery) {
        List<MemberRuleRelationListVO> results = new ArrayList<>();
        String ruleCustomizerTenantId = LoginHelper.getTenantId();
        bo.setRuleCustomizerTenantId(ruleCustomizerTenantId);
        IPage<MemberRuleRelation> page = TenantHelper.ignore(()->memberRuleRelationService.queryPageList(pageQuery.build(), bo));
        bo.setRuleCustomizerTenantId(ruleCustomizerTenantId);
        List<MemberRuleRelation> records = page.getRecords();
        for (MemberRuleRelation record : records) {
            MemberRuleRelationListVO memberLevelListVo = new MemberRuleRelationListVO();
            BeanUtil.copyProperties(record, memberLevelListVo);
            Long createBy = memberLevelListVo.getCreateBy();
            if(ObjectUtil.isNotEmpty(createBy)){
                SysUser user = TenantHelper.ignore(()->iMemberLevelService.getUserName(createBy));
                memberLevelListVo.setOperatorName(user.getNickName());

            }
            if(ObjectUtil.isNotEmpty(record.getLevelId())){
                MemberLevel level = iMemberLevelService.getById(record.getLevelId());
                String levelName = memberLevelMapper.getLevelName(level.getDictCode());
                memberLevelListVo.setLevelId(record.getLevelId());
                if(level.getStatus()==0){
                    memberLevelListVo.setLevelName(levelName);
                }else {
                    memberLevelListVo.setLevelName("-");
                }

            }

            results.add(memberLevelListVo);
        }
        MemberRuleRelationTableInfoVo vo = BeanUtil.toBean(TableDataInfo.build(results, page.getTotal()), MemberRuleRelationTableInfoVo.class);
        return vo;

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<Void> add(MemberRuleRelationVO vo) {

        if(ObjectUtil.isNotEmpty(vo.getEmailAddress())){
            Pattern emailPattern =
                Pattern.compile("^[_A-Za-z0-9-\\+]+(\\.[_A-Za-z0-9-]+)*@" +
                    "[A-Za-z0-9-]+(\\.[A-Za-z0-9]+)*(\\.[A-Za-z]{2,})$");
            Matcher matcher = emailPattern.matcher(vo.getEmailAddress());
            if(!matcher.matches()){
                throw new RuntimeException("邮箱格式错误");
            }
        }
        if(ObjectUtil.isNotEmpty(vo.getPhoneNumber())){
            Pattern phonePattern =
                Pattern.compile("^[_A-Za-z0-9-\\+]+(\\.[_A-Za-z0-9-]+)*@" +
                    "[A-Za-z0-9-]+(\\.[A-Za-z0-9]+)*(\\.[A-Za-z]{2,})$");
            Matcher phone = phonePattern.matcher("^1[3-9]\\d{9}$\n");
            if(!phone.matches()){
                throw new RuntimeException("手机号码格式错误");
            }
        }


        LoginHelper.getLoginUser(TenantType.Supplier);
        String tenantId = LoginHelper.getTenantId();
        Long userId = LoginHelper.getUserId();
        vo.setRuleCustomizerTenantId(tenantId);

        MemberRuleRelation memberRuleRelation = new MemberRuleRelation();
        BeanUtil.copyProperties(vo,memberRuleRelation);
        memberRuleRelation.setCreateBy(LoginHelper.getUserId());
        memberRuleRelation.setUpdateBy(LoginHelper.getUserId());
        LambdaQueryWrapper<MemberRuleRelation> eq = new LambdaQueryWrapper<MemberRuleRelation>()
            .eq(MemberRuleRelation::getRuleCustomizerTenantId, tenantId)
            .eq(MemberRuleRelation::getRuleFollowerTenantId, vo.getRuleFollowerTenantId())
            .eq(MemberRuleRelation::getDelFlag, 0).last("limit 1");
        MemberRuleRelation one = memberRuleRelationService.getOne(eq);

        if(ObjectUtil.isNotEmpty(one)){
            throw new RuntimeException("经销商已有等级");
        }
//        SysUser user = TenantHelper.ignore(()->iMemberLevelService.getUserName(userId)) ;
//
//        memberRuleRelation.setNickName(user.getNickName());

        memberRuleRelationService.add(memberRuleRelation);
        // 日志
        memberRuleRelationLogService.createAddLog(memberRuleRelation);
        return R.ok();
    }

    @Override
    public R<Void> del(MemberRuleRelationVO vo) {

        memberRuleRelationService.del(vo.getId());
        memberRuleRelationLogService.createDelLog(vo);
        return R.ok();
    }

    @Override
    @IsSame
    public R<Void> edit(MemberRuleRelationVO vo) {
        MemberRuleRelation memberRuleRelation = new MemberRuleRelation();
        MemberRuleRelation old = memberRuleRelationService.getById(vo.getId());

        BeanUtil.copyProperties(vo,memberRuleRelation);
        memberRuleRelationService.updateById(memberRuleRelation);
        memberRuleRelationLogService.createEditLog(vo,old);
        return null;
    }

    @Override
    public R<Void> addBefore(String tenantId) {
        SysUser sysUser = TenantHelper.ignore(()->iMemberLevelService.checkTenantId(tenantId));
        if(ObjectUtil.isEmpty(sysUser)){
            throw new RuntimeException("数据有误,无法保存.此经销商未注册,或者经销商ID有误,请查证");
        }

        return R.ok();
    }

    @Override
    public R<Boolean> addProductAfter() {
        List<MemberLevel> memberLevels = memberLevelMapper.selectList(new LambdaQueryWrapper<MemberLevel>().eq(MemberLevel::getStatus, 0)
                                                                                                           .eq(MemberLevel::getDelFlag, 0));
        if(ObjectUtil.isNotEmpty(memberLevels)){
            MemberRuleRelation one = memberRuleRelationService.getOne(new LambdaQueryWrapper<MemberRuleRelation>()
                .eq(MemberRuleRelation::getRuleCustomizerTenantId, LoginHelper.getTenantId())
                .eq(MemberRuleRelation::getDelFlag,0)
                .last("limit 1"));

            if(ObjectUtil.isNotEmpty(one)){
                return R.ok(true);
            }
        }
        return R.ok(false);
    }
}
