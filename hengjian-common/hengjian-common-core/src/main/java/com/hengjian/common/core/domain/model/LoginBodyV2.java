package com.hengjian.common.core.domain.model;

import com.hengjian.common.core.constant.UserConstants;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;


/**
 * 用户登录对象
 *
 * <AUTHOR> Li
 */
@Data
public class LoginBodyV2 {

    /**
     * 用户名
     */
    @NotBlank(message = "{user.username.not.blank}")
//    @Length(min = UserConstants.USERNAME_MIN_LENGTH, max = UserConstants.USERNAME_MAX_LENGTH, message = "{user.username.length.valid}")
    private String username;

//    /**
//     * 登录方式：Email/PhoneNumber
//     */
//    @NotBlank(message = "{user.username.not.blank}")
//    private String loginMode;


//    /**
//     * 邮箱（仅邮箱登录需要）
//     */
//    private String email;
//
//    /**
//     * 手机号码国际区号（仅手机号、验证码登录需要）
//     */
//    private String areaCode;
//
//    /**
//     * 手机号（仅手机号、验证码登录需要）
//     */
//    private String phoneNumber;

    /**
     * 用户密码 MD5加密后
     */
    @NotBlank(message = "{user.password.not.blank}")
    @Length(min = UserConstants.PASSWORD_MIN_LENGTH, max = UserConstants.PASSWORD_MAX_LENGTH, message = "{user.password.length.valid}")
    private String password;

    /**
     * 验证码
     */
    private String code;

    /**
     * 唯一标识
     */
    private String uuid;

    /**
     * 渠道相关扩展信息
     */
    private ChannelExtra channelExtra;

    @Data
    public static class ChannelExtra {
        private String channelType;

        private String access;
    }

}
