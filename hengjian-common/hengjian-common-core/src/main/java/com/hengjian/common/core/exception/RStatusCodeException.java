package com.hengjian.common.core.exception;

import com.hengjian.common.core.domain.RStatusCodeBase;
import com.hengjian.common.core.utils.MessageUtils;
import lombok.Getter;

/**
 * R状态码专用异常
 * <AUTHOR>
 * @date 2023/5/8
 */
@Getter
public class RStatusCodeException extends RuntimeException {

    private String customMsg;

    private RStatusCodeBase statusCode;

    public RStatusCodeException() {
        super();
    }

    public RStatusCodeException(RStatusCodeBase statusCode, String customMsg) {
        super(MessageUtils.message(statusCode.getMessageCode()));
        this.statusCode = statusCode;
        this.customMsg = customMsg;
    }

    public RStatusCodeException(RStatusCodeBase statusCode) {
        super(MessageUtils.message(statusCode.getMessageCode()));
        this.statusCode = statusCode;
    }

    public RStatusCodeException(String message) {
        super(message);
    }

    public RStatusCodeException(String message, Throwable cause) {
        super(message, cause);
    }

    public RStatusCodeException(Throwable cause) {
        super(cause);
    }

    protected RStatusCodeException(String message, Throwable cause, boolean enableSuppression,
        boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }

}
