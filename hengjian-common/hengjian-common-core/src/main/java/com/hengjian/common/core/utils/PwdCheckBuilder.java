package com.hengjian.common.core.utils;

import java.util.ArrayList;
import java.util.List;

/**
 * 密码校验构造者
 *
 * <AUTHOR>
 * @date 2023/9/14
 */
public class PwdCheckBuilder {

    private String password;

    private Integer minNum;

    private Integer maxNum;

    private List<String> checkType;

    /**
     * 本类的构造方法请勿直接使用，请使用PwdCheckUtils.builder(password)来创建builder实例
     * @param password
     */
    protected PwdCheckBuilder(String password) {
        this.password = password;
        this.checkType = new ArrayList<>();
    }

    /**
     * 校验长度
     * @param minNum
     * @param maxNum
     * @return
     */
    public PwdCheckBuilder containLength(Integer minNum, Integer maxNum) {
        this.minNum = minNum;
        this.maxNum = maxNum;
        this.checkType.add("Length");
        return this;
    }

    /**
     * 校验数字
     * @return
     */
    public PwdCheckBuilder containDigit() {
        this.checkType.add("Digit");
        return this;
    }

    /**
     * 校验字母
     * @return
     */
    public PwdCheckBuilder containCase() {
        this.checkType.add("Case");
        return this;
    }

    /**
     * 校验小写字母
     * @return
     */
    public PwdCheckBuilder containLowerCase() {
        this.checkType.add("LowerCase");
        return this;
    }

    /**
     * 校验大写字母
     * @return
     */
    public PwdCheckBuilder containUpperCase() {
        this.checkType.add("UpperCase");
        return this;
    }

    /**
     * 校验特殊符号
     * @return
     */
    public PwdCheckBuilder containSpecialChar() {
        this.checkType.add("SpecialChar");
        return this;
    }

    /**
     * 开始校验
     */
    public void start() {
        PwdCheckUtils.start(this.password, this.minNum, this.maxNum, this.checkType);
    }

}
