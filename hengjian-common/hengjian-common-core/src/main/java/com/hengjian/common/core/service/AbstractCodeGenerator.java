package com.hengjian.common.core.service;

import com.hengjian.common.core.constant.AbstractCodeTypeBase;
import com.hengjian.common.core.exception.RStatusCodeException;

import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;

/**
 * 业务码生成器-抽象类
 *
 */
public abstract class AbstractCodeGenerator {

    protected static final DateTimeFormatter FORMATTER_DATE = DateTimeFormatter.ofPattern("yyyyMMdd");
    protected static final DateTimeFormatter FORMATTER_DATE_SIMPLE_YEAR = DateTimeFormatter.ofPattern("yyMMdd");
    protected static final DateTimeFormatter FORMATTER_TIME = DateTimeFormatter.ofPattern("HHmmss");
    protected static final DateTimeFormatter FORMATTER_MILLISECOND = DateTimeFormatter.ofPattern("SSS");
    protected static final DateTimeFormatter FORMATTER_SECOND = DateTimeFormatter.ofPattern("ss");


    /**
     * 编号生成器
     * @param type 主类型
     * @return
     */
    protected abstract String codeGenerate(AbstractCodeTypeBase type) throws RStatusCodeException;

    /**
     * 编号生成器
     * @param type 主类型
     * @param subType 子类型
     * @return
     */
    protected abstract String codeGenerate(AbstractCodeTypeBase type, String subType) throws RStatusCodeException;

    /**
     * 获取时间点字符串
     * @return
     */
    protected String getDateTimeNumber() {
        LocalDate localDate = LocalDate.now();
        LocalTime localTime = LocalTime.now();

        String timeString = FORMATTER_TIME.format(localTime);
        String dateString = FORMATTER_DATE.format(localDate);

        return timeString + dateString;
    }

    /**
     * 获取时间点字符串（2位年份）
     * @return
     */
    protected String getDateTimeNumber2Year() {
        LocalDate localDate = LocalDate.now();
        LocalTime localTime = LocalTime.now();

        String timeString = FORMATTER_TIME.format(localTime);
        String dateString = FORMATTER_DATE_SIMPLE_YEAR.format(localDate);

        return timeString + dateString;
    }

    /**
     * 获取时间点字符串（秒）
     * @return
     */
    protected String getSecond() {
        LocalTime localTime = LocalTime.now();
        String second = FORMATTER_SECOND.format(localTime);
        return second;
    }

    /**
     * 获取时间点字符串（毫秒）
     * @return
     */
    protected String getMillisecond() {
        LocalTime localTime = LocalTime.now();
        String millisecond = FORMATTER_MILLISECOND.format(localTime);
        return millisecond;
    }

    /**
     * 获取时间字符串
     * @return
     */
    protected String getDateNumber() {
        LocalDate localDate = LocalDate.now();
        String dateString = FORMATTER_DATE.format(localDate);

        return dateString;
    }

}
