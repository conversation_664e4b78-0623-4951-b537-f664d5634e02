package com.hengjian.common.core.service;

/**
 * 通用 用户服务
 *
 * <AUTHOR> Li
 */
public interface UserService {

    /**
     * 通过用户ID查询用户账户
     *
     * @param userId 用户ID
     * @return 用户账户
     */
    String selectUserNameById(Long userId);

    /**
     * 忽略租户 - 通过用户ID查询用户账户
     * @param userId 用户ID
     * @return 用户账户
     */
    String selectUserNameByIdNoTenant(Long userId);

    String selectUserNameByIdNoTenantNotCache(Long userId);


}
