package com.hengjian.common.security.handler;

import cn.dev33.satoken.exception.NotLoginException;
import cn.dev33.satoken.exception.NotPermissionException;
import cn.dev33.satoken.exception.NotRoleException;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpStatus;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.domain.RStatusCodeBase;
import com.hengjian.common.core.exception.DemoModeException;
import com.hengjian.common.core.exception.RStatusCodeException;
import com.hengjian.common.core.exception.ServiceException;
import com.hengjian.common.core.utils.MessageUtils;
import com.hengjian.common.core.utils.StreamUtils;
import com.hengjian.common.core.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.validation.BindException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingPathVariableException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;

import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.lang.reflect.UndeclaredThrowableException;

/**
 * 全局异常处理器
 *
 * <AUTHOR> Li
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    /**
     * 权限码异常
     */
    @ExceptionHandler(NotPermissionException.class)
    public R<Void> handleNotPermissionException(NotPermissionException e, HttpServletRequest request) {
        String requestURI = request.getRequestURI();
        log.error("请求地址'{}',权限码校验失败'{}'", requestURI, e.getMessage());
        return R.fail(HttpStatus.HTTP_FORBIDDEN, "没有访问权限，请联系管理员授权");
    }

    /**
     * 角色权限异常
     */
    @ExceptionHandler(NotRoleException.class)
    public R<Void> handleNotRoleException(NotRoleException e, HttpServletRequest request) {
        String requestURI = request.getRequestURI();
        log.error("请求地址'{}',角色权限校验失败'{}'", requestURI, e.getMessage());
        return R.fail(HttpStatus.HTTP_FORBIDDEN, "没有访问权限，请联系管理员授权");
    }

    /**
     * 认证失败
     */
    @ExceptionHandler(NotLoginException.class)
    public R<Void> handleNotLoginException(NotLoginException e, HttpServletRequest request) {
        String requestURI = request.getRequestURI();
        log.error("请求地址'{}',认证失败'{}',无法访问系统资源", requestURI, e.getMessage());
        return R.fail(HttpStatus.HTTP_UNAUTHORIZED, "认证失败，无法访问系统资源");
    }

    /**
     * 请求方式不支持
     */
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public R<Void> handleHttpRequestMethodNotSupported(HttpRequestMethodNotSupportedException e,
                                                                HttpServletRequest request) {
        String requestURI = request.getRequestURI();
        log.error("请求地址'{}',不支持'{}'请求", requestURI, e.getMethod());
        return R.fail(e.getMessage());
    }

    /**
     * 业务异常
     */
    @ExceptionHandler(ServiceException.class)
    public R<Void> handleServiceException(ServiceException e, HttpServletRequest request) {
        log.error(e.getMessage(), e);
        Integer code = e.getCode();
        return ObjectUtil.isNotNull(code) ? R.fail(code, e.getMessage()) : R.fail(e.getMessage());
    }

    /**
     * 请求路径中缺少必需的路径变量
     */
    @ExceptionHandler(MissingPathVariableException.class)
    public R<Void> handleMissingPathVariableException(MissingPathVariableException e, HttpServletRequest request) {
        String requestURI = request.getRequestURI();
        log.error("请求路径中缺少必需的路径变量'{}',发生系统异常.", requestURI, e);
        return R.fail(String.format("请求路径中缺少必需的路径变量[%s]", e.getVariableName()));
    }

    /**
     * 请求参数类型不匹配
     */
    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    public R<Void> handleMethodArgumentTypeMismatchException(MethodArgumentTypeMismatchException e, HttpServletRequest request) {
        String requestURI = request.getRequestURI();
        log.error("请求参数类型不匹配'{}',发生系统异常.", requestURI, e);
        return R.fail(String.format("请求参数类型不匹配，参数[%s]要求类型为：'%s'，但输入值为：'%s'", e.getName(), e.getRequiredType().getName(), e.getValue()));
    }

    /**
     * 拦截未知的运行时异常
     */
    @ExceptionHandler(RuntimeException.class)
    public R<Void> handleRuntimeException(RuntimeException e, HttpServletRequest request) {
        String requestURI = request.getRequestURI();
        log.error("请求地址'{}',发生未知异常.", requestURI, e);

        if (e instanceof DataIntegrityViolationException) {
            Throwable cause = e.getCause();
            if (cause != null) {
                String message = cause.getMessage();
                if (StrUtil.contains(message, "Data too long for column")) {
                    return R.fail(MessageUtils.message("database.DataTooLong"));
                }
            }
            return R.fail(MessageUtils.message("database.unknownError"));
        }
        return R.fail(e.getMessage());
    }

    /**
     * 系统异常
     */
    @ExceptionHandler(Exception.class)
    public R<Void> handleException(Exception e, HttpServletRequest request) {
        String requestURI = request.getRequestURI();
        log.error("请求地址'{}',发生系统异常.", requestURI, e);
        return R.fail(e.getMessage());
    }

    /**
     * 自定义验证异常
     */
    @ExceptionHandler(BindException.class)
    public R<Void> handleBindException(BindException e) {
        log.error(e.getMessage(), e);
        String message = StreamUtils.join(e.getAllErrors(), DefaultMessageSourceResolvable::getDefaultMessage, ", ");
        return R.fail(message);
    }

    /**
     * 自定义验证异常
     */
    @ExceptionHandler(ConstraintViolationException.class)
    public R<Void> constraintViolationException(ConstraintViolationException e) {
        log.error(e.getMessage(), e);
        String message = StreamUtils.join(e.getConstraintViolations(), ConstraintViolation::getMessage, ", ");
        return R.fail(message);
    }

    /**
     * 自定义验证异常
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public R<Void> handleMethodArgumentNotValidException(MethodArgumentNotValidException e) {
        log.error(e.getMessage(), e);
        String message = e.getBindingResult().getFieldError().getDefaultMessage();
        return R.fail(message);
    }

    /**
     * 演示模式异常
     */
    @ExceptionHandler(DemoModeException.class)
    public R<Void> handleDemoModeException(DemoModeException e) {
        return R.fail("演示模式，不允许操作");
    }

    /**
     * R状态码专用异常
     */
    @ExceptionHandler(RStatusCodeException.class)
    public R<Void> handleRStatusCodeException(RStatusCodeException e, HttpServletRequest request) {
        String requestURI = request.getRequestURI();
        log.error("请求地址'{}',发生系统异常.", requestURI, e);
        String customMsg = e.getCustomMsg();
        RStatusCodeBase statusCode = e.getStatusCode();
        if (statusCode == null) {
            return R.fail(e.getMessage());
        }
        String subCode = statusCode.getSubCode();
        String message = statusCode.getMessage();
        Object[] args = statusCode.getArgs();
        if (args != null && args.length > 0) {
            message = StringUtils.format(message, args);
        }

        if (customMsg != null && !customMsg.isBlank()) {
            message = customMsg;
        }

        if ("0".equals(subCode)) {
            return R.ok(message);
        } else {
            return R.fail(message, subCode);
        }
    }

    @ExceptionHandler(DataIntegrityViolationException.class)
    public R<Void> handleMysqlDataTruncation(DataIntegrityViolationException e, HttpServletRequest request) {
        String requestURI = request.getRequestURI();
        Throwable cause = e.getCause();
        log.error("请求地址'{}', 数据库发生错误.", requestURI, e);
        if (cause != null) {
            String message = cause.getMessage();
            if (StrUtil.contains(message, "Data too long for column")) {
                return R.fail(MessageUtils.message("database.DataTooLong"));
            }
        }
        return R.fail(MessageUtils.message("database.unknownError"));
    }

    /**
     * 拦截未声明的异常
     */
    @ExceptionHandler(UndeclaredThrowableException.class)
    public R<Void> handleRuntimeException(UndeclaredThrowableException e, HttpServletRequest request) {
        String requestURI = request.getRequestURI();
        Throwable undeclaredThrowable = e.getUndeclaredThrowable();
        if (undeclaredThrowable instanceof RStatusCodeException) {
            RStatusCodeException re = (RStatusCodeException) undeclaredThrowable;
            String customMsg = re.getCustomMsg();
            RStatusCodeBase statusCode = re.getStatusCode();

            String subCode = statusCode.getSubCode();
            String message = statusCode.getMessage();
            Object[] args = statusCode.getArgs();
            if (args != null && args.length > 0) {
                message = StringUtils.format(message, args);
            }

            if (customMsg != null && !customMsg.isBlank()) {
                message = customMsg;
            }

            if ("0".equals(subCode)) {
                return R.ok(message);
            } else {
                return R.fail(message);
            }
        }

        log.error("请求地址'{}',发生未声明异常.", requestURI, e);
        return R.fail(e.getMessage());
    }
}
