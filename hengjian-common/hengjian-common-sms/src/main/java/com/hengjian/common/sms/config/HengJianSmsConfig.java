package com.hengjian.common.sms.config;

import com.hengjian.common.sms.config.properties.SmsProperties;
import com.hengjian.common.sms.utils.SmsRedisUtil;
import com.hengjian.common.sms.utils.SmsUtil;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;

/**
 * 短信配置类
 *
 * <AUTHOR> Li
 * @version 4.2.0
 */
@AutoConfiguration
@ConditionalOnProperty(value = "sms.enabled", havingValue = "true")
@EnableConfigurationProperties(SmsProperties.class)
public class HengJianSmsConfig {

    @Bean
    SmsUtil smsUtil(SmsProperties smsProperties) {
        return new SmsUtil(smsProperties);
    }

    @Bean
    SmsRedisUtil smsRedisUtil() { return new SmsRedisUtil(); }

}
