package com.hengjian.common.websocket.holder;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.handler.ConcurrentWebSocketSessionDecorator;

import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * WebSocketSession 用于保存当前所有在线的会话信息
 *
 * <AUTHOR>
 */
@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class WebSocketSessionHolder {

    private static final Map<Long, WebSocketSession> USER_SESSION_MAP = new ConcurrentHashMap<>();

    public static void addSession(Long sessionKey, WebSocketSession session) {
        // 把线程安全的session代理装饰类放到容器里
        USER_SESSION_MAP.put(sessionKey, new ConcurrentWebSocketSessionDecorator(session, 10 * 1000, 64000));
//        USER_SESSION_MAP.put(sessionKey, session);
    }

    public static void removeSession(Long sessionKey) {
        if (USER_SESSION_MAP.containsKey(sessionKey)) {
            WebSocketSession remove = USER_SESSION_MAP.remove(sessionKey);
            if(remove != null && remove.isOpen()) {
                try {
                    remove.close();
                } catch (IOException e) {
                    // 关闭出现异常
                    log.error("removeSession exception. {}", e.getMessage(), e);
                    e.printStackTrace();
                }
            }
        }
    }

    public static WebSocketSession getSessions(Long sessionKey) {
        return USER_SESSION_MAP.get(sessionKey);
    }

    public static Boolean existSession(Long sessionKey) {
        return USER_SESSION_MAP.containsKey(sessionKey);
    }
}
