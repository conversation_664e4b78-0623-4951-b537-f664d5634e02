package com.hengjian.common.websocket.listener;

import cn.hutool.core.collection.CollUtil;
import com.hengjian.common.websocket.holder.WebSocketSessionHolder;
import com.hengjian.common.websocket.utils.WebSocketUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.Ordered;
import org.springframework.web.socket.WebSocketSession;

/**
 * WebSocket 主题订阅监听器
 *
 * <AUTHOR>
 */
@Slf4j
public class WebSocketTopicListener implements ApplicationRunner, Ordered {

    @Override
    public void run(ApplicationArguments args) throws Exception {
        WebSocketUtils.subscribeMessage((message) -> {
            log.info("WebSocket主题订阅收到消息session keys={}  message={}！", message.getSessionKeys(), message.getMessage());
            if (CollUtil.isNotEmpty(message.getSessionKeys())) {
                message.getSessionKeys().forEach(key -> {
                    if (WebSocketSessionHolder.existSession(key)) {
                        WebSocketSession session = WebSocketSessionHolder.getSessions(key);
                        if (session == null || !session.isOpen()) {
                            log.error("[send] session会话已经关闭");
                            WebSocketSessionHolder.removeSession(key);
                        } else {
                            WebSocketUtils.sendMessage(key, message.getMessage());
                        }
                    }
                });
            }
        });
        log.info("初始化WebSocket主题订阅监听器成功");
    }

    @Override
    public int getOrder() {
        return -1;
    }
}
