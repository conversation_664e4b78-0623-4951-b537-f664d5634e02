package com.hengjian.common.websocket.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 消息的dto
 *
 * <AUTHOR>
 */
@Data
public class WebSocketMessageDto implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     * 需要推送到的session key 列表
     */
    private List<Long> sessionKeys;

    /**
     * 需要发送的消息
     */
    private String message;

    /**
     * 消息类型
     */
    private String messageType;

    /**
     * 消息Id
     */
    private String messageId;
}
